export interface ExportDataColumn {
  columnId: string;
  columnNameKey: string;
  columnDataType: 'string' | 'number' | 'date';
  defaultOn: boolean;
  minWidth?: string;
  maxWidth?: string;
}

export const EXPORT_DATA_COLUMNS: Record<string, ExportDataColumn[]> = {
  reviews: [
    {
      columnId: 'dateOfReview',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.DATE_OF_REVIEW',
      columnDataType: 'date',
      defaultOn: true,
    },
    {
      columnId: 'accountGroupId',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.BUSINESS_ID',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'companyName',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.COMPANY_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'address',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'city',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'state',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.STATE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'zip',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'source',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.SOURCE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'reviewerName',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.REVIEWER_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'reviewRating',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.REVIEW_RATING',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'reviewText',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.REVIEW_TEXT',
      columnDataType: 'string',
      defaultOn: true,
      minWidth: '500px',
      maxWidth: '500px',
    },
    {
      columnId: 'reviewUrl',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.REVIEW_URL',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'responseStatus',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.RESPONSE_STATUS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'response',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.RESPONSE',
      columnDataType: 'string',
      defaultOn: true,
    },
  ],
  gmb: [
    {
      columnId: 'accountGroupId',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.BUSINESS_ID',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'companyName',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.COMPANY_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'address',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'city',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'state',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.STATE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'zip',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'actions_driving_directions',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECTIONS_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'actions_phone',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.PHONE_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'actions_website',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.WEBSITE_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'queries_direct',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECT_SEARCH',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'queries_indirect',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DISCOVERY_SEARCH',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'views_maps',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.MAP_VIEW',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'views_search',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCH_VIEW',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_actions',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_queries',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_SEARCHES',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_views',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
      columnDataType: 'number',
      defaultOn: true,
    },
  ],
  'gmb-breakdown': [
    {
      columnId: 'dateOfData',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.GMB.DATE_OF_DATA',
      columnDataType: 'date',
      defaultOn: true,
    },
    {
      columnId: 'accountGroupId',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.BUSINESS_ID',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'companyName',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.COMPANY_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'address',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'city',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'state',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.STATE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'zip',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'actions_driving_directions',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECTIONS_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'actions_phone',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.PHONE_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'actions_website',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.WEBSITE_ACTION',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'queries_direct',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DIRECT_SEARCH',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'queries_indirect',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.DISCOVERY_SEARCH',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'views_maps',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.MAP_VIEW',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'views_search',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCH_VIEW',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_actions',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_queries',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_SEARCHES',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total_views',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
      columnDataType: 'number',
      defaultOn: true,
    },
  ],
  reviews_overview: [
    {
      columnId: 'accountGroupId',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.BUSINESS_ID',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'companyName',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.COMPANY_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'address',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'city',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'state',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.STATE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'zip',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'source',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.SOURCE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'rating',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.REVIEW.REVIEW_RATING',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'total',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.TOTAL_REVIEWS_TAB',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'responded',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONDED_TO',
      columnDataType: 'number',
      defaultOn: true,
    },
    {
      columnId: 'responseRate',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONSE_RATE',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'oneStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.1_STAR',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'twoStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.2_STARS',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'threeStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.3_STARS',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'fourStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.4_STARS',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'fiveStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.5_STARS',
      columnDataType: 'number',
      defaultOn: false,
    },
    {
      columnId: 'noStar',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.NO_RATING_REVIEW_SERIES',
      columnDataType: 'number',
      defaultOn: false,
    },
  ],
  listing_data: [
    {
      columnId: 'accountGroupId',
      columnNameKey: 'MULTI_LOCATION.DATA_EXPORTER.TABLE_COLUMNS.SHARED.BUSINESS_ID',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'sourceName',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.SOURCE_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'sourceUrl',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.SOURCE_URL',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'accuracy',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.ACCURACY_TAB',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedCompanyName',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedAddress',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedCity',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedProvince',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_PROVINCE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedZip',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedPhoneNumber',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_PHONE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'expectedWebsiteLink',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.EXPECTED_LINK',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentCompanyName',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_NAME',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentAddress',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_ADDRESS',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentCity',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_CITY',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentProvince',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_PROVINCE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentZip',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_ZIP',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentPhoneNumber',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_PHONE',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'currentWebsiteLink',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.CURRENT_LISTING_LINK',
      columnDataType: 'string',
      defaultOn: true,
    },
    {
      columnId: 'lastUpdate',
      columnNameKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.LAST_UPDATE',
      columnDataType: 'date',
      defaultOn: true,
    },
  ],
};
