import { Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Review, ReviewActionStatus } from '@vendasta/reputation';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import LocalizedFormat from 'dayjs/plugin/localizedFormat';
import { BehaviorSubject, Observable, Subject, combineLatest, firstValueFrom, of } from 'rxjs';
import { debounceTime, filter, map, shareReplay, switchMap, take, takeUntil } from 'rxjs/operators';
import { AccountGroup } from '../../account-group';
import { CsvExporterService } from '../../core/csv-exporter.service';
import { Location, LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { GmbService } from '../../metrics/gmb.service';
import { ReviewsService } from '../../metrics/reviews.service';
import { ReputationService } from '../../reputation';
import { TimeAgoPipe } from '../../shared/time-ago.pipe';
import { AccountGroupCacheService } from '../account-group-cache.service';
import { BrandManageListingsService } from '../brand-manage-listings/brand-manage-listings.service';
import { STATUS, Source } from '../brand-manage-listings/source';
import { Iter, QueryReviewsService, ReviewsWithPublishSettings } from '../brand-manage-reviews/query-reviews.service';
import { BRAND_DATA_SOURCES, SOURCE_INDEXES } from './export-options/export-data-sources';
import { CSVHeader, ExportService } from '@vendasta/business-center';

dayjs.extend(LocalizedFormat);
dayjs.extend(advancedFormat);

@Injectable({ providedIn: 'root' })
export class BrandDataExportService {
  reviews$$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  reviews$: Observable<any[]> = this.reviews$$.asObservable();
  iter$: Observable<Iter<ReviewsWithPublishSettings>>;

  gmbData$$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  gmbData$: Observable<any[]> = this.gmbData$$.asObservable();

  reviewsOverview$: Observable<any[]>;
  reviewsOverviewPreview$: Observable<any[]>;

  loading$: Observable<boolean>;
  hasMore$: Observable<boolean>;

  initialLoading: boolean;
  killSubscriptions$$: Subject<any> = new Subject();

  constructor(
    private queryReviewsService: QueryReviewsService,
    private locationsService: LocationsService,
    private accountGroupCacheService: AccountGroupCacheService,
    private accountGroupService: AccountGroupMetricService,
    private csvExporterService: CsvExporterService,
    private gmbService: GmbService,
    public brandManageListingService: BrandManageListingsService,
    private reviewsService: ReviewsService,
    private reputationService: ReputationService,
    private exportService: ExportService,
  ) {
    this.reviewsOverview$ = this.reviewsOverview(false);
    this.reviewsOverviewPreview$ = this.reviewsOverview(true);
    this.iter$ = this.queryReviewsService.iter$;
    this.iter$
      .pipe(
        switchMap(() => {
          this.reviews$$.next([]);
          return this.loadReviewsDataPage();
        }),
        takeUntilDestroyed(),
      )
      .subscribe();
    this.loading$ = this.iter$.pipe(switchMap((iter) => iter.loading$));
    this.hasMore$ = this.iter$.pipe(switchMap((iter) => iter.hasMore$));
  }

  loadDataSource(dataSource: string, limitData = true): void {
    // Kill all open subscriptions when changing subscriptions
    this.killSubscriptions$$.next('die');

    switch (dataSource) {
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS].sourceId:
        this.queryReviewsService.setPageSize(10);
        this.queryReviewsService.setIsAllTimeRange(false);
        break;
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.GMB_TOTAL].sourceId:
        this.loadGMBData(limitData)
          .pipe(takeUntil(this.killSubscriptions$$))
          .subscribe((data) => this.gmbData$$.next(data));
        break;
      default:
        console.warn('dataSource is unknown at BrandDataExportService', dataSource);
    }
  }

  loadReviewsDownloadIterator(): void {
    this.queryReviewsService.setPageSize(500);
    this.queryReviewsService.setIsAllTimeRange(false);
  }

  clearReview(): void {
    this.reviews$$.next([]);
  }

  loadReviewsDataPage(): Observable<any> {
    return this.iter$.pipe(
      debounceTime(10),
      take(1),
      switchMap((iter) => iter.next()),
      map((reviewsWithPublishSettings: ReviewsWithPublishSettings) => {
        if (reviewsWithPublishSettings === null) {
          return null;
        }
        return reviewsWithPublishSettings.reviews;
      }),
      switchMap((reviews: Review[]) => {
        if (!reviews) {
          return combineLatest([of(null), of(null), of(null)]);
        }
        return combineLatest([
          of(reviews),
          this.locationsService.currentLocation$,
          this.accountGroupCacheService.getMulti(reviews.map((rev) => rev.reviewId.accountGroupId)),
        ]);
      }),
      map(([reviews, location, accountGroups]: [Review[], Location, any]) => {
        if (!reviews) {
          this.reviews$$.next([]);
          return null;
        }
        const reviewRows = reviews.map((review) =>
          this.convertReviewToRow(review, location, accountGroups[review.reviewId.accountGroupId]),
        );
        this.reviews$$.next(reviewRows);
        return reviewRows;
      }),
      take(1),
    );
  }

  loadGMBData(limitData = true): Observable<
    {
      accountGroupId: string;
      companyName: string;
      address: string;
      city: string;
      state: string;
      zip: string;
      [key: string]: string;
    }[]
  > {
    // Build measure value map for reviews
    const insightsMeasureMap = combineLatest([
      this.locationsService.currentAccountGroupIds$,
      this.gmbService.currentInsightsByLocation$,
    ]).pipe(
      filter(([accountGroupIds, insightStats]) => accountGroupIds !== null && insightStats !== null),
      map(([accountGroupIds, insightStats]) => {
        const locationMeasureMap = {};
        if (accountGroupIds == null || insightStats == null) {
          return locationMeasureMap;
        }

        accountGroupIds.forEach((accountGroupId) => {
          // Default value all locations
          locationMeasureMap[accountGroupId] = {
            total_views: 'N/A',
            total_actions: 'N/A',
            views_maps: 'N/A',
            views_search: 'N/A',
            actions_website: 'N/A',
            actions_phone: 'N/A',
            actions_driving_directions: 'N/A',
            actions_bookings: 'N/A',
            actions_conversations: 'N/A',
            actions_food_orders: 'N/A',
          };
        });
        insightStats.forEach((insightStat) => {
          // Set value for all locations in stats
          locationMeasureMap[insightStat.dimension] = {
            total_views: insightStat.stat.totalViews(),
            total_actions: insightStat.stat.totalActions(),
            views_maps: insightStat.stat.views_maps,
            views_search: insightStat.stat.views_search,
            actions_website: insightStat.stat.actions_website,
            actions_phone: insightStat.stat.actions_phone,
            actions_driving_directions: insightStat.stat.actions_driving_directions,
            actions_bookings: insightStat.stat.actions_bookings,
            actions_conversations: insightStat.stat.actions_conversations,
            actions_food_orders: insightStat.stat.actions_food_orders,
          };
        });

        return locationMeasureMap;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // Progressively loaded data for map/table. Location data, then measures/grades
    return combineLatest([this.accountGroupService.filteredLocationsForPath$, insightsMeasureMap]).pipe(
      filter(([locations, insightsMeasures]) => locations !== null && insightsMeasures !== null),
      map(([locations, insightsMeasures]) => {
        if (locations == null) {
          return null;
        }
        let keys = Object.keys(locations);
        if (limitData) {
          keys = keys.slice(0, 10);
        }

        return keys
          .map((k) => locations[k])
          .map((ag: AccountGroup) => {
            return {
              accountGroupId: ag.accountGroupId,
              companyName: ag.companyName,
              address: ag.address,
              city: ag.city,
              state: ag.state,
              zip: ag.zip,
              ...(insightsMeasures || {})[ag.accountGroupId],
            };
          });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  // Maps the review data to the style of data the table is expecting
  private convertReviewToRow(
    review: Review,
    location: Location,
    accountGroup: AccountGroup,
  ): {
    dateOfReview: string;
    companyName: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    source: string;
    reviewerName: string;
    reviewRating: string;
    reviewText: string;
    reviewUrl: string;
    accountGroupId: string;
    responseStatus: any;
    response: any;
  } {
    return {
      dateOfReview: dayjs(review.published).format('MMM Do, YYYY'),
      companyName: accountGroup.companyName,
      address: accountGroup.address,
      city: accountGroup.city,
      state: accountGroup.state,
      zip: accountGroup.zip,
      source: review.domain,
      reviewerName: review.reviewer?.name || '',
      reviewRating: review.rating,
      reviewText: review.content,
      reviewUrl: review.url,
      accountGroupId: review.reviewId.accountGroupId,
      responseStatus: this.getResponseStatus(review),
      response: this.getResponseFromReview(review),
    };
  }

  private getResponseFromReview(review: Review): string {
    if (
      review.actionStatus === ReviewActionStatus.REVIEW_ACTION_STATUS_OWNER_RESPONDED ||
      review.actionStatus === ReviewActionStatus.REVIEW_ACTION_STATUS_DIGITAL_AGENT_RESPONDED
    ) {
      if (!review.comments || review.comments.length === 0) {
        return '';
      }
      return review.comments[0].text;
    }
    return '';
  }

  private getResponseStatus(review: Review): string {
    switch (review.actionStatus) {
      case undefined:
      case ReviewActionStatus.REVIEW_ACTION_STATUS_ACTION_REQUIRED:
      case ReviewActionStatus.REVIEW_ACTION_STATUS_NO_ACTION_REQUIRED:
        return 'Not Responded';
      case ReviewActionStatus.REVIEW_ACTION_STATUS_RESPONSE_AWAITING_APPROVAL:
        return 'Reponse Waiting Approval';
      case ReviewActionStatus.REVIEW_ACTION_STATUS_OWNER_RESPONDED:
      case ReviewActionStatus.REVIEW_ACTION_STATUS_DIGITAL_AGENT_RESPONDED:
        return 'Responded';
    }
  }

  exportToCSV(titleRows: string[], dataRows: string[][]): void {
    // Clear any previous data
    this.csvExporterService.clearRows();

    // Add the data to the service
    this.csvExporterService.setTitleRow(titleRows);
    dataRows.forEach((dataRow) => {
      return this.csvExporterService.addDataRow(dataRow);
    });

    // Export File
    this.csvExporterService.downloadFile(this.csvName());
  }

  async exportReviewDataToCSV(headers: CSVHeader[]): Promise<void> {
    const query = await this.queryReviewsService.getExportReviewRequestQuery();
    await this.csvExporterService.downloadContent(
      this.csvName(),
      async () =>
        await firstValueFrom(
          this.exportService.exportReviews({
            headers: headers,
            query: query,
          }),
        ),
    );
  }

  setTitleRowToCSV(titleRows: string[]): void {
    // Clear any previous data
    this.csvExporterService.clearRows();

    this.csvExporterService.setTitleRow(titleRows);
  }

  addDataRowToCSV(dataRows: string[][]): void {
    // Add the data to the service
    dataRows.forEach((dataRow) => {
      return this.csvExporterService.addDataRow(dataRow);
    });
  }

  exportCSV(): void {
    // Export File
    const now = dayjs().format('L');
    this.csvExporterService.downloadFile(`export-${now}.csv`);
  }

  loadListingDataFromManagement(displayList: boolean): Observable<any> {
    if (displayList && !this.brandManageListingService.dataExporter) {
      this.brandManageListingService.updateListingSources(true, 20);
    } else if (displayList && this.brandManageListingService.dataExporter) {
      this.brandManageListingService.killSubscriptions();
      this.brandManageListingService.resetSources();
      this.brandManageListingService.updateListingSources(true, 20, this.brandManageListingService.fil);
      this.brandManageListingService.dataExporter = false;
    } else {
      if (!this.initialLoading) {
        this.brandManageListingService.changePage(true, true, 500);
      } else {
        this.initialLoading = false;
      }
    }
    return this.brandManageListingService.sources$.pipe(
      map((source) => {
        return source.map((list) => {
          const accuracy = this.getSourceStatusLabel(list);
          if (list.listings[0]) {
            const listDataCurrent = list.listings[0].anchorData;
            const listDataExact = list.listings[0].correctAnchorData;
            const lastUpdate = new TimeAgoPipe();
            return {
              accountGroupId: list.accountGroupId,
              sourceName: list.source.name,
              sourceUrl: list.listings[0].permalink,
              accuracy: accuracy,
              expectedCompanyName: listDataExact.businessName ? listDataExact.businessName : 'N/A',
              expectedAddress: listDataExact.address ? listDataExact.address : 'N/A',
              expectedCity: listDataExact.city ? listDataExact.city : 'N/A',
              expectedProvince: listDataExact.provState ? listDataExact.provState : 'N/A',
              expectedZip: listDataExact.postalZip ? listDataExact.postalZip : 'N/A',
              expectedPhoneNumber: listDataExact.phoneNumber ? listDataExact.phoneNumber : 'N/A',
              expectedWebsiteLink: listDataExact.website ? listDataExact.website : 'N/A',
              currentCompanyName: listDataCurrent.companyName ? listDataCurrent.companyName : 'N/A',
              currentAddress: listDataCurrent.address ? listDataCurrent.address : 'N/A',
              currentCity: listDataCurrent.city ? listDataCurrent.city : 'N/A',
              currentProvince: listDataCurrent.state ? listDataCurrent.state : 'N/A',
              currentZip: listDataCurrent.zip ? listDataCurrent.zip : 'N/A',
              currentPhoneNumber: listDataCurrent.phone ? listDataCurrent.phone : 'N/A',
              currentWebsiteLink: listDataExact.website ? listDataExact.website : 'N/A',
              lastUpdate: list.listings[0].lastUpdated
                ? 'last updated ' + lastUpdate.transform(list.listings[0].lastUpdated)
                : 'N/A',
            };
          } else {
            return {
              accountGroupId: list.accountGroupId,
              sourceName: list.source.name,
              sourceUrl: 'N/A',
              accuracy: accuracy,
              expectedCompanyName: list.source.correctAnchorData?.businessName
                ? list.source.correctAnchorData.businessName
                : 'N/A',
              expectedAddress: list.source.correctAnchorData?.address ? list.source.correctAnchorData.address : 'N/A',
              expectedCity: list.source.correctAnchorData?.city ? list.source.correctAnchorData?.city : 'N/A',
              expectedProvince: list.source.correctAnchorData?.provState
                ? list.source.correctAnchorData?.provState
                : 'N/A',
              expectedZip: list.source.correctAnchorData?.postalZip ? list.source.correctAnchorData?.postalZip : 'N/A',
              expectedPhoneNumber: list.source.correctAnchorData?.phoneNumber
                ? list.source.correctAnchorData.phoneNumber
                : 'N/A',
              expectedWebsiteLink: list.source.correctAnchorData?.website
                ? list.source.correctAnchorData.website
                : 'N/A',
              currentCompanyName: 'N/A',
              currentAddress: 'N/A',
              currentCity: 'N/A',
              currentProvince: 'N/A',
              currentZip: 'N/A',
              currentPhoneNumber: 'N/A',
              currentWebsiteLink: 'N/A',
              lastUpdate: 'N/A',
            };
          }
        });
      }),
    );
  }

  reviewsOverview(preview: boolean): Observable<any> {
    return combineLatest([
      preview ? this.reviewsService.currentByLocationSourcePreview$ : this.reviewsService.currentByLocationSource$,
      this.accountGroupService.filteredLocationsForPath$,
      this.reputationService.brandSourcesFull$,
    ]).pipe(
      filter(([data, accountGroups, sources]) => !!data && !!accountGroups && !!sources),
      map(([data, accountGroups, sources]) => {
        return data
          .map((row) => {
            const [agId, sourceId] = row.dimension.split('|');
            const ag = accountGroups[agId];
            return {
              accountGroupId: agId,
              companyName: ag.companyName,
              address: ag.address,
              city: ag.city,
              state: ag.state,
              zip: ag.zip,
              source: sources.get(sourceId).name,
              rating: row.stat.averageReviewScore(),
              total: row.stat.totalReviews(),
              oneStar: row.stat.numberOneStars,
              twoStar: row.stat.numberTwoStars,
              threeStar: row.stat.numberThreeStars,
              fourStar: row.stat.numberFourStars,
              fiveStar: row.stat.numberFiveStars,
              noStar: row.stat.numberNoStars,
              responded: row.stat.numberResponded,
              unresponded: row.stat.numberUnresponded,
              responseRate: row.stat.numberResponded / row.stat.totalReviews(),
            };
          })
          .sort((a, b) => {
            if (a.accountGroupId === b.accountGroupId) {
              return a.source > b.source ? 1 : -1;
            }
            return a.accountGroupId > b.accountGroupId ? 1 : -1;
          });
      }),
    );
  }

  getSourceStatusLabel(list: Source): string {
    if (!list) {
      return 'Not found';
    }

    switch (list.status()) {
      case STATUS.LISTINGS_NOT_FOUND:
        return 'Not found';
      case STATUS.LISTINGS_FAILED:
        return 'Error';
      case STATUS.LISTINGS_PROCESSING:
        return 'Processing';
      case STATUS.LISTINGS_FOUND_INACCURATE:
        return 'Inaccurate';
      default:
        return 'Accurate';
    }
  }

  cleanSource(): void {
    this.brandManageListingService.resetSources();
    this.brandManageListingService.killSubscriptions();
  }

  private csvName(): string {
    return `export-${dayjs().format('L')}.csv`;
  }
}
