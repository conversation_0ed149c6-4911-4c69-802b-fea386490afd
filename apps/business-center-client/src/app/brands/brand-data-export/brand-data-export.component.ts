import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatPaginator } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  map,
  Observable,
  Subscription,
  switchMap,
  take,
  takeWhile,
  tap,
} from 'rxjs';
import { AppConfigService } from '../../app-config.service';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { NavigationService } from '../../navigation/navigation.service';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { PageAccessService, PageId } from '../../page-access';
import { BrandFilterContainerService } from '../brand-filter-container/brand-filter-container.service';
import { ManageListingsFiltersComponent } from '../brand-manage-listings/manage-listings-filters/manage-listings-filters.component';
import { ManageReviewsFiltersComponent } from '../brand-manage-reviews/manage-reviews/manage-reviews-filters/manage-reviews-filters.component';
import { BrandDataExportService } from './brand-data-export.service';
import { EXPORT_DATA_COLUMNS, ExportDataColumn } from './export-options/export-data-columns';
import { BRAND_DATA_SOURCES, BrandDataSource, SOURCE_INDEXES } from './export-options/export-data-sources';
import { ACCOUNT_GROUP_TOKEN } from '../../../tokens';

@Component({
  selector: 'bc-brand-data-export',
  templateUrl: './brand-data-export.component.html',
  styleUrls: ['./brand-data-export.component.scss'],
  standalone: false,
})
export class BrandDataExportComponent implements OnInit, OnDestroy {
  availableDataSources = BRAND_DATA_SOURCES;
  dataSources$: Observable<BrandDataSource[]>;
  availableColumns: ExportDataColumn[] = [];
  selectedColumns: ExportDataColumn[] = [];
  columnsToDisplay: string[] = [];

  batchSize = 500;
  selectedSource: string;

  currentCSVCount$$: BehaviorSubject<number> = new BehaviorSubject<number>(null);
  currentCSVCount$: Observable<number> = this.currentCSVCount$$.asObservable();
  totalRowCount$$: BehaviorSubject<number> = new BehaviorSubject<number>(null);
  totalRowCount$: Observable<number> = this.totalRowCount$$.asObservable();
  totalDataNum$ = this.brandDataExportService.brandManageListingService.totalCount$;
  currentProgress = 0;
  targetProgress = 0;
  currentFileCount = 0;
  targetFileCount = 0;
  progressIntervalId = 0;
  fileCountProgressId = 0;
  private hasMore$$ = new BehaviorSubject(true);
  hasMore$ = this.hasMore$$.asObservable();

  exportingCSV$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  exportingCSV$: Observable<boolean> = this.exportingCSV$$.asObservable();

  listingLoading$ = this.brandDataExportService.loading$;
  selectedDataSource$: Observable<any>;

  countListener$: any;

  excludesEmpty = false;

  private accountGroup = inject(ACCOUNT_GROUP_TOKEN);
  protected previousPageUrl$ = this.accountGroup.pipe(
    map((accountGroup) => `/account/location/${accountGroup.accountGroupId}/settings`),
  );

  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

  constructor(
    private brandDataExportService: BrandDataExportService,
    public sidepanelService: SidepanelService,
    private nav: NavigationService,
    private accountGroupMetricService: AccountGroupMetricService,
    private translateService: TranslateService,
    private pageVisiblityService: PageAccessService,
    private brandFiltersService: BrandFilterContainerService,
    private appConfigService: AppConfigService,
    private readonly destroyRef: DestroyRef,
  ) {}

  ngOnInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.OVER, null);
    this.nav.setBreadcrumbs([{ text: 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT' }]);

    // Make sure the filter bar is visible
    this.brandFiltersService.setMinimizedStatus(false);

    this.dataSources$ = combineLatest([
      this.pageVisiblityService.isPageAccessible$(PageId.manage_multi_location_reviews),
      this.pageVisiblityService.isPageAccessible$(PageId.multi_location_google_my_business),
      this.pageVisiblityService.isPageAccessible$(PageId.multi_location_listings),
      this.appConfigService.legacyConfig$.pipe(map((config) => config.allowExportReviews)),
    ]).pipe(
      map(([reviewVisible, gmbVisbile, listingVisible, reviewConfig]) => {
        const dataSource = [];
        if (reviewVisible && reviewConfig) dataSource.push(this.availableDataSources[SOURCE_INDEXES.REVIEWS]);
        if (reviewVisible) {
          dataSource.push(this.availableDataSources[SOURCE_INDEXES.REVIEWS_OVERVIEW]);
        }
        if (gmbVisbile) {
          dataSource.push(this.availableDataSources[SOURCE_INDEXES.GMB_TOTAL]);
        }
        if (listingVisible) {
          dataSource.push(this.availableDataSources[SOURCE_INDEXES.LISTING_DATA]);
        }
        return dataSource;
      }),
    );

    // Listens for changes to the total rows downloaded count and the total csv rows available
    combineLatest([this.totalRowCount$, this.currentCSVCount$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([totalRows, currentCSVCount]) => {
        this.targetProgress = Math.round((currentCSVCount / totalRows) * 100) || 0;
      });

    // if its redirect to the data exporter page from other page
    if (this.brandDataExportService.brandManageListingService.dataExporter) {
      this.selectColumnsToDisplay('listing_data');
      this.brandDataExportService.brandManageListingService.dataExporter = false;
    }
  }

  ngOnDestroy(): void {
    // Removes the ability for the screen size to be bigger than 1200px
    this.brandDataExportService.killSubscriptions$$.next('die');
    this.brandDataExportService.cleanSource();
    this.sidepanelService.close();
  }

  selectColumnsToDisplay(selectedSource: string): void {
    this.selectedSource = selectedSource;
    this.brandDataExportService.loadDataSource(selectedSource);
    this.setDataSource();
    this.availableColumns = [...EXPORT_DATA_COLUMNS[selectedSource]];
    this.selectedColumns = [...EXPORT_DATA_COLUMNS[selectedSource]].filter((col) => col.defaultOn);
    this.columnsToDisplay = this.selectedColumns.map((col) => col.columnId);
  }

  private setDataSource(): void {
    this.sidepanelService.close();

    switch (this.selectedSource) {
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS].sourceId:
        this.selectedDataSource$ = this.brandDataExportService.reviews$;
        this.sidepanelService.setViewThenOpen(Size.REGULAR, Mode.SIDE, ManageReviewsFiltersComponent);
        break;
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.GMB_TOTAL].sourceId:
        this.selectedDataSource$ = this.brandDataExportService.gmbData$;
        this.sidepanelService.setView(Size.REGULAR, Mode.OVER, null);
        break;
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS_OVERVIEW].sourceId:
        this.selectedDataSource$ = this.brandDataExportService.reviewsOverviewPreview$;
        break;
      case BRAND_DATA_SOURCES[SOURCE_INDEXES.LISTING_DATA].sourceId:
        this.listingLoading$ = this.brandDataExportService.brandManageListingService.loading$;
        this.selectedDataSource$ = this.brandDataExportService.loadListingDataFromManagement(true);
        this.sidepanelService.setViewThenOpen(Size.REGULAR, Mode.SIDE, ManageListingsFiltersComponent, true);
        break;
      default:
        console.warn('this.selectedSource is unknown at BrandDataExportComponent', this.selectedSource);
    }
    this.excludesEmpty = this.selectedSource === BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS_OVERVIEW].sourceId;
  }

  toggleSelectedColumn(checked: boolean, columnId: string): void {
    if (!checked) {
      this.columnsToDisplay = this.columnsToDisplay.filter((col) => col !== columnId);
      this.selectedColumns = this.selectedColumns.filter((col) => col.columnId !== columnId);
    } else {
      if (!this.selectedColumns.find((col) => col.columnId === columnId)) {
        this.selectedColumns.push(
          ...(EXPORT_DATA_COLUMNS[this.selectedSource] || []).filter((col) => col.columnId === columnId),
        );
      }
      if (this.columnsToDisplay.indexOf(columnId) === -1) {
        this.columnsToDisplay.push(columnId);
      }
      // Order the columns with the current available columns list
      this.columnsToDisplay = this.availableColumns
        .filter((col) => !!this.columnsToDisplay.find((displayCol) => displayCol === col.columnId))
        .map((col) => col.columnId);
    }
  }

  // When the columns are dragged and dropped
  dropListDropped(event: CdkDragDrop<any, any>, index: number): void {
    moveItemInArray(this.selectedColumns, event.item.data.columnIndex, index);
    moveItemInArray(this.columnsToDisplay, event.item.data.columnIndex, index);
    moveItemInArray(this.availableColumns, event.item.data.columnIndex, index);
  }

  // When the menu items are dragged and dropped (Columns button clicked)
  menuDropped(event: any): void {
    moveItemInArray(this.selectedColumns, event.item.data.columnIndex, event.currentIndex);
    moveItemInArray(this.availableColumns, event.item.data.columnIndex, event.currentIndex);
    this.columnsToDisplay = this.availableColumns
      .filter((col) => !!this.columnsToDisplay.find((displayCol) => displayCol === col.columnId))
      .map((col) => col.columnId);
  }

  downloadCSV(): void {
    this.resetDownloadSettings();
    this.resetDownloadIntervals();

    window.requestAnimationFrame(async () => {
      if (this.selectedSource === BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS].sourceId) {
        this.totalRowCount$$.next(0); // the progress will be unknown so we don't need this
        const headers = this.selectedColumns.map((col) => ({
          headerId: toSnakeCase(col.columnId),
          headerTranslation: this.translateService.instant(col.columnNameKey),
        }));
        await this.brandDataExportService.exportReviewDataToCSV(headers);
        this.exportingCSV$$.next(false);
      } else if (this.selectedSource === BRAND_DATA_SOURCES[SOURCE_INDEXES.GMB_TOTAL].sourceId) {
        this.countListener$ = this.accountGroupMetricService.filteredAccountGroupIdsForPath$
          .pipe(
            map((r) => {
              if (!r || !r.size) {
                this.totalRowCount$$.next(0);
                return;
              }
              this.totalRowCount$$.next(r.size);
            }),
            take(1),
          )
          .subscribe();

        this.brandDataExportService
          .loadGMBData(false)
          .pipe(
            take(1),
            tap((dataSource) => {
              this.currentCSVCount$$.next(dataSource.length);
              this.brandDataExportService.exportToCSV(
                this.selectedColumns.map((col) => this.translateService.instant(col.columnNameKey)),
                dataSource.map((data) => this.dataSourceToSelectedColumnArray(data)),
              );
              this.exportingCSV$$.next(false);
              this.clearIntervalsAfterDownload();
            }),
          )
          .subscribe();
      } else if (this.selectedSource === BRAND_DATA_SOURCES[SOURCE_INDEXES.REVIEWS_OVERVIEW].sourceId) {
        this.brandDataExportService.setTitleRowToCSV(
          this.selectedColumns.map((col) => this.translateService.instant(col.columnNameKey)),
        );
        this.hasMore$$.next(false);
        this.brandDataExportService.reviewsOverview$.pipe(take(1)).subscribe((reviews) => {
          this.totalRowCount$$.next(reviews.length);
          this.brandDataExportService.addDataRowToCSV(
            reviews.map((data) => this.dataSourceToSelectedColumnArray(data)),
          );
          this.brandDataExportService.exportCSV();
          this.exportingCSV$$.next(false);
          this.currentCSVCount$$.next(undefined);
        });
      } else if (this.selectedSource === BRAND_DATA_SOURCES[SOURCE_INDEXES.LISTING_DATA].sourceId) {
        this.brandDataExportService.initialLoading = true;
        // Load the iter with a 500 listing data limit
        this.brandDataExportService.setTitleRowToCSV(
          this.selectedColumns.map((col) => this.translateService.instant(col.columnNameKey)),
        );
        this.totalDataNum$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((num) => this.totalRowCount$$.next(num));

        const reviewAddingSub = this.brandDataExportService
          .loadListingDataFromManagement(false)
          .pipe(
            tap((reviews) => {
              this.brandDataExportService.addDataRowToCSV(
                reviews.map((data) => this.dataSourceToSelectedColumnArray(data)),
              );
            }),
            switchMap(() => this.hasMore$),
            takeWhile((hasMore) => !!hasMore),
          )
          .subscribe({
            complete: () => {
              this.finalizeReviewCSVDownload(reviewAddingSub);
            },
          });

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const combineLatestSub = combineLatest([
          this.brandDataExportService.brandManageListingService.iter.hasMore(),
          this.brandDataExportService.brandManageListingService.iter.loading(),
        ])
          .pipe(
            filter(([_, loading]) => !loading),
            takeWhile(([hasMore]) => hasMore),
            tap(() => {
              this.brandDataExportService.loadListingDataFromManagement(false);
              if (this.targetFileCount + this.batchSize >= this.totalRowCount$$.getValue()) {
                const value = this.totalRowCount$$.getValue() - this.currentCSVCount$$.getValue();
                this.targetFileCount = this.currentCSVCount$$.getValue() + value;
              } else {
                this.currentCSVCount$$.next(this.currentCSVCount$$.getValue() + this.batchSize);
                this.targetFileCount = this.currentCSVCount$$.getValue() + this.batchSize;
              }
            }),
          )
          .subscribe({
            complete: () => {
              this.hasMore$$.next(false);
            },
          });
        if (this.totalRowCount$$.getValue() < 20) {
          this.finalizeReviewCSVDownload(reviewAddingSub);
        }
      }
    });
  }

  private resetDownloadSettings(): void {
    this.exportingCSV$$.next(true);
    this.currentCSVCount$$.next(0);
    this.currentProgress = 0;
    this.targetProgress = 0;
    this.hasMore$$.next(true);

    if (this.countListener$) {
      this.countListener$.unsubscribe();
      this.countListener$ = undefined;
    }
  }

  private resetDownloadIntervals(): void {
    // if progressIntervalId is set, fileCountProgressId must also be set so clear both
    if (this.progressIntervalId) {
      window.clearInterval(this.progressIntervalId);
      window.clearInterval(this.fileCountProgressId);
      this.progressIntervalId = null;
    }

    // Changes the current progress bar in a more animated fashion
    this.progressIntervalId = window.setInterval(() => {
      const currentChange = this.targetProgress - this.currentProgress;
      if (currentChange > 0 && currentChange <= 3) {
        this.currentProgress += 0.5;
      } else if (currentChange > 0 && currentChange > 3) {
        this.currentProgress += 1;
      }
    }, 250);

    // Counts the files downloaded count
    this.fileCountProgressId = window.setInterval(() => {
      const currentChange = this.targetFileCount - this.currentFileCount;
      if (currentChange > 0 && currentChange <= 200) {
        this.currentFileCount += 1;
      } else if (currentChange > 200 && currentChange <= 300) {
        this.currentFileCount += 5;
      } else if (currentChange > 300 && currentChange <= 500) {
        this.currentFileCount += 10;
      } else if (currentChange > 500) {
        this.currentFileCount += 25;
      }
    }, 10);
  }

  private finalizeReviewCSVDownload(reviewAddingSub: Subscription): void {
    reviewAddingSub.unsubscribe();
    this.brandDataExportService.exportCSV();
    this.exportingCSV$$.next(false);
    this.currentCSVCount$$.next(undefined);
    this.totalRowCount$$.next(undefined);
    this.targetFileCount = 0;

    // Load the review iter with a 10 review limit
    this.brandDataExportService.loadDataSource(this.selectedSource);
    this.clearIntervalsAfterDownload();

    // clean up the download listing source
    this.currentFileCount = 0;
    this.currentProgress = 0;
    this.brandDataExportService.cleanSource();
    this.brandDataExportService.brandManageListingService.updateListingSources(false, 20);
    this.brandDataExportService.initialLoading = true;
  }

  private clearIntervalsAfterDownload(): void {
    // Clear the counting inverval
    window.clearInterval(this.progressIntervalId);
    this.progressIntervalId = null;
    window.clearInterval(this.fileCountProgressId);
    this.fileCountProgressId = null;
  }

  private dataSourceToSelectedColumnArray(data: any): string[] {
    const dataArray: string[] = [];
    this.columnsToDisplay.forEach((col) => {
      dataArray.push(data[col]);
    });
    return dataArray;
  }
}

function toSnakeCase(str: string): string {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
}
