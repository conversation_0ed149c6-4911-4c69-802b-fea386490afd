import { Component, Inject, OnInit, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, UntypedFormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { BrandKeywordTrackingService } from '../brand-keyword-tracking.service';
import { VaFormsModule } from '@vendasta/forms';

export interface AddKeywordDialogData {
  accountGroupId?: string;
  availableLocations?: any[]; // Pass through available locations
  prefilledKeywords?: string[]; // For back navigation from location dialog
}

export interface AddKeywordDialogResult {
  keywords: string[];
  selectedLocationIds?: string[];
  success: boolean;
}

@Component({
  selector: 'app-add-keyword-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TranslateModule,
    VaFormsModule,
  ],
  templateUrl: './add-keyword-dialog.component.html',
  styleUrls: ['./add-keyword-dialog.component.scss'],
})
export class AddKeywordDialogComponent implements OnInit {
  keywordForm: UntypedFormArray;
  isLoading = false;
  formError: string | null = null;
  keywordLimit = 20; // Default keyword limit

  private fb = inject(FormBuilder);
  private snackBar = inject(MatSnackBar);
  private translate = inject(TranslateService);
  private brandKeywordTrackingService = inject(BrandKeywordTrackingService);
  private dialog = inject(MatDialog);

  constructor(
    public dialogRef: MatDialogRef<AddKeywordDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddKeywordDialogData,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.keywordForm = new UntypedFormArray([]);

    // If we have prefilled keywords (from back navigation), use them
    if (this.data.prefilledKeywords && this.data.prefilledKeywords.length > 0) {
      this.data.prefilledKeywords.forEach((keyword) => {
        this.keywordForm.controls.push(this.fb.control(keyword));
      });
    } else {
      // Otherwise, start with an empty field
      this.keywordForm.controls.push(this.fb.control(''));
    }
  }

  get isFormValid(): boolean {
    const hasValidKeywords = this.keywordForm.controls.some(
      (control) => control.value && control.value.trim().length >= 2,
    );
    return hasValidKeywords && !this.isLoading;
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  async onSave(): Promise<void> {
    if (!this.isFormValid) {
      return;
    }

    // Get all non-empty keywords
    const keywords = this.keywordForm.controls
      .map((control) => control.value?.trim())
      .filter((keyword) => keyword && keyword.length >= 2);

    if (keywords.length === 0) {
      return;
    }

    // Close the current dialog first
    this.dialogRef.close();

    // Dynamically import the location dialog to avoid circular dependencies
    const { KeywordLocationDialogComponent } = await import(
      '../keyword-location-dialog/keyword-location-dialog.component'
    );

    // Open the location dialog with the collected keywords
    const locationDialogRef = this.dialog.open(KeywordLocationDialogComponent, {
      width: '900px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: {
        availableLocations: this.data.availableLocations,
        prefilledKeywords: keywords, // Pass the keywords to the location dialog
      },
    });

    // Handle the result from the location dialog
    locationDialogRef.afterClosed().subscribe((locationResult) => {
      if (locationResult && locationResult.success) {
        // The location dialog already handles the final assignment and success message
        // We could emit an event here if needed by the parent component
      }
    });
  }

  removeClicked(keyword: string): void {
    // Handle keyword removal if needed by the forms-va-input-repeated component
  }
}
