<div class="dialog-container">
  <h3 mat-dialog-title>
    <mat-icon class="dialog-icon">search</mat-icon>
    <span>{{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.TITLE' | translate }}</span>
  </h3>

  <mat-dialog-content class="dialog-content">
    <div class="dialog-description">
      {{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.DESCRIPTION' | translate }}
    </div>

    @if (formError) {
      <div class="form-errors">
        {{ formError | translate }}
      </div>
    }

    <forms-va-input-repeated
      [controlArray]="keywordForm"
      [placeholder]="'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.KEYWORD_PLACEHOLDER' | translate"
      [label]="'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.KEYWORD_LABEL' | translate"
      [addText]="'BRAND_KEYWORD.ADD_KEYWORD' | translate"
      [maxFields]="keywordLimit"
      [disableAdd]="keywordForm.length >= keywordLimit"
      [bottomSpacing]="'small'"
      (removeClicked)="removeClicked($event)"
    ></forms-va-input-repeated>

    <div class="keyword-guidelines">
      <h4>{{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.GUIDELINES_TITLE' | translate }}</h4>
      <ul>
        <li>{{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.GUIDELINE_1' | translate }}</li>
        <li>{{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.GUIDELINE_2' | translate }}</li>
        <li>{{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.GUIDELINE_3' | translate }}</li>
      </ul>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <div class="button-container">
      <button mat-button color="primary" (click)="onCancel()" [disabled]="isLoading">
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="onSave()"
        [disabled]="!isFormValid"
        [class.spinner]="isLoading"
      >
        @if (isLoading) {
          <mat-spinner diameter="20"></mat-spinner>
        }
        {{ 'BRAND_KEYWORD.ADD_KEYWORD_DIALOG.SELECT_LOCATIONS_BUTTON' | translate }}
      </button>
    </div>
  </mat-dialog-actions>
</div>
