import { Component, Inject, OnInit, inject } from '@angular/core';
import { Form<PERSON>uilder, UntypedFormArray, UntypedFormControl, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { debounceTime, map, tap, catchError, finalize } from 'rxjs/operators';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { BrandKeywordTrackingService } from '../brand-keyword-tracking.service';
import { BulkKeywordService, BulkKeywordRequest } from '../services/bulk-keyword.service';
import { BulkLocationService, LocationDisplay } from '../services/bulk-location.service';

export interface KeywordLocationDialogData {
  availableLocations?: LocationDisplay[];
  preselectedLocations?: string[];
  prefilledKeywords?: string[];
}

export interface KeywordLocationDialogResult {
  keywords: string[];
  selectedLocationIds: string[];
  success: boolean;
}

@Component({
  selector: 'app-keyword-location-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    ScrollingModule,
    TranslateModule,
    VaFormsModule,
    GalaxyBadgeModule,
  ],
  templateUrl: './keyword-location-dialog.component.html',
  styleUrls: ['./keyword-location-dialog.component.scss'],
})
export class KeywordLocationDialogComponent implements OnInit {
  // Keyword form
  keywordForm: UntypedFormArray;
  keywordLimit = 20;

  // Location selection
  locationFilterControl = new UntypedFormControl('');
  availableLocations$: Observable<LocationDisplay[]>;
  filteredLocations$: Observable<LocationDisplay[]>;
  selectedLocationDetails$: Observable<LocationDisplay[]>;
  selectedLocations$$ = new BehaviorSubject<string[]>([]);

  // Form state
  isLoading = false;
  formError: string | null = null;
  noResultsFound = false;

  private fb = inject(FormBuilder);
  private snackBar = inject(MatSnackBar);
  private translate = inject(TranslateService);
  private brandKeywordTrackingService = inject(BrandKeywordTrackingService);
  private dialog = inject(MatDialog);
  private bulkKeywordService = inject(BulkKeywordService);
  private bulkLocationService = inject(BulkLocationService);

  constructor(
    public dialogRef: MatDialogRef<KeywordLocationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: KeywordLocationDialogData,
  ) {}

  ngOnInit(): void {
    this.initializeKeywordForm();
    this.initializeLocationSelection();
  }

  private initializeKeywordForm(): void {
    this.keywordForm = new UntypedFormArray([]);

    // If we have prefilled keywords from the previous dialog, use them
    if (this.data.prefilledKeywords && this.data.prefilledKeywords.length > 0) {
      this.data.prefilledKeywords.forEach((keyword) => {
        this.keywordForm.controls.push(this.fb.control(keyword));
      });
    } else {
      // Otherwise, start with an empty field
      this.keywordForm.controls.push(this.fb.control(''));
    }
  }

  private initializeLocationSelection(): void {
    // Initialize with preselected locations if provided
    if (this.data.preselectedLocations) {
      this.selectedLocations$$.next([...this.data.preselectedLocations]);
    }

    // Set up available locations - use real data if provided, otherwise get from service
    this.availableLocations$ = this.data.availableLocations 
      ? of(this.data.availableLocations)
      : this.getAvailableLocationsFromService();

    // Set up filtered locations
    this.filteredLocations$ = combineLatest([
      this.availableLocations$,
      this.locationFilterControl.valueChanges.pipe(debounceTime(300)),
      this.selectedLocations$$,
    ]).pipe(
      map(([locations, searchValue, selectedIds]) => this.filterLocations(locations, searchValue || '', selectedIds)),
      tap((filteredLocations) => {
        this.noResultsFound = filteredLocations.length === 0 && !!this.locationFilterControl.value?.trim();
      }),
    );

    // Set up selected location details
    this.selectedLocationDetails$ = combineLatest([this.availableLocations$, this.selectedLocations$$]).pipe(
      map(([locations, selectedIds]) => locations.filter((location) => selectedIds.includes(location.accountGroupId))),
    );
  }

  private getAvailableLocationsFromService(): Observable<LocationDisplay[]> {
    // Return available locations from the bulk location service
    return new Observable<LocationDisplay[]>((observer) => {
      this.bulkLocationService.getAvailableLocations()
        .then((locations) => {
          if (locations.length > 0) {
            observer.next(locations);
          } else {
            // Fall back to mock data if no real locations available
            observer.next(this.getMockLocations());
          }
          observer.complete();
        })
        .catch((error) => {
          console.error('Failed to get available locations:', error);
          // Fall back to mock data on error
          observer.next(this.getMockLocations());
          observer.complete();
        });
    });
  }

  private getMockLocations(): LocationDisplay[] {
    // Mock data - in real implementation, this would come from a service
    return [
      {
        accountGroupId: '1',
        name: 'Downtown Coffee Shop',
        address: '123 Main St, New York, NY',
        isPro: true,
        customerIdentifier: 'COFFEE001',
        tags: ['retail', 'food'],
        keywordCount: 8,
        keywordLimit: 15,
        keywordUsage: '8/15',
      },
      {
        accountGroupId: '2',
        name: 'Suburban Dental Clinic',
        address: '456 Oak Ave, Brooklyn, NY',
        isPro: false,
        customerIdentifier: 'DENTAL002',
        tags: ['healthcare'],
        keywordCount: 2,
        keywordLimit: 3,
        keywordUsage: '2/3',
      },
      {
        accountGroupId: '3',
        name: 'City Auto Repair',
        address: '789 Industrial Blvd, Queens, NY',
        isPro: true,
        customerIdentifier: 'AUTO003',
        tags: ['automotive', 'service'],
        keywordCount: 12,
        keywordLimit: 15,
        keywordUsage: '12/15',
      },
    ];
  }

  private filterLocations(locations: LocationDisplay[], searchValue: string, selectedIds: string[]): LocationDisplay[] {
    // Filter out already selected locations
    let filtered = locations.filter((location) => !selectedIds.includes(location.accountGroupId));

    // Apply search filter if search value exists
    if (searchValue.trim()) {
      const searchTerm = searchValue.toLowerCase();
      filtered = filtered.filter(
        (location) =>
          location.name.toLowerCase().includes(searchTerm) ||
          location.address.toLowerCase().includes(searchTerm) ||
          location.customerIdentifier?.toLowerCase().includes(searchTerm),
      );
    }

    return filtered;
  }

  get isFormValid(): boolean {
    const hasValidKeywords = this.keywordForm.controls.some(
      (control) => control.value && control.value.trim().length >= 2,
    );
    const hasSelectedLocations = this.selectedLocations$$.value.length > 0;
    return hasValidKeywords && hasSelectedLocations && !this.isLoading;
  }

  get selectedLocations$(): Observable<string[]> {
    return this.selectedLocations$$.asObservable();
  }

  selectLocation(locationElement: HTMLElement, locationId: string): void {
    locationElement.remove();
    const currentSelected = this.selectedLocations$$.value;
    if (!currentSelected.includes(locationId)) {
      this.selectedLocations$$.next([...currentSelected, locationId]);
    }
  }

  removeLocation(locationId: string): void {
    const currentSelected = this.selectedLocations$$.value;
    this.selectedLocations$$.next(currentSelected.filter((id) => id !== locationId));
  }

  removeClicked(keyword: string): void {
    // Handle keyword removal if needed by the forms-va-input-repeated component
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  async onBack(): Promise<void> {
    // Get the current keywords from the form
    const currentKeywords = this.keywordForm.controls
      .map((control) => control.value?.trim())
      .filter((keyword) => keyword && keyword.length >= 2);

    // Close the current dialog
    this.dialogRef.close();

    // Dynamically import the keyword dialog to avoid circular dependencies
    const { AddKeywordDialogComponent } = await import('../add-keyword-dialog/add-keyword-dialog.component');

    // Reopen the keyword dialog with the current keywords
    const keywordDialogRef = this.dialog.open(AddKeywordDialogComponent, {
      width: '600px',
      data: {
        availableLocations: this.data.availableLocations,
        prefilledKeywords: currentKeywords, // Pass current keywords back
      },
    });

    // Handle the result if user proceeds from keyword dialog again
    keywordDialogRef.afterClosed().subscribe((result) => {
      // If user cancels the keyword dialog, the workflow ends
      // If user proceeds, the keyword dialog will handle opening location dialog again
    });
  }

  onSave(): void {
    if (!this.isFormValid) {
      return;
    }

    // Get all non-empty keywords
    const keywords = this.keywordForm.controls
      .map((control) => control.value?.trim())
      .filter((keyword) => keyword && keyword.length >= 2);

    const selectedLocationIds = this.selectedLocations$$.value;

    if (keywords.length === 0) {
      this.formError = this.translate.instant('BRAND_KEYWORD.ERRORS.KEYWORD_REQUIRED');
      return;
    }

    if (selectedLocationIds.length === 0) {
      this.formError = this.translate.instant('BRAND_KEYWORD.ERRORS.LOCATION_REQUIRED');
      return;
    }

    this.isLoading = true;
    this.formError = null;

    // Execute actual keyword and location assignment
    this.executeKeywordLocationAssignment(keywords, selectedLocationIds);
  }

  private async executeKeywordLocationAssignment(keywords: string[], selectedLocationIds: string[]): Promise<void> {
    try {
      // Get partner and market info for the request
      const { partnerId, marketId } = await this.bulkLocationService.getPartnerMarketInfo();

      // Create the bulk keyword request
      const request: BulkKeywordRequest = {
        businessIds: selectedLocationIds,
        keywords: keywords,
        partnerId,
        marketId,
      };

      // Execute the bulk keyword addition
      const results = await this.bulkKeywordService.addKeywordsToBulkBusinesses(request);

      // Show results and close dialog
      this.bulkKeywordService.showBulkResultMessage(results);
      
      const successfulResults = results.filter(r => r.success);
      
      this.dialogRef.close({
        keywords,
        selectedLocationIds,
        success: successfulResults.length > 0,
      } as KeywordLocationDialogResult);

    } catch (error) {
      console.error('Error adding keywords to locations:', error);
      this.formError = this.translate.instant('BRAND_KEYWORD.ERRORS.ADD_FAILED');
    } finally {
      this.isLoading = false;
    }
  }


}
