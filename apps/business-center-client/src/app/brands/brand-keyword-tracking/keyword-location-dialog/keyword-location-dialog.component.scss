@use 'design-tokens' as *;

$dialog-max-width: 900px;

mat-dialog-container {
  width: $dialog-max-width;
  max-width: 95vw;
  max-height: 90vh;
}

.dialog-container {
  padding: 20px;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: $dark-gray;

  .dialog-icon {
    color: $blue;
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .step-indicator {
    font-size: 16px;
    font-weight: 400;
    color: $gray;
    margin-left: 8px;
  }
}

.dialog-content {
  padding: 0;
  margin: 0;
  max-height: 60vh;
  overflow-y: auto;

  .dialog-description {
    margin-bottom: 20px;
    color: $gray;
    font-size: 14px;
    line-height: 1.5;
  }

  .form-errors {
    color: $red;
    background-color: rgba($red, 0.1);
    border: 1px solid rgba($red, 0.2);
    border-radius: 4px;
    padding: 10px 12px;
    margin-bottom: 16px;
    font-size: 13px;
  }

  .section {
    margin-bottom: 24px;
    border: 1px solid $light-gray;
    border-radius: 8px;
    overflow: hidden;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      padding: 12px 16px;
      background-color: $lighter-gray;
      font-size: 14px;
      font-weight: 600;
      color: $dark-gray;
      border-bottom: 1px solid $light-gray;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: $blue;
      }

      .prefilled-indicator {
        font-size: 12px;
        font-weight: 400;
        color: $gray;
        margin-left: 8px;
      }
    }

    .keywords-description {
      padding: 12px 16px;
      background-color: rgba($blue, 0.05);
      border-left: 3px solid $blue;
      font-size: 13px;
      color: $dark-gray;
      margin-bottom: 8px;
    }
  }

  .keywords-section {
    .keyword-guidelines {
      margin: 16px;
      padding: 12px;
      background-color: $lighter-gray;
      border-radius: 6px;
      border-left: 3px solid $blue;

      h4 {
        margin: 0 0 8px 0;
        font-size: 13px;
        font-weight: 600;
        color: $dark-gray;
      }

      ul {
        margin: 0;
        padding-left: 16px;

        li {
          font-size: 12px;
          color: $gray;
          margin-bottom: 4px;
          line-height: 1.3;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    :host ::ng-deep forms-va-input-repeated {
      padding: 16px;
    }
  }

  .locations-section {
    .location-selection-container {
      display: flex;
      min-height: 300px;

      .available-locations {
        flex: 1;
        border-right: 1px solid $light-gray;

        .search-container {
          padding: 16px;
          border-bottom: 1px solid $light-gray;

          .full-width {
            width: 100%;
          }
        }

        .available-list {
          .location-viewport {
            height: 250px;

            .no-results-found {
              text-align: center;
              padding: 32px 16px;
              color: $gray;
              font-size: 14px;
            }

            .location-option {
              display: flex;
              align-items: center;
              padding: 12px 16px;
              border-bottom: 1px solid $light-gray;
              transition: background-color 0.2s ease;

              &:hover {
                background-color: rgba($blue, 0.05);
              }

              .location-details {
                flex: 1;

                .location-name {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-size: 14px;
                  font-weight: 500;
                  color: $dark-gray;
                  margin-bottom: 4px;
                }

                .location-address {
                  font-size: 12px;
                  color: $gray;
                  margin-bottom: 2px;
                }

                .location-id {
                  font-size: 11px;
                  color: $gray;
                  font-style: italic;
                }
              }

              .location-actions {
                button {
                  mat-icon {
                    color: $blue;
                  }

                  &:hover {
                    background-color: rgba($blue, 0.1);
                  }
                }
              }
            }
          }
        }
      }

      .selected-locations {
        flex: 1;
        background-color: $lighter-gray;
        display: flex;
        flex-direction: column;

        .selected-header {
          padding: 16px;
          border-bottom: 1px solid $light-gray;
          background-color: $white;

          .selected-title {
            font-size: 14px;
            font-weight: 600;
            color: $dark-gray;
          }

          .selected-count {
            margin-left: 8px;
            font-size: 12px;
            color: $gray;
          }
        }

        .selected-list {
          flex: 1;
          padding: 16px;
          overflow-y: auto;
          max-height: 234px;

          .no-selection {
            text-align: center;
            padding: 32px 16px;
            color: $gray;
            font-size: 14px;
          }

          .selected-location-card {
            margin-bottom: 8px;
            padding: 8px 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .selected-location-content {
              display: flex;
              align-items: center;

              .location-details {
                flex: 1;

                .location-name {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-size: 13px;
                  font-weight: 500;
                  color: $dark-gray;
                  margin-bottom: 2px;
                }

                .location-address {
                  font-size: 11px;
                  color: $gray;
                }
              }

              .remove-action {
                .remove-icon {
                  font-size: 18px;
                  color: $light-gray;
                  cursor: pointer;
                  transition: color 0.2s ease;

                  &:hover {
                    color: $red;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.dialog-actions {
  padding: 20px 0 0 0;
  margin: 0;
  border-top: 1px solid $light-gray;

  .button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    width: 100%;

    .left-buttons {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .right-buttons {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    button {
      min-width: 80px;
      height: 36px;
      text-transform: none;
      font-weight: 500;
      font-size: 14px;

      &.spinner {
        position: relative;
        color: transparent;

        mat-spinner {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    button[mat-button] {
      color: $gray;

      &:hover:not(:disabled) {
        background-color: rgba($gray, 0.1);
      }
    }

    button[mat-raised-button] {
      &:hover:not(:disabled) {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

// Loading spinner animation
.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid $white;
  border-top-color: $blue;
  animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

// Success snackbar styling
:host ::ng-deep .success-snackbar {
  background-color: $green;
  color: $white;
}

// Responsive design
@media (max-width: 768px) {
  mat-dialog-container {
    width: 95vw;
    max-width: 95vw;
  }

  .dialog-content {
    .locations-section {
      .location-selection-container {
        flex-direction: column;

        .available-locations {
          border-right: none;
          border-bottom: 1px solid $light-gray;
        }

        .selected-locations {
          .selected-list {
            max-height: 150px;
          }
        }
      }
    }
  }
}
