<div class="dialog-container">
  <h2 mat-dialog-title>
    <mat-icon class="dialog-icon">analytics</mat-icon>
    <span>{{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.TITLE' | translate }}</span>
  </h2>

  <mat-dialog-content class="dialog-content">
    <div class="dialog-description">
      {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.DESCRIPTION' | translate }}
    </div>

    @if (formError) {
      <div class="form-errors">
        {{ formError | translate }}
      </div>
    }

    <!-- Locations Section -->
    <div class="section locations-section">
      <h3 class="section-title">
        <mat-icon>location_on</mat-icon>
        {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.LOCATIONS_TITLE' | translate }}
      </h3>

      <div class="location-selection-container">
        <!-- Available Locations -->
        <div class="available-locations">
          <div class="search-container">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>{{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.SEARCH_PLACEHOLDER' | translate }}</mat-label>
              <input
                matInput
                [formControl]="locationFilterControl"
                [placeholder]="'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.SEARCH_PLACEHOLDER' | translate"
                autocomplete="off"
              />
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </div>

          <div class="available-list">
            <cdk-virtual-scroll-viewport itemSize="60" class="location-viewport">
              <ng-container *ngIf="filteredLocations$ | async as locations">
                <div class="no-results-found" *ngIf="noResultsFound">
                  {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.NO_RESULTS' | translate }}
                </div>

                <div #locationElement class="location-option" *cdkVirtualFor="let location of locations">
                  <div class="location-details">
                    <div class="location-name">
                      {{ location.name }}
                      <glxy-badge *ngIf="!location.isPro" color="grey" size="small">
                        {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.STANDARD' | translate }}
                      </glxy-badge>
                    </div>
                    <div class="location-address">{{ location.address }}</div>
                    <div class="location-id" *ngIf="location.customerIdentifier">
                      ID: {{ location.customerIdentifier }}
                    </div>
                  </div>
                  <div class="location-actions">
                    <button
                      mat-icon-button
                      color="primary"
                      (click)="selectLocation(locationElement, location.accountGroupId)"
                      [attr.aria-label]="'Add location'"
                    >
                      <mat-icon>add_circle</mat-icon>
                    </button>
                  </div>
                </div>
              </ng-container>
            </cdk-virtual-scroll-viewport>
          </div>
        </div>

        <!-- Selected Locations -->
        <div class="selected-locations">
          <ng-container *ngIf="selectedLocationDetails$ | async as selectedLocations">
            <div class="selected-header">
              <span class="selected-title">{{
                'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.SELECTED_LOCATIONS' | translate
              }}</span>
              <span class="selected-count" *ngIf="selectedLocations.length > 0">
                ({{ selectedLocations.length }}
                {{
                  (selectedLocations.length === 1
                    ? 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.LOCATION'
                    : 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.LOCATIONS'
                  ) | translate
                }})
              </span>
            </div>

            <div class="selected-list">
              <div class="no-selection" *ngIf="selectedLocations.length === 0">
                {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.NO_LOCATIONS_SELECTED' | translate }}
              </div>

              <mat-card appearance="outlined" class="selected-location-card" *ngFor="let location of selectedLocations">
                <div class="selected-location-content">
                  <div class="location-details">
                    <div class="location-name">
                      {{ location.name }}
                      <glxy-badge *ngIf="!location.isPro" color="grey" size="small">
                        {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.STANDARD' | translate }}
                      </glxy-badge>
                    </div>
                    <div class="location-address">{{ location.address }}</div>
                  </div>
                  <div class="remove-action">
                    <mat-icon
                      class="remove-icon"
                      (click)="removeLocation(location.accountGroupId)"
                      [attr.aria-label]="'Remove location'"
                    >
                      cancel
                    </mat-icon>
                  </div>
                </div>
              </mat-card>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <div class="button-container">
      <!-- Left-aligned buttons -->
      <div class="left-buttons" *ngIf="data.prefilledKeywords && data.prefilledKeywords.length > 0">
        <button mat-button (click)="onBack()" [disabled]="isLoading">
          <mat-icon>arrow_back</mat-icon>
          {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.BACK_BUTTON' | translate }}
        </button>
      </div>

      <!-- Right-aligned buttons -->
      <div class="right-buttons">
        <button mat-button color="primary" (click)="onCancel()" [disabled]="isLoading">
          {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="onSave()"
          [disabled]="!isFormValid"
          [class.spinner]="isLoading"
        >
          @if (isLoading) {
            <mat-spinner diameter="20"></mat-spinner>
          }
          {{ 'BRAND_KEYWORD.KEYWORD_LOCATION_DIALOG.SAVE_BUTTON' | translate }}
        </button>
      </div>
    </div>
  </mat-dialog-actions>
</div>
