import { Component, inject, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-add-keyword-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, TranslateModule],
  templateUrl: './add-keyword-button.component.html',
  styleUrls: ['./add-keyword-button.component.scss'],
})
export class AddKeywordButtonComponent {
  @Input() accountGroupId?: string;
  @Input() disabled = false;
  @Input() availableLocations?: any[];

  private dialog = inject(MatDialog);

  async openAddKeywordDialog(): Promise<void> {
    // Dynamic import to avoid circular dependency
    const { AddKeywordDialogComponent } = await import('../add-keyword-dialog/add-keyword-dialog.component');

    const dialogRef = this.dialog.open(AddKeywordDialogComponent, {
      width: '600px',
      data: {
        accountGroupId: this.accountGroupId,
        availableLocations: this.availableLocations,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.keywords && result.keywords.length > 0) {
        // The dialog will handle the actual keyword addition
        // This component just triggers the dialog
        console.log('Keywords added:', result.keywords);
      }
    });
  }
}
