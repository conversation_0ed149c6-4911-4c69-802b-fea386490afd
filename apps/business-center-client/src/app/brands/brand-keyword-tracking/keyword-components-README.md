# Keyword Components

This directory contains multiple Angular components for managing keyword tracking functionality in the brands module, including a two-step workflow for keyword entry and location assignment.

## Components

### AddKeywordButtonComponent

**Path:** `add-keyword-button/add-keyword-button.component.ts`

A standalone button component that opens the Add Keyword dialog when clicked.

**Props:**
- `accountGroupId?: string` - Optional account group ID for context
- `disabled: boolean` - Whether the button should be disabled (default: false)
- `availableLocations?: any[]` - Available locations for the location selection step

**Usage:**
```html
<app-add-keyword-button 
  [accountGroupId]="selectedAccountGroupId"
  [availableLocations]="locationsList"
  [disabled]="isFormLoading">
</app-add-keyword-button>
```

**Workflow:**
This button initiates a two-step process:
1. Opens AddKeywordDialogComponent for keyword entry
2. Automatically opens KeywordLocationDialogComponent for location selection

### AddKeywordDialogComponent

**Path:** `add-keyword-dialog/add-keyword-dialog.component.ts`

A dialog component that provides a form for adding multiple new keywords. This is the first step in the two-step workflow.

**Features:**
- Multiple keyword input using forms-va-input-repeated component from @vendasta/forms
- Dynamic addition and removal of keyword fields
- Keyword limit enforcement (default: 20)
- Keyword guidelines display
- Responsive design following Material Design principles
- **Automatically opens KeywordLocationDialogComponent** when Save is clicked
- Passes collected keywords to the location selection dialog

**Dialog Data Interface:**
```typescript
interface AddKeywordDialogData {
  accountGroupId?: string;
  availableLocations?: any[]; // Passed to location dialog
}
```

**Dialog Result Interface:**
```typescript
interface AddKeywordDialogResult {
  keywords: string[];
  selectedLocationIds?: string[]; // From location dialog
  success: boolean;
}
```

**Usage:**
```typescript
const dialogRef = this.dialog.open(AddKeywordDialogComponent, {
  width: '600px',
  data: {
    accountGroupId: 'some-account-group-id',
    availableLocations: this.locationsList,
  },
});

// Note: The dialog handles opening the location dialog automatically
// Final result comes from the location dialog, not this one
```

### KeywordLocationDialogComponent

**Path:** `keyword-location-dialog/keyword-location-dialog.component.ts`

A comprehensive dialog that combines keyword input with location selection. Can be used standalone or as part of the two-step workflow.

**Features:**
- Accepts pre-filled keywords from the previous dialog
- Multiple keyword input with modification capability
- Location search and filtering
- Virtual scrolling for large location lists
- Selected locations management
- Final keyword-to-location assignment
- Success notifications and error handling

## Integration

The button component has been integrated into the main `brand-keyword-tracking.component.html` template in the page actions area, alongside the time range picker.

## Translations

All text content uses the Angular i18n translation system with keys under the `BRAND_KEYWORD` namespace:

- `BRAND_KEYWORD.ADD_KEYWORD` - Button text
- `BRAND_KEYWORD.ADD_KEYWORD_DIALOG.*` - Dialog content
- `BRAND_KEYWORD.ERRORS.*` - Error messages
- `BRAND_KEYWORD.SUCCESS.*` - Success messages

## Service Integration

The workflow now handles keyword-to-location assignment in the `KeywordLocationDialogComponent`. The actual keyword addition happens in the location dialog with location context.

**TODO for Production:**
1. Implement location service integration in `KeywordLocationDialogComponent.getMockLocations()`
2. Replace `simulateKeywordLocationAssignment()` with real API integration
3. Add proper error handling for API failures
4. Implement keyword validation against existing keywords per location
5. Add refresh functionality to update keyword lists after assignment
6. Consider implementing keyword suggestions based on business location data

## Styling

Both components follow the existing application's design system:
- Uses Material Design components and patterns
- Follows the color scheme defined in `design-tokens`
- Responsive design for different screen sizes
- Consistent with other dialog components in the application

## Files Created

```
add-keyword-button/
├── add-keyword-button.component.ts
├── add-keyword-button.component.html
└── add-keyword-button.component.scss

add-keyword-dialog/
├── add-keyword-dialog.component.ts
├── add-keyword-dialog.component.html
└── add-keyword-dialog.component.scss

keyword-location-dialog/
├── keyword-location-dialog.component.ts
├── keyword-location-dialog.component.html
└── keyword-location-dialog.component.scss

keyword-location-button/
├── keyword-location-button.component.ts
├── keyword-location-button.component.html
└── keyword-location-button.component.scss

add-keyword-components.ts (barrel export)
keyword-components-README.md
```

## Workflow Summary

1. **Two-Step Process (Recommended):**
   - User clicks `AddKeywordButtonComponent`
   - `AddKeywordDialogComponent` opens → user enters keywords → clicks Save
   - Dialog closes and `KeywordLocationDialogComponent` opens automatically
   - Keywords are pre-filled, user selects locations → clicks Save
   - Final assignment occurs with success notification

2. **Single-Step Process (Alternative):**
   - User clicks `KeywordLocationButtonComponent`
   - `KeywordLocationDialogComponent` opens with both keyword input and location selection
   - User completes both steps in one dialog 