import { Component, inject, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BulkLocationService } from '../services/bulk-location.service';

@Component({
  selector: 'app-bulk-add-keyword-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, TranslateModule],
  templateUrl: './bulk-add-keyword-button.component.html',
  styleUrls: ['./bulk-add-keyword-button.component.scss'],
})
export class BulkAddKeywordButtonComponent {
  @Input() disabled = false;

  private dialog = inject(MatDialog);
  private bulkLocationService = inject(BulkLocationService);

  async openBulkAddKeywordDialog(): Promise<void> {
    try {
      // Get available locations for the dialog
      const availableLocations = await this.bulkLocationService.getAvailableLocations();

      // Dynamic import to avoid circular dependency
      const { AddKeywordDialogComponent } = await import('../add-keyword-dialog/add-keyword-dialog.component');

      const dialogRef = this.dialog.open(AddKeywordDialogComponent, {
        width: '600px',
        data: {
          availableLocations: availableLocations,
          isBulkMode: true, // Flag to indicate this is bulk mode
        },
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result && result.success) {
          // The dialog will handle the actual keyword addition
          // This component just triggers the dialog
          console.log('Bulk keywords operation completed:', result);
        }
      });
    } catch (error) {
      console.error('Error opening bulk keyword dialog:', error);
    }
  }
} 