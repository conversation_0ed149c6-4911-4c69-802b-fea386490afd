import { Component, inject, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { KeywordLocationDialogComponent } from '../keyword-location-dialog/keyword-location-dialog.component';
import { LocationDisplay } from '../services/bulk-location.service';

@Component({
  selector: 'app-keyword-location-button',
  standalone: true,
  imports: [MatButtonModule, MatIconModule, TranslateModule],
  templateUrl: './keyword-location-button.component.html',
  styleUrls: ['./keyword-location-button.component.scss'],
})
export class KeywordLocationButtonComponent {
  @Input() availableLocations?: LocationDisplay[];
  @Input() preselectedLocations?: string[];
  @Input() disabled = false;

  private dialog = inject(MatDialog);

  async openKeywordLocationDialog(): Promise<void> {
    const dialogRef = this.dialog.open(KeywordLocationDialogComponent, {
      width: '900px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: {
        availableLocations: this.availableLocations,
        preselectedLocations: this.preselectedLocations,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.keywords && result.keywords.length > 0 && result.selectedLocationIds.length > 0) {
        console.log('Keywords assigned to locations:', {
          keywords: result.keywords,
          locations: result.selectedLocationIds,
        });
        // Handle successful keyword-location assignment
        // You can emit an event or call a service method here
      }
    });
  }
}
