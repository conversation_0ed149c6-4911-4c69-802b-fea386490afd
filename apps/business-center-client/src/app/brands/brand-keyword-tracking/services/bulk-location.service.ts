import { Injectable, inject } from '@angular/core';
import { Observable, firstValueFrom, combineLatest } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AccountGroupApiService, GetMultiRequest, ProjectionFilter as AGProjectionFilter } from '@vendasta/account-group';
import { 
  ListingProfileApiService, 
  GetMultiListingProfileRequest, 
  ProjectionFilter 
} from '@vendasta/listing-products';
import { BrandsService } from '../../brands.service';
import { BrandContext, QueryService } from '../../../metrics/query.service';
import { TranslateService } from '@ngx-translate/core';
import { BulkKeywordService, BusinessKeywordData } from './bulk-keyword.service';

export interface LocationDisplay {
  accountGroupId: string;
  name: string;
  address: string;
  isPro: boolean;
  customerIdentifier: string;
  tags: string[];
  keywordCount: number;
  keywordLimit: number;
  keywordUsage: string; // e.g., "5/15"
}

@Injectable({
  providedIn: 'root',
})
export class BulkLocationService {
  private accountGroupService = inject(AccountGroupApiService);
  private listingProfileService = inject(ListingProfileApiService);
  private brandsService = inject(BrandsService);
  private queryService = inject(QueryService);
  private translate = inject(TranslateService);
  private bulkKeywordService = inject(BulkKeywordService);

  /**
   * Get available locations for keyword assignment from current brand context
   */
  async getAvailableLocations(): Promise<LocationDisplay[]> {
    try {
      // Get current brand context
      const brandContext = await firstValueFrom(this.queryService.brandsContext$);
      
      if (!brandContext || !brandContext.resourceIds?.length) {
        console.warn('No brand context available');
        return [];
      }

             // Extract business IDs from the brand context
       const businessIds = await this.extractBusinessIdsFromContext(brandContext);
       
       if (businessIds.length === 0) {
         console.warn('No business IDs found in brand context - using alternative method');
         // Fall back to getting business IDs through the brand service
         // This is a placeholder - in practice, you would integrate with the existing
         // brand keyword tracking service to get the business IDs
         return [];
       }

       // Get business data with keyword information
       const businessDataMap = await this.bulkKeywordService.getBusinessKeywordData(businessIds);

      // Convert to LocationDisplay format
      const locations: LocationDisplay[] = [];
      
      for (const [businessId, businessData] of businessDataMap) {
        const location: LocationDisplay = {
          accountGroupId: businessId,
          name: businessData.businessName,
          address: businessData.address,
          isPro: businessData.isPro,
          customerIdentifier: businessId,
          tags: this.generateTagsFromBusinessData(businessData),
          keywordCount: businessData.existingKeywords.length,
          keywordLimit: businessData.keywordLimit,
          keywordUsage: `${businessData.existingKeywords.length}/${businessData.keywordLimit}`,
        };
        
        locations.push(location);
      }

      // Sort by name for better UX
      return locations.sort((a, b) => a.name.localeCompare(b.name));
      
    } catch (error) {
      console.error('Failed to get available locations:', error);
      return [];
    }
  }

  /**
   * Get locations with detailed keyword information for selection
   */
  async getLocationsWithKeywordData(businessIds?: string[]): Promise<LocationDisplay[]> {
    try {
      let targetBusinessIds = businessIds;
      
      // If no specific business IDs provided, get all from brand context
      if (!targetBusinessIds) {
        const locations = await this.getAvailableLocations();
        return locations;
      }

      // Get business data for specific IDs
      const businessDataMap = await this.bulkKeywordService.getBusinessKeywordData(targetBusinessIds);
      
      const locations: LocationDisplay[] = [];
      
      for (const [businessId, businessData] of businessDataMap) {
        const location: LocationDisplay = {
          accountGroupId: businessId,
          name: businessData.businessName,
          address: businessData.address,
          isPro: businessData.isPro,
          customerIdentifier: businessId,
          tags: this.generateTagsFromBusinessData(businessData),
          keywordCount: businessData.existingKeywords.length,
          keywordLimit: businessData.keywordLimit,
          keywordUsage: `${businessData.existingKeywords.length}/${businessData.keywordLimit}`,
        };
        
        locations.push(location);
      }

      return locations.sort((a, b) => a.name.localeCompare(b.name));
      
    } catch (error) {
      console.error('Failed to get locations with keyword data:', error);
      return [];
    }
  }

  /**
   * Check if locations can accept new keywords
   */
  async validateKeywordCapacity(businessIds: string[], keywordCount: number): Promise<{
    canAddToAll: boolean;
    capacityByBusiness: Map<string, { canAdd: number; willSkip: number; atLimit: boolean }>;
  }> {
    try {
      const businessDataMap = await this.bulkKeywordService.getBusinessKeywordData(businessIds);
      const capacityByBusiness = new Map<string, { canAdd: number; willSkip: number; atLimit: boolean }>();
      let canAddToAll = true;

      for (const [businessId, businessData] of businessDataMap) {
        const availableSlots = businessData.keywordLimit - businessData.existingKeywords.length;
        const canAdd = Math.min(keywordCount, availableSlots);
        const willSkip = keywordCount - canAdd;
        const atLimit = availableSlots <= 0;

        capacityByBusiness.set(businessId, {
          canAdd,
          willSkip,
          atLimit,
        });

        if (atLimit) {
          canAddToAll = false;
        }
      }

      return { canAddToAll, capacityByBusiness };
      
    } catch (error) {
      console.error('Failed to validate keyword capacity:', error);
      return { 
        canAddToAll: false, 
        capacityByBusiness: new Map() 
      };
    }
  }

  /**
   * Get partner and market information for keyword operations
   */
  async getPartnerMarketInfo(): Promise<{ partnerId?: string; marketId?: string }> {
    try {
      const brandContext = await firstValueFrom(this.queryService.brandsContext$);
      
      if (!brandContext || !brandContext.resourceIds?.length) {
        return {};
      }

             // Try to get partner/market info from the first business
       const businessIds = await this.extractBusinessIdsFromContext(brandContext);
       
       if (businessIds.length === 0) {
         return {};
       }

      // Get account group data to extract partner/market info
      const accountGroupResponse = await firstValueFrom(
        this.accountGroupService.getMulti(
          new GetMultiRequest({
            accountGroupIds: [businessIds[0]], // Just need one to get partner info
            projectionFilter: {
              accountGroupExternalIdentifiers: true,
            } as AGProjectionFilter,
          })
        )
      );

      const accountGroup = accountGroupResponse.accountGroups?.[0]?.accountGroup;
      
      return {
        partnerId: accountGroup?.accountGroupExternalIdentifiers?.partnerId,
        marketId: accountGroup?.accountGroupExternalIdentifiers?.marketId,
      };
      
    } catch (error) {
      console.error('Failed to get partner/market info:', error);
      return {};
    }
  }

  /**
   * Extract business IDs from brand context
   * Note: This gets the group ID from context, then business IDs are fetched through analytics queries
   */
  private async extractBusinessIdsFromContext(brandContext: BrandContext): Promise<string[]> {
    if (!brandContext.resourceIds?.length) {
      return [];
    }

    // Get the group ID from the brand context
    const groupId = brandContext.resourceIds[0]?.groupId?.groupPathNodes?.[0];
    
    if (!groupId) {
      return [];
    }

    // In a real implementation, we would query the analytics service to get business IDs
    // For now, we'll return an empty array and let the calling code handle it
    // This follows the pattern used in the existing brand keyword tracking service
    console.warn('Business ID extraction from brand context not fully implemented. Use getAvailableLocations() instead.');
    return [];
  }

  /**
   * Generate descriptive tags for a business based on its data
   */
  private generateTagsFromBusinessData(businessData: BusinessKeywordData): string[] {
    const tags: string[] = [];
    
    // Edition tag
    tags.push(businessData.isPro ? 'Pro' : 'Standard');
    
    // Keyword capacity tags
    const usagePercentage = (businessData.existingKeywords.length / businessData.keywordLimit) * 100;
    if (usagePercentage >= 90) {
      tags.push('Near Limit');
    } else if (usagePercentage >= 70) {
      tags.push('High Usage');
    } else if (usagePercentage <= 25) {
      tags.push('Low Usage');
    }

    // Keyword count tag
    if (businessData.existingKeywords.length === 0) {
      tags.push('No Keywords');
    } else if (businessData.existingKeywords.length >= 10) {
      tags.push('Many Keywords');
    }

    return tags;
  }

  /**
   * Format location for display in selection components
   */
  formatLocationForDisplay(location: LocationDisplay): string {
    return `${location.name} (${location.keywordUsage})`;
  }

  /**
   * Get location summary for bulk operations
   */
  getLocationSummary(locations: LocationDisplay[]): {
    total: number;
    proCount: number;
    standardCount: number;
    averageUsage: number;
    atLimitCount: number;
  } {
    const total = locations.length;
    const proCount = locations.filter(l => l.isPro).length;
    const standardCount = total - proCount;
    const atLimitCount = locations.filter(l => l.keywordCount >= l.keywordLimit).length;
    
    const totalUsage = locations.reduce((sum, l) => {
      return sum + (l.keywordLimit > 0 ? (l.keywordCount / l.keywordLimit) : 0);
    }, 0);
    
    const averageUsage = total > 0 ? (totalUsage / total) * 100 : 0;

    return {
      total,
      proCount,
      standardCount,
      averageUsage: Math.round(averageUsage),
      atLimitCount,
    };
  }
} 