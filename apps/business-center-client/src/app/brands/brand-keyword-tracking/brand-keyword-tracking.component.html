<glxy-page [pagePadding]="false">
  <mat-drawer-container [hasBackdrop]="false">
    <glxy-page [pagePadding]="false">
      <glxy-page-toolbar>
        <glxy-page-title updateTabTitle>{{ 'BRAND_KEYWORD.TITLE' | translate }}</glxy-page-title>
        <glxy-page-actions>
          <glxy-page-actions>
            <!-- <app-add-keyword-button></app-add-keyword-button> -->
            <app-bulk-add-keyword-button></app-bulk-add-keyword-button>
            <bc-time-range-picker></bc-time-range-picker>
          </glxy-page-actions>
        </glxy-page-actions>
      </glxy-page-toolbar>
      <glxy-page-below-toolbar>
        <bc-nav-tabs></bc-nav-tabs>
      </glxy-page-below-toolbar>

      <glxy-page-wrapper [widthPreset]="'full'">
        @if (keywordState$ | async; as state) {
          @if (state.loading) {
            <div class="row">
              <div class="col col-xs-12">
                <div class="cards-loading-skeleton">
                  @for (item of [1, 2, 3]; track item) {
                    <mat-card class="skeleton-card">
                      <mat-card-content>
                        <div class="skeleton-line short"></div>
                        <div class="skeleton-line long"></div>
                      </mat-card-content>
                    </mat-card>
                  }
                </div>
              </div>
            </div>
            <br />
            <mat-card>
              <div class="table-loading-skeleton">
                <div class="skeleton-header">
                  <div class="skeleton-line header-line"></div>
                </div>
                <div class="skeleton-rows">
                  @for (item of [1, 2, 3, 4, 5]; track item) {
                    <div class="skeleton-row">
                      <div class="skeleton-line table-cell wide"></div>
                      @for (cell of [1, 2, 3, 4]; track cell) {
                        <div class="skeleton-line table-cell"></div>
                      }
                    </div>
                  }
                </div>
              </div>
            </mat-card>
          } @else {
            @if (state?.options?.length > 0) {
              <div class="row drop-down-bg">
                <div class="custom-dropdown">
                  <glxy-page-actions>
                    <glxy-form-field size="default" class="fade-in">
                      <mat-select [value]="state?.selectedKeyword" (selectionChange)="onKeywordChange($event.value)">
                        @for (item of state?.options; track item) {
                          <mat-option [value]="item.keyword">
                            {{ item.label }}
                          </mat-option>
                        }
                      </mat-select>
                    </glxy-form-field>
                  </glxy-page-actions>
                </div>
              </div>

              <div class="row">
                <div class="col col-xs-12">
                  @if (SEOTableData$ | async; as data) {
                    <ng-container>
                      <app-brand-local-seo-keywords-stats-cards
                        [data]="data"
                        [keywordMetrics]="brandKeywordTrackingService.keywordMetrics$ | async"
                      ></app-brand-local-seo-keywords-stats-cards>
                    </ng-container>
                  }
                </div>
              </div>
              <br />
              <div class="row">
                <div class="col alignment-space">
                  <div class="searchitem">
                    <glxy-form-field suffixIcon="search">
                      <input type="search" placeholder="Search" (input)="search($event)" matInput />
                    </glxy-form-field>
                  </div>
                </div>
              </div>
              <br />
              <ng-container *ngTemplateOutlet="showTable"></ng-container>
              <ng-template #showTable>
                <mat-card>
                  <div
                    class="scroll"
                    matSort
                    [matSortActive]="sortSettings.active"
                    [matSortDirection]="sortSettings.direction"
                    (matSortChange)="setSortSettings($event)"
                  >
                    <table mat-table [dataSource]="dataSource" class="example-table">
                      <ng-container matColumnDef="empty" class="table-background">
                        <th mat-header-cell *matHeaderCellDef class="divided"></th>
                      </ng-container>

                      <ng-container matColumnDef="local" class="table-background">
                        <th mat-header-cell colspan="2" class="divided" *matHeaderCellDef>
                          <div class="mat-sort-header-content td-align">
                            <img
                              aria-hidden="true"
                              src="https://vstatic-prod.apigateway.co/listing-builder-client/assets/images/google/google-maps.svg"
                              alt="GoogleMapImage"
                            />
                            <div>{{ 'BRAND_KEYWORD.MAPS' | translate }}</div>
                          </div>
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="organic" class="table-background">
                        <th mat-header-cell colspan="2" class="" *matHeaderCellDef>
                          <div class="mat-sort-header-content td-align">
                            <img
                              src="https://vstatic-prod.apigateway.co/listing-builder-client/assets/images/google/google.png"
                              aria-hidden="true"
                              alt="GoogleImage"
                            />
                            <div>{{ 'BRAND_KEYWORD.ORGANIC' | translate }}</div>
                          </div>
                        </th>
                      </ng-container>

                      <ng-container matColumnDef="business">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header class="divided th-align">
                          {{ 'BRAND_KEYWORD.BUSINESS' | translate }}
                        </th>
                        <td mat-cell *matCellDef="let element" class="right-border fade-in">
                          <div class="business-color td-align">{{ element.business | translate }}</div>
                          <div class="td-align">{{ element.address | translate }}</div>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="mapsRank">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header class="th-align">
                          {{ 'BRAND_KEYWORD.MAPS_RANK' | translate }}
                        </th>
                        <td mat-cell *matCellDef="let element" class="th-align fade-in">
                          {{ element.mapsRank.value }}
                          @if (element.mapsStartedAt.rawValue > 0 && element.mapsRank.rawValue !== 0) {
                            <app-local-seo-delta
                              class="abs"
                              [value]="-1 * (element.mapsRank?.rawValue - element.mapsStartedAt?.rawValue)"
                            ></app-local-seo-delta>
                          }
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="mapsStartedAt">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header class="divided th-space">
                          {{ 'BRAND_KEYWORD.MAPS_STARTED_AT' | translate }}
                        </th>
                        <td mat-cell *matCellDef="let element" class="right-border align-text fade-in">
                          {{ element.mapsStartedAt.value }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="organicRank">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header class="th-align">
                          {{ 'BRAND_KEYWORD.ORGANIC_RANK' | translate }}
                        </th>
                        <td mat-cell *matCellDef="let element" class="th-align fade-in">
                          {{ element.organicRank.value }}
                          @if (element.organicStartedAt.rawValue > 0 && element.organicRank.rawValue !== 0) {
                            <app-local-seo-delta
                              class="abs"
                              [value]="-1 * (element.organicRank?.rawValue - element.organicStartedAt?.rawValue)"
                            ></app-local-seo-delta>
                          }
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="organicStartedAt">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header class="divided th-space">
                          {{ 'BRAND_KEYWORD.ORGANIC_STARTED_AT' | translate }}
                        </th>
                        <td mat-cell *matCellDef="let element" class="align-text fade-in">
                          {{ element.organicStartedAt.value }}
                        </td>
                      </ng-container>

                      <tr mat-header-row *matHeaderRowDef="['empty', 'local', 'organic']"></tr>
                      <tr
                        mat-header-row
                        *matHeaderRowDef="['business', 'mapsRank', 'mapsStartedAt', 'organicRank', 'organicStartedAt']"
                      ></tr>
                      <tr
                        mat-row
                        *matRowDef="
                          let row;
                          columns: ['business', 'mapsRank', 'mapsStartedAt', 'organicRank', 'organicStartedAt']
                        "
                        class="row-slide-in"
                        (click)="onClick(row)"
                      ></tr>
                    </table>
                  </div>
                </mat-card>
              </ng-template>
              <mat-paginator #paginator [pageSize]="20" [pageSizeOptions]="[5, 10, 25, 50]"></mat-paginator>
            } @else {
              <div class="empty-keyword-page">
                <img
                  src="https://vstatic-prod.apigateway.co/business-center-client/assets/local-seo/empty-keyword-page.svg"
                  class="empty-keyword-image"
                  alt="KeywordEmptyStateImage"
                />
                <div class="title">
                  Looks like you aren’t tracking <br />
                  any keywords yet
                </div>
                <div class="subtitle">Add keywords in Local SEO to get started</div>
              </div>
            }
          }
        }
      </glxy-page-wrapper>
    </glxy-page>
    <mat-drawer role="region" #details [position]="'end'" [mode]="'side'" class="sidebar">
      <app-brand-keyword-sidebar (closeEvent)="closeSideNav()"></app-brand-keyword-sidebar>
    </mat-drawer>
  </mat-drawer-container>
</glxy-page>
