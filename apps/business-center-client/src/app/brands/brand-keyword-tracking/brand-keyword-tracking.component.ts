import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule, SortDirection } from '@angular/material/sort';
import { Component, DestroyRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCard, MatCardModule } from '@angular/material/card';
import { GalaxyTableModule } from '@vendasta/galaxy/table';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FormsModule } from '@angular/forms';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { Observable, Subject } from 'rxjs';
import { BrandKeywordTrackingService, KeywordState } from './brand-keyword-tracking.service';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { DeltaComponent } from './delta/delta.component';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BrandKeywordSidebarComponent } from './brand-keyword-sidebar/brand-keyword-sidebar.component';
import { partnerId } from '../../../globals';
import { LocalSeoKeywordsStatsCardsComponent } from './keyword-stats-cards/local-seo-keywords-stats-cards.component';
import { WhitelabelApiService } from '@vendasta/partner';
import { NavTabsComponent } from '@vendasta/business-nav';
import { BrandKeywordTableData } from './brand-keyword-tracking-common';
import { AddKeywordButtonComponent } from './add-keyword-button/add-keyword-button.component';
import { BulkAddKeywordButtonComponent } from './bulk-add-keyword-button/bulk-add-keyword-button.component';

interface SortSettings {
  active: keyof any;
  direction: SortDirection;
}

@Component({
  selector: 'app-brand-keyword-tracking',
  imports: [
    CommonModule,
    MatCard,
    GalaxyTableModule,
    MatPaginatorModule,
    TranslateModule,
    GalaxyFormFieldModule,
    FormsModule,
    MatSelect,
    MatOption,
    MatInput,
    MatIconModule,
    GalaxyPageModule,
    TimeRangePickerComponent,
    MatTableModule,
    DeltaComponent,
    BrandFilterContainerModule,
    MatSortModule,
    LocalSeoKeywordsStatsCardsComponent,
    MatSidenavModule,
    BrandKeywordSidebarComponent,
    NavTabsComponent,
    MatCardModule,
    AddKeywordButtonComponent,
    BulkAddKeywordButtonComponent,
  ],
  templateUrl: './brand-keyword-tracking.component.html',
  styleUrls: ['./brand-keyword-tracking.component.scss'],
})
export default class BrandKeywordTrackingComponent implements OnInit, OnDestroy {
  dataSource: MatTableDataSource<BrandKeywordTableData> = new MatTableDataSource([]);
  sortSettings = <SortSettings>{ active: 'mapsRank', direction: 'asc' };
  selectedLabel: string;

  private keywordChange$ = new Subject<void>();

  title: string;
  keywordState$: Observable<KeywordState>;

  brandKeywordData: BrandKeywordTableData[] = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild('details') detailsDrawer!: MatDrawer;
  @ViewChild(BrandKeywordSidebarComponent) sideBarComponent!: BrandKeywordSidebarComponent;
  sort: MatSort;
  flag: boolean;

  @ViewChild(MatSort)
  set matSort(ms: MatSort) {
    this.sort = ms;
    this.dataSource.sort = this.sort;
  }

  filteredItems = [...this.brandKeywordData];
  SEOTableData$ = this.brandKeywordTrackingService.SEOTableData$;

  constructor(
    protected brandKeywordTrackingService: BrandKeywordTrackingService,
    private destroyRef: DestroyRef,
    private whiteLabelApiService: WhitelabelApiService,
  ) {
    this.keywordState$ = this.brandKeywordTrackingService.keywordState$;
  }

  ngOnInit(): void {
    const destroy$ = this.destroyRef;

    // Populate Table Data
    this.brandKeywordTrackingService.brandKeywordData$.pipe(takeUntilDestroyed(destroy$)).subscribe((data) => {
      this.brandKeywordData = this.dataSource.data = data;
      this.dataSource.paginator = this.paginator;
      this.dataSource.sortData = (items: any[], sort: MatSort) => this.sortData(items, sort);
    });

    // Fetch Branding Information
    this.whiteLabelApiService
      .getBranding({ marketId: '', partnerId })
      .pipe(takeUntilDestroyed(destroy$))
      .subscribe((data) => (this.title = data.branding.apps['MS'].name));
  }

  ngOnDestroy(): void {
    this.keywordChange$.next();
    this.keywordChange$.complete();
  }

  protected search(event: Event): void {
    const input = event.target as HTMLInputElement;
    const query = input.value.trim().toLowerCase();

    this.filteredItems = query
      ? this.brandKeywordData.filter((item) => {
          const business = item.business.toLowerCase();
          const address = item.address.toLowerCase();
          return business.includes(query) || address.includes(query);
        })
      : [...this.brandKeywordData];

    this.dataSource.data = this.filteredItems;
  }

  protected closeSideNav() {
    this.detailsDrawer.close();
  }

  protected onClick(row: BrandKeywordTableData): void {
    this.brandKeywordTrackingService.handleRowClick(row);
    this.sideBarComponent.onSelectActiveTab('grid');
    this.detailsDrawer.open();
  }

  protected onKeywordChange(selectedLabel: string): void {
    this.brandKeywordTrackingService.handleKeywordSelection(selectedLabel);
    this.detailsDrawer.close();
  }

  protected setSortSettings($event: SortSettings): void {
    this.sortSettings = { ...$event };
  }

  private sortData<T>(data: T[], sort: MatSort): T[] {
    if (!sort?.active || !sort.direction) {
      return data;
    }
    const isAsc = sort.direction === 'asc';
    const multiplier = isAsc ? 1 : -1;
    const key = sort.active;
    const MAX = Number.MAX_SAFE_INTEGER;

    return data.sort((a, b) => {
      const aValue = a[key]?.rawValue ?? MAX;
      const bValue = b[key]?.rawValue ?? MAX;

      const aRank = a[key]?.value === 'NR' || a[key]?.value === 'NA' ? MAX : aValue;
      const bRank = b[key]?.value === 'NR' || b[key]?.value === 'NA' ? MAX : bValue;

      return multiplier * Math.sign(aRank - bRank);
    });
  }
}
