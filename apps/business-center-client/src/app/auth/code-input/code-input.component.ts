import {
  Component,
  <PERSON><PERSON>hildren,
  Query<PERSON>ist,
  ElementRef,
  AfterViewInit,
  OnInit,
  input,
  output,
  HostListener,
} from '@angular/core';

@Component({
  selector: 'app-code-input',
  templateUrl: './code-input.component.html',
  styleUrls: ['./code-input.component.scss'],
})
export class CodeInputComponent implements OnInit, AfterViewInit {
  /**
   * Parent will set the number of digits to be entered. Based on that the input fields will be created.
   * Also we will declare an empty array of the same size to hold the values. This is just to make sure
   * that if the parent sets a different number of digits, we will not have any issues.
   */
  totalDigits = input<number>(6);
  disabled = input<boolean>(false);

  codeComplete = output<string>();

  digits: string[] = [];

  @ViewChildren('inputField') inputFields!: QueryList<ElementRef>;

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    event.preventDefault();

    const pastedText = event.clipboardData?.getData('text') || '';
    // Exit early if nothing was pasted
    if (!pastedText) return;

    const currentInput = event.target as HTMLInputElement;
    const inputElements = this.inputFields.toArray();
    const startIndex = inputElements.findIndex((input) => input.nativeElement === currentInput);

    if (startIndex === -1) return;

    const charsToProcess = Math.min(pastedText.length, inputElements.length - startIndex);

    for (let i = 0; i < charsToProcess; i++) {
      const targetIndex = startIndex + i;
      const char = pastedText[i];

      inputElements[targetIndex].nativeElement.value = char;
      this.digits[targetIndex] = char;
    }

    const nextIndex = startIndex + charsToProcess;
    if (nextIndex < inputElements.length) {
      inputElements[nextIndex].nativeElement.focus();
    }

    if (!this.digits.includes('')) {
      this.codeComplete.emit(this.digits.join(''));
    }
  }

  ngOnInit() {
    this.digits = new Array(this.totalDigits()).fill('');
  }

  ngAfterViewInit() {
    if (this.inputFields.first) {
      // Focusing on the first input when the view loaded
      this.inputFields.first.nativeElement.focus();
    }
  }

  /**
   * If all inputs are empty, any attempt to focus a non-first input will redirect to the first input
   * This ensures users always start typing from the first position when the field is empty
   */
  handleInputFocus(event: FocusEvent, index: number) {
    // Only redirect focus if this isn't the first input and all fields are empty
    const allEmpty = !this.digits.some((digit) => digit !== '');

    if (allEmpty && index !== 0) {
      // If all fields are empty and trying to focus on non-first input, redirect to first
      this.inputFields.first.nativeElement.focus();
    }
  }

  onInput(event: Event, index: number) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    this.digits[index] = value;

    // focusing on the next input field
    if (index < this.totalDigits() - 1 && input.value) {
      const nextInput = this.inputFields.toArray()[index + 1];
      nextInput.nativeElement.focus();
    }

    // sending the value to the parent component
    if (this.digits.every((d) => d !== '')) {
      this.codeComplete.emit(this.digits.join(''));
    }
  }

  onKeyDown(event: KeyboardEvent, index: number) {
    const key = event.key;
    if (key === 'Backspace') {
      if (!this.digits[index] && index > 0) {
        const prevInput = this.inputFields.toArray()[index - 1];
        prevInput.nativeElement.focus();
      }
    }
  }
}
