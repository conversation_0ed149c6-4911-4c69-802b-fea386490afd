import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CodeInputComponent } from './code-input.component';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

describe('CodeInputComponent', () => {
  let component: CodeInputComponent;
  let fixture: ComponentFixture<CodeInputComponent>;
  let inputElements: DebugElement[];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodeInputComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CodeInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    inputElements = fixture.debugElement.queryAll(By.css('input'));
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should create the correct number of input fields based on totalDigits', () => {
    expect(inputElements.length).toBe(6);

    component.digits = new Array(4).fill('');
    fixture.detectChanges();

    inputElements = fixture.debugElement.queryAll(By.css('input'));
    expect(inputElements.length).toBe(4);
  });

  it('should emit codeComplete event when all digits are filled out', () => {
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');

    const testCode = ['1', '2', '3', '4', '5', '6'];

    inputElements.forEach((inputEl, index) => {
      const input = inputEl.nativeElement;
      input.value = testCode[index];

      const inputEvent = new Event('input');
      input.dispatchEvent(inputEvent);
    });

    expect(codeCompleteSpy).toHaveBeenCalledWith('123456');
  });

  it('should not emit codeComplete event if not all digits are filled out', () => {
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');

    const testCode = ['1', '2', '3', '', '5', '6'];

    inputElements.forEach((inputEl, index) => {
      const input = inputEl.nativeElement;
      input.value = testCode[index];

      const inputEvent = new Event('input');
      input.dispatchEvent(inputEvent);
    });

    expect(codeCompleteSpy).not.toHaveBeenCalled();
  });

  it('should focus on the next input element after entering a digit', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    const firstInput = inputElements[0].nativeElement;
    firstInput.value = '1';

    const inputEvent = new Event('input');
    firstInput.dispatchEvent(inputEvent);

    expect(mockFocus).toHaveBeenCalledTimes(1);
  });

  it('should not focus on next element if current input is empty', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    const firstInput = inputElements[0].nativeElement;
    firstInput.value = '';

    const inputEvent = new Event('input');
    firstInput.dispatchEvent(inputEvent);

    expect(mockFocus).not.toHaveBeenCalled();
  });

  it('should focus on previous element when delete key is pressed and current field is empty', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    component.digits[1] = '';

    const backspaceEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
    });

    const secondInput = inputElements[1].nativeElement;
    secondInput.dispatchEvent(backspaceEvent);

    expect(mockFocus).toHaveBeenCalledTimes(1);
  });

  it('should not focus on previous element when delete key is pressed but current field has a value', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    component.digits[1] = '2';

    const backspaceEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
    });

    const secondInput = inputElements[1].nativeElement;
    secondInput.dispatchEvent(backspaceEvent);

    expect(mockFocus).not.toHaveBeenCalled();
  });

  it('should be disabled when disabled input is true', () => {
    const inputElements = fixture.debugElement.queryAll(By.css('input'));

    inputElements.forEach((inputEl) => {
      inputEl.nativeElement.disabled = true;
    });
    fixture.detectChanges();

    inputElements.forEach((inputEl) => {
      expect(inputEl.nativeElement.disabled).toBeTruthy();
    });
  });

  it('should handle pasting text correctly and distribute characters to input fields', () => {
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');

    // Create a mock paste event with the test data
    const testCode = '123456';
    const pasteEvent = {
      preventDefault: jest.fn(),
      clipboardData: {
        getData: jest.fn().mockReturnValue(testCode),
      },
      target: inputElements[0].nativeElement,
    };

    // Mock focus method
    const mockFocus = jest.fn();
    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    // Trigger the paste handler
    component.onPaste(pasteEvent as unknown as ClipboardEvent);

    // Verify input values were set correctly
    for (let i = 0; i < testCode.length; i++) {
      expect(component.digits[i]).toBe(testCode[i]);
      expect(inputElements[i].nativeElement.value).toBe(testCode[i]);
    }

    // Verify preventDefault was called
    expect(pasteEvent.preventDefault).toHaveBeenCalled();

    // Verify codeComplete was emitted with the correct value
    expect(codeCompleteSpy).toHaveBeenCalledWith(testCode);

    // No focus should happen since we filled all fields
    expect(mockFocus).not.toHaveBeenCalled();
  });

  it('should handle partial paste and focus next available input', () => {
    // Create a mock paste event with partial data
    const partialCode = '123';
    const pasteEvent = {
      preventDefault: jest.fn(),
      clipboardData: {
        getData: jest.fn().mockReturnValue(partialCode),
      },
      target: inputElements[0].nativeElement,
    };

    // Reset digits
    component.digits = new Array(6).fill('');

    // Mock focus method
    const mockFocus = jest.fn();
    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    // Trigger the paste handler
    component.onPaste(pasteEvent as unknown as ClipboardEvent);

    // Verify first 3 inputs have values
    for (let i = 0; i < partialCode.length; i++) {
      expect(component.digits[i]).toBe(partialCode[i]);
      expect(inputElements[i].nativeElement.value).toBe(partialCode[i]);
    }

    // Verify remaining inputs are still empty
    for (let i = partialCode.length; i < component.digits.length; i++) {
      expect(component.digits[i]).toBe('');
    }

    // Verify preventDefault was called
    expect(pasteEvent.preventDefault).toHaveBeenCalled();

    // Verify focus was set on the input after the last pasted character
    expect(mockFocus).toHaveBeenCalledTimes(1);
    expect(mockFocus).toHaveBeenCalledWith();

    // The paste handler focuses the next available input (index 3)
    expect(mockFocus.mock.instances[0]).toBe(inputElements[partialCode.length].nativeElement);
  });

  it('should exit early when an empty string is pasted', () => {
    // Create a mock paste event with empty data
    const emptyCode = '';
    const pasteEvent = {
      preventDefault: jest.fn(),
      clipboardData: {
        getData: jest.fn().mockReturnValue(emptyCode),
      },
      target: inputElements[0].nativeElement,
    };

    // Reset digits
    component.digits = new Array(6).fill('');

    // Mock focus method
    const mockFocus = jest.fn();
    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    // Trigger the paste handler
    component.onPaste(pasteEvent as unknown as ClipboardEvent);

    // Verify inputs are still empty
    for (let i = 0; i < component.digits.length; i++) {
      expect(component.digits[i]).toBe('');
      expect(inputElements[i].nativeElement.value).toBe('');
    }

    // Verify preventDefault was called - we still prevent default even if we exit early
    expect(pasteEvent.preventDefault).toHaveBeenCalled();

    // Verify focus was not changed since we exit early
    expect(mockFocus).not.toHaveBeenCalled();

    // Verify codeComplete was not emitted
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');
    expect(codeCompleteSpy).not.toHaveBeenCalled();
  });

  it('should force focus to the first input when all fields are empty', () => {
    // Reset digits to empty
    component.digits = new Array(6).fill('');

    // Mock the focus method
    const mockFocus = jest.fn();
    inputElements[0].nativeElement.focus = mockFocus;

    // Create a mock focus event on a non-first input element
    const focusEvent = {
      target: inputElements[2].nativeElement, // Third input
    };

    // Trigger focus event
    component.handleInputFocus(focusEvent as unknown as FocusEvent, 2);

    // Verify first input was focused
    expect(mockFocus).toHaveBeenCalled();
  });

  it('should allow focus on any input when some fields have data', () => {
    // Set some data in the digits array
    component.digits = ['1', '2', '', '', '', ''];

    // Mock the focus method
    const mockFocus = jest.fn();
    inputElements[0].nativeElement.focus = mockFocus;

    // Create a mock focus event on a non-first input element
    const focusEvent = {
      target: inputElements[3].nativeElement, // Fourth input
    };

    // Trigger focus event
    component.handleInputFocus(focusEvent as unknown as FocusEvent, 3);

    // Verify first input was NOT forcefully focused
    expect(mockFocus).not.toHaveBeenCalled();
  });
});
