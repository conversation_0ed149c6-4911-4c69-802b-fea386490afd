<div class="code-input-container d-flex w-100">
  @for (digit of digits; track $index; let i = $index) {
    <input
      class="single-input-box"
      type="text"
      maxlength="1"
      pattern="[0-9]*"
      inputmode="numeric"
      (input)="onInput($event, i)"
      (keydown)="onKeyDown($event, i)"
      (focus)="handleInputFocus($event, i)"
      #inputField
      [attr.data-index]="i"
      [disabled]="disabled()"
    />
  }
</div>
