import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Filters } from '@vendasta/uikit';
import { Observable, Subject, Subscription, combineLatest, forkJoin, from } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { SocialApiService } from '../../core';
import { SocialStatsResponse } from '../../core/social.api.service';
import { MultiSeriesChartConfig, MultiSeriesChartType } from '../../shared';
import { SocialService } from '../../social/social.service';

export interface SSIDNamePair {
  ssid: string;
  name: string;
}

export interface SocialStatsChart {
  chartConfig: MultiSeriesChartConfig;
  ssid: string;
  name: string;
  socialServiceName: string;
  industryAverage: number;
  total: any;
}

@Component({
  selector: 'rm-social-statistics-card',
  templateUrl: './statistics-cards.component.html',
  styleUrls: ['./statistics-cards.component.scss', '../analytics.scss'],
  standalone: false,
})
export class StatisticsCardComponent implements OnInit, OnDestroy {
  @Input() accountGroupId: string;

  empty$: Observable<boolean>;
  charts$$: Subject<SocialStatsChart[]> = new Subject<SocialStatsChart[]>();

  filters$: Observable<Filters>;
  filterChange$: Observable<{ name: string; id: string; value: string }[]>;

  // Unix times for filter dates
  startDate = 0;
  endDate = Date.now(); // Today

  subscriptions: Subscription[] = [];

  constructor(
    private socialService: SocialService,
    private socialApiService: SocialApiService,
  ) {}

  get socialStatsCharts$(): Observable<SocialStatsChart[]> {
    const chartData$ = this.filterChange$.pipe(
      switchMap((filterFields) => {
        return forkJoin(
          filterFields.map((filterField) => {
            const socialStats$ = from(
              this.socialApiService.getSocialStats(
                this.accountGroupId,
                filterField.id,
                new Date(this.startDate),
                new Date(this.endDate),
              ),
            );
            const socialStatsCharts$ = socialStats$.pipe(
              map((response) => {
                return {
                  chartConfig: this.buildChartData(response),
                  ssid: filterField.id,
                  name: filterField.name,
                  socialServiceName: this.getSocialServiceTitleText(filterField.id),
                  industryAverage: response.data.industryAverage,
                  total: response.data.stats
                    .map((stats) => stats.data.map((datapoint) => datapoint[1]))[0]
                    .reverse()[0],
                };
              }),
            );
            return socialStatsCharts$;
          }),
        );
      }),
    );

    return chartData$ as Observable<SocialStatsChart[]>;
  }

  ngOnInit(): void {
    this.filters$ = this.socialService.statsFilters$;
    this.filterChange$ = combineLatest([this.filters$, this.socialService.statsFilterValueChanged$]).pipe(
      map(([filters]) => {
        return this.buildFilterFields(filters.activeFields);
      }),
      shareReplay(1),
    );

    this.subscriptions.push(
      this.socialStatsCharts$.subscribe((charts) => {
        this.charts$$.next(charts);
      }),
    );

    this.subscriptions.push(
      this.filterChange$.subscribe(() => {
        this.charts$$.next(null);
      }),
    );

    this.empty$ = this.socialService.statsFilters$.pipe(
      map((filters) => {
        return filters.sections.length <= 1;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  private buildFilterFields(activeFields): Array<{ name: string; id: string; value: string }> {
    let filterData = activeFields.map((filterField) => {
      if (filterField.id === 'startDate') {
        this.startDate = filterField.value;
      } else if (filterField.id === 'endDate') {
        this.endDate = filterField.value;
      }
      return { name: filterField.name, id: filterField.id, value: filterField.value };
    });
    filterData = filterData.filter((r) => this.isSSID(r.id));
    return filterData;
  }

  private buildChartData(response: SocialStatsResponse): MultiSeriesChartConfig {
    const series = response.data.stats[0].data.map((datapoint) => {
      return {
        name: new Date(datapoint[0]).toLocaleDateString(),
        value: datapoint[1],
      };
    });
    return {
      chartType: MultiSeriesChartType.Area,
      seriesData: [
        {
          name: response.data.name,
          series: series,
        },
      ],
      colorOverrides: [{ name: response.data.name, value: '#40A7F7' }],
      showLegend: false,
    };
  }

  private getSocialServiceTitleText(ssid: string): string {
    switch (ssid.split('-')[0]) {
      case 'FBP':
        return 'COMMON.FACEBOOK_LIKES';
      case 'TWU':
        return 'COMMON.X_FOLLOWERS';
      default:
        return '';
    }
  }

  private isSSID(ssid: string): boolean {
    return this.getSocialServiceTitleText(ssid) !== '';
  }
}
