import { Injectable } from '@angular/core';

import { BehaviorSubject, combineLatest as ObservableCombineLatest, EMPTY as ObservableEmpty, Observable } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  map,
  scan,
  shareReplay,
  skipWhile,
  startWith,
  switchMap,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { CheckboxFilterField, DatePickerFilterField, FilterField, Filters, FilterSection } from '@vendasta/uikit';
import {
  AppConfigService,
  ListSocialActivitesRequest,
  ListSocialActivitesResponse,
  SocialActivity,
  SocialActivityFilters,
  SocialApiService,
  SocialServiceType,
} from '../core';

export interface SocialActivityFeedData {
  activities: SocialActivity[];
}

@Injectable()
export class SocialService {
  readonly filters$: Observable<Filters>;
  readonly statsFilters$: Observable<Filters>;
  private activityFeedData$$: BehaviorSubject<SocialActivityFeedData> = new BehaviorSubject(null);
  private loadMoreActivities$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private activitiesFeedCursor$$: BehaviorSubject<string> = new BehaviorSubject(null);
  private loadingActivitiesFeed$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private activityServiceTypes = [SocialServiceType.FACEBOOK, SocialServiceType.TWITTER, SocialServiceType.LINKEDIN];
  private statsServiceTypes = [SocialServiceType.FACEBOOK, SocialServiceType.TWITTER];

  activitiesFeed$: Observable<SocialActivity[]>;

  constructor(
    private socialApi: SocialApiService,
    private config: AppConfigService,
  ) {
    this.activityServiceTypes = this.getServiceTypes();
    const accountGroupId$ = config.config$.pipe(
      distinctUntilChanged((a, b) => a.accountGroupId === b.accountGroupId),
      map((c) => c.accountGroupId),
      shareReplay(1),
    );

    this.filters$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => {
        return this.getActivityFilters(accountGroupId);
      }),
      catchError((err) => {
        console.error(err);
        return ObservableEmpty;
      }),
      shareReplay(1),
    );

    this.statsFilters$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => {
        return this.getStatsFilters(accountGroupId);
      }),
      catchError((err) => {
        console.error(err);
        return ObservableEmpty;
      }),
      shareReplay(1),
    );

    const loadActivitiesFeed$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => {
        return this.loadActivitiesFeed(accountGroupId);
      }),
      map((socialActivityFeedData) => {
        this.activityFeedData$$.next(socialActivityFeedData);
      }),
      catchError((err) => {
        console.error(err);
        return ObservableEmpty;
      }),
    );

    loadActivitiesFeed$.subscribe();

    this.activitiesFeed$ = this.activityFeedData$$.pipe(
      skipWhile((value) => value === null),
      map((r) => r.activities),
      scan(
        (acc, activities) =>
          activities
            ? [...acc.filter((old) => !activities.find((r) => r.postLink === old.postLink)), ...activities]
            : [],
        [],
      ),
      shareReplay(1),
    );
  }

  get hasMoreActivities$(): Observable<boolean> {
    return this.activitiesFeedCursor$$.asObservable().pipe(map(Boolean));
  }

  get loadingActivities$(): Observable<boolean> {
    return this.loadingActivitiesFeed$$.asObservable();
  }

  get filterValueChanged$(): Observable<FilterField<any>> {
    return this.filters$.pipe(
      switchMap((filters) => filters.fieldValuesObservable),
      startWith(null as FilterField<any>),
    );
  }

  get statsFilterValueChanged$(): Observable<FilterField<any>> {
    return this.statsFilters$.pipe(
      switchMap((filters) => filters.fieldValuesObservable),
      startWith(null as FilterField<any>),
    );
  }

  private loadActivitiesFeed(accountGroupId: string): Observable<SocialActivityFeedData> {
    return ObservableCombineLatest([
      this.filters$,
      this.filterValueChanged$.pipe(
        tap(() => {
          this.activitiesFeedCursor$$.next(null);
          this.activityFeedData$$.next({ activities: null });
        }),
      ),
      this.loadMoreActivities$$.asObservable(),
    ]).pipe(
      withLatestFrom(this.activitiesFeedCursor$$.asObservable()),
      map(([[filters], cursor]) => {
        const req: ListSocialActivitesRequest = {
          cursor,
          accountGroupId,
          filters: { ...this.activityFilters(filters) },
        };
        return req;
      }),
      distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
      tap(() => this.loadingActivitiesFeed$$.next(true)),
      switchMap((req) => this.socialApi.listActivities(req)),
      tap((res: ListSocialActivitesResponse) => {
        this.loadingActivitiesFeed$$.next(false);
        this.activitiesFeedCursor$$.next(res.cursor);
      }),
      map((res: ListSocialActivitesResponse) => {
        let activities = [];
        if (res.posts) {
          Object.keys(res.posts).forEach((serviceType) => {
            const servicePosts = res.posts[serviceType];
            const serviceActivites = [].concat(...Object.values(servicePosts)).map((activity) => {
              activity.serviceType = serviceType;
              return activity;
            });
            activities = activities.concat(serviceActivites);
          });
        }

        activities = activities.sort((a, b) => {
          if (a.datePosted > b.datePosted) {
            return -1;
          } else if (a.datePosted < b.datePosted) {
            return 1;
          } else {
            return 0;
          }
        });
        return { activities };
      }),
    );
  }

  private getActivityFilters(accountGroupId: string): Observable<Filters> {
    return this.socialApi.getSocialActivityFilters(accountGroupId).pipe(
      map((f) => {
        const sections = [
          new FilterSection({
            title: 'COMMON.FILTER_LABELS.DATE_RANGE',
            type: 'or',
            fields: [
              new DatePickerFilterField({
                id: 'startDate',
                name: 'COMMON.FILTER_LABELS.START',
                value: null,
              }),
              new DatePickerFilterField({
                id: 'endDate',
                name: 'COMMON.FILTER_LABELS.END',
                value: null,
              }),
            ],
          }),
        ];

        this.activityServiceTypes
          .filter((serviceType) => f.services[serviceType] && f.services[serviceType].length > 0)
          .forEach((serviceType) => {
            sections.push(
              new FilterSection({
                title: this.serviceFilterTitle(serviceType),
                type: 'or',
                fields: f.services[serviceType].map((s) => {
                  return new CheckboxFilterField({
                    name: s.name,
                    id: serviceType === SocialServiceType.TWITTER_EMPLOYEE_ALIAS ? s.twitterUserSsid : s.ssid,
                    value: true,
                  });
                }),
              }),
            );
          });
        return new Filters('COMMON.FILTER_LABELS.FILTERS', sections);
      }),
    );
  }

  private getStatsFilters(accountGroupId: string): Observable<Filters> {
    return this.socialApi.getSocialActivityFilters(accountGroupId).pipe(
      map((f) => {
        const sections = [
          new FilterSection({
            title: 'COMMON.FILTER_LABELS.DATE_RANGE',
            type: 'or',
            fields: [
              new DatePickerFilterField({
                id: 'startDate',
                name: 'COMMON.FILTER_LABELS.START',
                value: null,
              }),
              new DatePickerFilterField({
                id: 'endDate',
                name: 'COMMON.FILTER_LABELS.END',
                value: null,
              }),
            ],
          }),
        ];

        this.statsServiceTypes
          .filter((serviceType) => f.services[serviceType] && f.services[serviceType].length > 0)
          .forEach((serviceType) => {
            sections.push(
              new FilterSection({
                title: this.serviceFilterTitle(serviceType),
                type: 'or',
                fields: f.services[serviceType].map((s) => {
                  return new CheckboxFilterField({
                    name: s.name,
                    id: serviceType === SocialServiceType.TWITTER_EMPLOYEE_ALIAS ? s.twitterUserSsid : s.ssid,
                    value: true,
                  });
                }),
              }),
            );
          });
        return new Filters('COMMON.FILTER_LABELS.FILTERS', sections);
      }),
    );
  }

  private serviceFilterTitle(serviceType: string): string {
    switch (serviceType) {
      case SocialServiceType.FACEBOOK:
        return 'SOCIAL.FILTER_LABELS.FACEBOOK';
      case SocialServiceType.TWITTER:
        return 'SOCIAL.FILTER_LABELS.X';
      case SocialServiceType.LINKEDIN:
        return 'SOCIAL.FILTER_LABELS.LINKEDIN';
      case SocialServiceType.TWITTER_EMPLOYEE:
      case SocialServiceType.TWITTER_EMPLOYEE_ALIAS:
        return 'SOCIAL.FILTER_LABELS.TWITTER_EMPLOYEES';

      default:
        return '';
    }
  }

  private activityFilters(f: Filters): SocialActivityFilters {
    const start = f.fields.find((field) => field.id === 'startDate').value;
    const end = f.fields.find((field) => field.id === 'endDate').value;
    const dateRange = start || end ? { start, end } : null;
    const serviceFilterSections = f.sections.filter((s) => s.title !== 'COMMON.FILTER_LABELS.DATE_RANGE');
    let serviceFilterFields = [];
    serviceFilterSections.forEach((section) => (serviceFilterFields = serviceFilterFields.concat(section.fields)));
    const ssids = serviceFilterFields.filter((field) => field.value).map((field) => field.id);
    return {
      ssids,
      dateRange,
    };
  }

  getServiceTypes(): SocialServiceType[] {
    return [SocialServiceType.FACEBOOK, SocialServiceType.TWITTER, SocialServiceType.LINKEDIN];
  }

  loadMore(): void {
    this.loadMoreActivities$$.next(true);
  }
}
