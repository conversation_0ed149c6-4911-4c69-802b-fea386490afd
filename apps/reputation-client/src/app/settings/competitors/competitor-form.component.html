<form (ngSubmit)="submit()" [formGroup]="form">
  <glxy-form-field>
    <glxy-label>{{ 'COMMON.FIELD_LABELS.BUSINESS_NAME' | translate }}</glxy-label>
    <input formControlName="competitorName" matInput required />
    <glxy-error *ngIf="form.controls['competitorName'].errors?.required && form.controls['competitorName'].touched">
      {{ 'SETTINGS.COMPETITION.ADD_EDIT_FORM.BUSINESS_NAME_REQUIRED_ERROR' | translate }}
    </glxy-error>
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label>{{ 'SETTINGS.COMPETITION.ADD_EDIT_FORM.X_URL' | translate }}</glxy-label>
    <input formControlName="twitterUrl" matInput />
    <glxy-error *ngIf="form.controls['twitterUrl'].errors && form.controls['twitterUrl'].touched">
      {{ 'SETTINGS.COMPETITION.ADD_EDIT_FORM.INVALID_X_URL' | translate }}
    </glxy-error>
    <glxy-hint>https://x.com/profileId <span class="or">or</span> https://twitter.com/profileId</glxy-hint>
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label>{{ 'SETTINGS.COMPETITION.ADD_EDIT_FORM.FACEBOOK' | translate }}</glxy-label>
    <input formControlName="facebookUrl" matInput />
    <glxy-error *ngIf="form.controls['facebookUrl'].errors && form.controls['facebookUrl'].touched">
      {{ 'SETTINGS.COMPETITION.ADD_EDIT_FORM.INVALID_FACEBOOK_URL' | translate }}
    </glxy-error>
    <glxy-hint>{{ 'https://www.facebook.com/pageId' }}</glxy-hint>
  </glxy-form-field>

  <div class="actions">
    <button
      (click)="delete.emit()"
      *ngIf="competitorId"
      color="warn"
      data-action="delete-competitor"
      mat-button
      type="button"
    >
      {{ 'COMMON.ACTION_LABELS.DELETE' | translate }}
    </button>
    <button (click)="cancelled.emit()" color="primary" data-action="cancel-competitor" mat-button type="button">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
    <button
      [disabled]="form?.pristine || form?.invalid"
      color="primary"
      data-action="save-competitor"
      mat-raised-button
      type="submit"
    >
      {{ (competitorId ? 'COMMON.ACTION_LABELS.SAVE' : 'COMMON.ACTION_LABELS.ADD') | translate }}
    </button>
  </div>
</form>
