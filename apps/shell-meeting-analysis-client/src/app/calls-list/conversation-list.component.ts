import { Component } from '@angular/core';
import { MeetingAnalysisApiService } from '@vendasta/meeting-analysis';
import { NamespaceService } from '../namespace.service';
import { map, switchMap } from 'rxjs/operators';
import { AsyncPipe } from '@angular/common';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateModule } from '@ngx-translate/core';
import { RouterLink } from '@angular/router';

@Component({
  templateUrl: './conversation-list.component.html',
  imports: [AsyncPipe, GalaxyEmptyStateModule, GalaxyPageModule, TranslateModule, RouterLink],
})
export class ConversationListComponent {
  conversations$ = this.namespaceService.namespace$.pipe(
    switchMap((namespace) =>
      this.meetingAnalysisApiService.listConversations({
        filters: {
          namespace: namespace,
        },
        pagingOptions: {
          pageSize: 100,
          cursor: '',
        },
      }),
    ),
    map((response) => response?.conversations || []),
  );

  constructor(
    private readonly meetingAnalysisApiService: MeetingAnalysisApiService,
    private readonly namespaceService: NamespaceService,
  ) {}
}
