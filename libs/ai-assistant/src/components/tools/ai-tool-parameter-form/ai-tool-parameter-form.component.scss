@use 'design-tokens' as *;

:host {
  display: block;
  width: 100%;
}

.card {
  position: relative;
  border: solid 1px $field-border-color;
  border-radius: $default-border-radius;
  padding: $spacing-2;
  padding-top: calc($spacing-1 + $spacing-2);
}

.description-row {
  margin-top: $spacing-2;
}

.expand-icon {
  @include text-preset-4;
  height: $font-preset-4-size;
  width: $font-preset-4-size;
}

.object-fields {
  margin: 0;
  cursor: pointer;
}

.accordion {
  margin-top: $spacing-1;
  margin-bottom: $spacing-1;
  color: $secondary-text-color;
}

.fields-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  padding-left: $spacing-2;
  border-left: 1px solid $field-border-disabled-color;
}

.parent-parameter {
  display: flex;
  flex-direction: row;
}

.form-fields {
  flex: 1;
}

.empty {
  border-color: $field-border-disabled-color;
  color: $tertiary-text-color;
  border-style: dashed;
  background: none;
  margin-bottom: $spacing-1;
}

.remove-parameter {
  position: absolute;
  top: 0;
  right: 0;
}

.info-icon {
  font-size: inherit;
  height: inherit;
}
