<div [formGroup]="form()" [class.card]="!isNestedField()">
  @let parent = parentForm();
  <ng-container>
    @if (isNestedField()) {
      <ng-container [ngTemplateOutlet]="nestedField"></ng-container>
    } @else {
      <ng-container [ngTemplateOutlet]="parentParameter"></ng-container>
    }

    @if (form().controls.type.value === ParameterType.OBJECT || form().controls.type.value === ParameterType.ARRAY) {
      <cdk-accordion>
        <cdk-accordion-item #accordionItem="cdkAccordionItem">
          <div class="accordion">
            <p (click)="accordionItem.toggle()" class="object-fields">
              <mat-icon class="expand-icon">{{ accordionItem.expanded ? 'expand_less' : 'expand_more' }}</mat-icon>
              @if (form().controls.type.value === ParameterType.OBJECT) {
                {{ form().controls.name.value }} {{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.FIELDS' | translate }}
              } @else {
                {{ form().controls.name.value }} {{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.ARRAY_ITEMS' | translate }}
              }
            </p>
          </div>
          @if (accordionItem.expanded) {
            @if (form().controls.type.value === ParameterType.OBJECT) {
              <div class="fields-container">
                @if (form().controls.nestedParameters.controls.length === 0) {
                  <mat-card class="empty">
                    <mat-card-content>
                      {{
                        'AI_ASSISTANT.FUNCTIONS.PARAMETER.NO_FIELDS' | translate: { name: form().controls.name.value }
                      }}
                    </mat-card-content>
                  </mat-card>
                } @else {
                  @for (nestedForm of form().controls.nestedParameters.controls; track nestedForm; let idx = $index) {
                    <ai-tool-parameter-form
                      [form]="nestedForm"
                      [parentForm]="form()"
                      (remove)="handleParamRemove(idx)"
                    />
                  }
                }
              </div>
              @if (form().enabled) {
                <button mat-button color="primary" type="button" class="add-field" (click)="handleParamAdd()">
                  {{
                    form().controls.name.value
                      ? ('AI_ASSISTANT.FUNCTIONS.PARAMETER.ADD_FIELD_TO'
                        | translate: { name: form().controls.name.value })
                      : ('AI_ASSISTANT.FUNCTIONS.PARAMETER.ADD_FIELD_NO_NAME' | translate)
                  }}
                </button>
              }
            } @else {
              <div class="fields-container">
                <ai-tool-parameter-form [form]="form().controls.nestedParameters.controls[0]" [parentForm]="form()" />
              </div>
            }
          }
        </cdk-accordion-item>
      </cdk-accordion>
    }
  </ng-container>

  <!--  NG-TEMPLATES  -->
  <!--  form  for the top level parameters-->
  <ng-template #parentParameter>
    <div class="parent-parameter">
      <div class="form-fields">
        <glxy-form-row>
          <glxy-form-field class="col-xs-12 col-sm-2" bottomSpacing="none" [hideRequiredLabel]="true">
            <glxy-label>{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.KEY_LABEL' | translate }}</glxy-label>
            <input
              type="text"
              matInput
              formControlName="name"
              placeholder="{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.KEY_PLACEHOLDER' | translate }}"
            />
          </glxy-form-field>

          <glxy-form-field class="col-xs-12 col-sm-2" bottomSpacing="none" [hideRequiredLabel]="true">
            <glxy-label
              >{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.LOCATION_LABEL' | translate }}
              <mat-icon
                class="info-icon"
                [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.PARAMETER.HELP_TEXT.LOCATION' | translate"
              >
                info_outline
              </mat-icon>
            </glxy-label>
            <mat-select formControlName="location">
              @for (option of parameterLocationOptions; track option.value) {
                <mat-option [value]="option.value">
                  {{ option.label }}
                </mat-option>
              }
            </mat-select>
          </glxy-form-field>

          <glxy-form-field class="col-xs-12 col-sm-2" bottomSpacing="none" [hideRequiredLabel]="true">
            <glxy-label
              >{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.TYPE_LABEL' | translate }}
              <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.FUNCTIONS.PARAMETER.HELP_TEXT.TYPE' | translate">
                info_outline
              </mat-icon>
            </glxy-label>

            <mat-select formControlName="type">
              @for (option of parameterTypeOptions(); track option.value) {
                <mat-option [value]="option.value">
                  {{ option.label }}
                </mat-option>
              }
            </mat-select>
          </glxy-form-field>
        </glxy-form-row>
        <glxy-form-row class="description-row">
          <glxy-form-field class="col-xs-12 col-sm-12" bottomSpacing="none" [hideRequiredLabel]="true">
            <input
              type="text"
              matInput
              formControlName="description"
              placeholder="{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.DESCRIPTION_PLACEHOLDER' | translate }}"
            />
          </glxy-form-field>
        </glxy-form-row>
        @if (form().enabled) {
          <button mat-icon-button class="col-xs-12 col-sm-1 remove-parameter" (click)="remove.emit()">
            <mat-icon class="remove-icon">close</mat-icon>
          </button>
        }
      </div>
    </div>
  </ng-template>

  <!-- form for nested fields and array item-->
  <ng-template #nestedField>
    <glxy-form-row>
      @if (parent?.controls?.type?.value !== ParameterType.ARRAY) {
        <glxy-form-field class="col-xs-12 col-sm-3" bottomSpacing="none" [hideRequiredLabel]="true">
          <input
            type="text"
            matInput
            formControlName="name"
            placeholder="{{ 'AI_ASSISTANT.FUNCTIONS.PARAMETER.FIELD_NAME' | translate }}"
          />
        </glxy-form-field>
      }

      <glxy-form-field class="col-xs-12 col-sm-2" bottomSpacing="none" [hideRequiredLabel]="true">
        <mat-select formControlName="type">
          @for (option of parameterTypeOptions(); track option.value) {
            <mat-option [value]="option.value">
              {{ option.label }}
            </mat-option>
          }
        </mat-select>
      </glxy-form-field>
      <glxy-form-field class="col-xs-12 col-sm-6" bottomSpacing="none" [hideRequiredLabel]="true">
        <input
          type="text"
          matInput
          formControlName="description"
          placeholder="{{
            isArrayItem()
              ? ('AI_ASSISTANT.FUNCTIONS.PARAMETER.ARRAY_ITEM_DESCRIPTION' | translate)
              : ('AI_ASSISTANT.FUNCTIONS.PARAMETER.FIELD_DESCRIPTION' | translate)
          }}"
        />
      </glxy-form-field>
      @if (form().enabled && !isArrayItem()) {
        <button mat-icon-button class="col-xs-12 col-sm-1" (click)="remove.emit()">
          <mat-icon class="remove-icon">close</mat-icon>
        </button>
      }
    </glxy-form-row>
  </ng-template>
</div>
