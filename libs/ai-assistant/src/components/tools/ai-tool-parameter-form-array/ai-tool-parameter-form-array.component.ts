import { Component, input } from '@angular/core';
import { AiToolParameterForm, AiToolParameterFormArray } from '../../../core/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { FunctionParameterInterface } from '@vendasta/ai-assistants';
import { AiToolParameterFormComponent } from '../ai-tool-parameter-form/ai-tool-parameter-form.component';

@Component({
  selector: 'ai-tool-parameter-form-array',
  imports: [
    AiToolParameterFormComponent,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatIconModule,
  ],
  templateUrl: './ai-tool-parameter-form-array.component.html',
  styleUrl: './ai-tool-parameter-form-array.component.scss',
})
export class AiToolParameterFormArrayComponent {
  readonly forms = input.required<AiToolParameterFormArray>();
  readonly submitting = input<boolean>(false);

  handleAdd(parameter?: FunctionParameterInterface) {
    this.forms().push(new AiToolParameterForm(parameter));
  }

  handleRemove(index: number) {
    this.forms().removeAt(index);
  }
}
