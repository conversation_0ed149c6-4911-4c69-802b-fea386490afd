import { Component, computed, DestroyRef, effect, inject, Injector, signal } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { AssistantType, Namespace, PromptModuleKey } from '@vendasta/ai-assistants';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, firstValueFrom, map, switchMap, take, of } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { ImageUploadComponent } from '../image-upload/image-upload.component';
import { StickyFooterComponent } from '../../sticky-footer/sticky-footer.component';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import {
  DEFAULT_AVATAR_SVG_ICON,
  LANGCHAIN_FEATURE_FLAG,
  DEFAULT_SYSTEM_ASSISTANT_NAME,
} from '../../../core/ai-assistant.constants';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { CanComponentDeactivate } from '../../../core/services/unsaved-changes-v2.guard';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';

import { AiCapabilityAccordionComponent } from '../../capabilities/ai-capabilities-accordion/ai-capabilities-accordion.component';
import { getNamespace } from '../../../core/services/ai-assistant-utils';
import {
  AiAssistantForm,
  AiCapabilityForm,
  AiToolForm,
  getDirtyStateMap,
  KnowledgeForm,
  setupFormDirtyTracking,
} from '../../../core/forms';
import { AiToolFormComponent } from '../../tools/ai-tool-form/ai-tool-form.component';
import { AiConnectionsFormComponent } from '../../ai-connections-form/ai-connections-form.component';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { AiAssistantVoiceUsageComponent } from '../../ai-assistant-voice-usage/ai-assistant-voice-usage.component';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';
import { v4 as uuidv4 } from 'uuid';
import { AiToolFormRegistryService } from '../../../core/services/ai-tool-form-registry.service';
import { AiProfileAccordionComponent } from '../../capabilities/ai-profile-accordion/ai-profile-accordion.component';
import { StandardIds } from '@galaxy/crm/static';
import { GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { encodeFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { ApplicationKnowledgeComponent } from '@galaxy/ai-knowledge';
import { KnowledgeSource } from '@vendasta/embeddings';
import { FeatureFlagService } from '@galaxy/partner';

const FALLBACK_BACK_URL = '../..';

const BETA_ASSISTANT_TYPES = [AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST, AssistantType.ASSISTANT_TYPE_CUSTOM];

@Component({
  selector: 'ai-ai-assistant-form-v2',
  imports: [
    CommonModule,
    MatInputModule,
    MatCardModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    AiAssistantI18nModule,
    ImageUploadComponent,
    StickyFooterComponent,
    GalaxyPageModule,
    GalaxyAvatarModule,
    GalaxyTooltipModule,
    MatIconModule,
    GalaxyBadgeModule,
    AiConnectionsFormComponent,
    AiCapabilityAccordionComponent,
    GalaxyAlertModule,
    AiAssistantVoiceUsageComponent,
    AiToolFormComponent,
    AiProfileAccordionComponent,
    ApplicationKnowledgeComponent,
  ],
  providers: [AiAssistantFormV2Service, AiToolFormRegistryService],
  templateUrl: './ai-assistant-form-v2.component.html',
  styleUrl: './ai-assistant-form-v2.component.scss',
})
export class AiAssistantFormV2Component implements CanComponentDeactivate {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly accountGroupId = toSignal(this.accountGroupId$);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly partnerId = toSignal(this.partnerId$);

  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly route = inject(ActivatedRoute);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pageService = inject(PageService);
  private readonly aiIconService = inject(GalaxyAiIconService);
  private readonly router = inject(Router);
  private readonly aiAssistantFormV2Service = inject(AiAssistantFormV2Service);
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly unsavedGuard = inject(UnsavedChangesGuard);
  private readonly toolFormRegistry = inject(AiToolFormRegistryService);
  private readonly featureFlagService = inject(FeatureFlagService);

  readonly defaultAvatarIcon = DEFAULT_AVATAR_SVG_ICON;
  readonly defaultAssistantName = DEFAULT_SYSTEM_ASSISTANT_NAME;

  protected readonly isLangChainEnabled$ = this.partnerId$.pipe(
    switchMap((partnerId) =>
      this.featureFlagService.batchGetStatus(partnerId, '', [LANGCHAIN_FEATURE_FLAG]).pipe(
        map((resp) => resp[LANGCHAIN_FEATURE_FLAG]),
        catchError(() => of(false)),
      ),
    ),
    take(1),
  );
  protected readonly isLangChainEnabled = toSignal(this.isLangChainEnabled$);

  protected readonly assistantForm = toSignal(this.aiAssistantFormV2Service.form$);

  protected readonly aiAssistant = toSignal(this.aiAssistantFormV2Service.aiAssistant$);
  readonly isCustomAssistant = computed(() => {
    return (
      this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_CUSTOM ||
      this.aiAssistant()?.assistant?.type === undefined
    );
  });

  protected readonly activeToolForm = signal<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>(
    undefined,
  );
  protected readonly aiConnections = toSignal(this.aiAssistantFormV2Service.aiConnections$);
  protected readonly appId = computed(() => this.aiAssistantService.buildAppId(this.aiAssistant()?.assistant.id));

  protected readonly availableModels = toSignal(this.aiAssistantFormV2Service.availableModels);

  protected readonly loading = computed(() => {
    return this.assistantForm() === undefined;
  });

  protected readonly saving = signal(false);

  protected readonly showBetaBadge = computed(() => {
    const assistantType = this.aiAssistant()?.assistant?.type;
    return assistantType && BETA_ASSISTANT_TYPES.includes(assistantType);
  });

  protected readonly assistantSupportsVoice = computed(() => {
    return this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
  });

  protected readonly formDirty = signal(false);

  protected readonly submitDisabled = computed(() => {
    const isFormDirty = this.formDirty();
    const isSaving = this.saving();
    return !isFormDirty || isSaving;
  });

  private readonly destroyRef = inject(DestroyRef);
  private readonly injector = inject(Injector);

  constructor() {
    // Make sure the default AI icon is registered
    this.aiIconService.dummyInit();

    effect(() => {
      const form = this.assistantForm();
      if (!form) return;
      // @ts-expect-error temp
      window.foo = () => console.log('Dirty state map:', getDirtyStateMap(form));
    });

    effect(() => {
      const form = this.assistantForm();
      if (!form) return;
      setupFormDirtyTracking(form, this.destroyRef, this.injector, () => {
        this.formDirty.set(true);
        this.unsavedGuard.notifyStateChanged(true);
      });
    });
  }
  protected readonly isSalesCoach = computed(
    () => this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_SALES_COACH,
  );

  protected onImageChanged(imageUrl: string): void {
    this.assistantForm()?.controls.avatarUrl.setValue(imageUrl);
    this.assistantForm()?.controls.avatarUrl.markAsDirty();
  }

  protected updateSelectedSources(opts: { sources: KnowledgeSource[] | null; initialLoad: boolean }): void {
    const { sources, initialLoad = false } = opts;
    const form = this.assistantForm();
    if (!form) {
      return;
    }
    form.controls.knowledge.fromKnowledgeSources(sources ?? [], initialLoad);
    if (!initialLoad) {
      this.formDirty.set(true);
    }
  }

  protected handleToolFormDisplay(event: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined) {
    const { parentCapabilityForm, form: toolForm } = event || {};

    if (!toolForm) {
      // Create a new tool form if none provided
      this.activeToolForm.set({
        parentCapabilityForm: parentCapabilityForm,
        form: new AiToolForm({ metadata: { isNew: true, localId: uuidv4() } }),
      });
      return;
    }

    // Simply clone the form - we don't need to maintain form identity here
    // The registry is used when we need to ensure only one form exists across the app
    this.activeToolForm.set({
      parentCapabilityForm: parentCapabilityForm,
      form: toolForm.clone(),
    });
  }

  protected handleToolFormClosed(_toolForm: AiToolForm | undefined) {
    this.activeToolForm.set(undefined);
  }

  protected handleToolFormSubmit() {
    const submittedToolForm = this.activeToolForm();
    const assistantForm = this.assistantForm();
    if (!submittedToolForm || !assistantForm || submittedToolForm.form.disabled) {
      this.activeToolForm.set(undefined);
      return;
    }

    if (!submittedToolForm.form.valid) {
      submittedToolForm.form.markAllAsTouched();
      this.snackbarService.openErrorSnack('AI_ASSISTANT.FUNCTIONS.SNACKBAR.MISSING_FIELDS');
      return;
    }

    const parentCapabilityGoalId = submittedToolForm.parentCapabilityForm?.controls.goalId.getRawValue();
    const submittedToolId = submittedToolForm.form.controls.id.getRawValue();
    const matchedCapabilityForm = assistantForm.controls.capabilities.controls.find(
      (capabilityForm) => capabilityForm.controls.goalId.getRawValue() === parentCapabilityGoalId,
    );

    // If matchedCapabilityForm has the tool already, update it (merge), otherwise add it
    if (matchedCapabilityForm) {
      const matchedTool = matchedCapabilityForm.controls.tools.controls.find((tool) => {
        if (!submittedToolId) {
          return tool.metadata.localId && tool.metadata.localId === submittedToolForm.form.metadata.localId;
        }
        return tool.controls.id.getRawValue() === submittedToolId;
      });
      if (matchedTool) {
        matchedTool.merge(submittedToolForm.form);
      } else {
        matchedCapabilityForm.controls.tools.push(submittedToolForm.form);
      }
    } else {
      // Handle updating the tool if we don't know what capability it is on
      assistantForm.controls.capabilities.controls.forEach((capabilityForm) => {
        if (capabilityForm.controls.goalId.getRawValue() === parentCapabilityGoalId) {
          return;
        }
        const matchedTool = capabilityForm.controls.tools.controls.find((tool) => {
          if (submittedToolId) {
            return tool.controls.id.getRawValue() === submittedToolId;
          }
          return false;
        });
        if (matchedTool) {
          matchedTool.merge(submittedToolForm.form);
        }
      });
    }

    this.activeToolForm.set(undefined);
  }

  protected async submit(): Promise<void> {
    try {
      this.saving.set(true);
      await this.handleSubmit();
    } catch (e) {
      console.error(e);
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATE_ERROR');
    } finally {
      this.saving.set(false);
    }
  }

  protected async handlePersonalityRollback(capabilityId: string) {
    this.confirmationModal
      .openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_TITLE',
        message: 'AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_CONFIRMATION',
        confirmButtonText: 'AI_ASSISTANT.SETTINGS.ROLLBACK',
      })
      .subscribe(async (confirmed: boolean) => {
        if (confirmed) {
          // Fetch the original personality capability, based on whether this is a voice assistant, or regular
          const isVoiceAssistant =
            this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
          let personalityGoalId = 'DefaultPersonality';
          if (isVoiceAssistant) {
            personalityGoalId = 'DefaultVoicePersonality';
          }

          try {
            const personalityCapability = await firstValueFrom(
              this.aiAssistantService.getGoal(personalityGoalId, { globalNamespace: {} }),
            );

            const promptModuleContents = await this.aiAssistantService.getMultiPromptModuleVersions(
              personalityCapability.promptModules?.map(
                (promtModule) => new PromptModuleKey({ id: promtModule.id, namespace: promtModule.namespace }),
              ) || [],
            );

            const replacementForm = new AiCapabilityForm(
              { goal: personalityCapability },
              promptModuleContents,
              this.toolFormRegistry,
            );
            const formToReplace = this.assistantForm()?.controls.capabilities.controls.find(
              (capability) => capability.controls.goalId.getRawValue() === capabilityId,
            );
            if (!formToReplace) {
              return;
            }
            formToReplace.patchValue(replacementForm.getRawValue());
            formToReplace.markAsPristine();
            formToReplace.controls.promptModules.markAsPristine();
          } catch (err) {
            this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.PERSONALITY_ROLLBACK_ERROR');
            return;
          }
        }
      });
  }

  private async handleSubmit(): Promise<void> {
    const form = this.assistantForm();
    if (!form) {
      return;
    }

    const localNamespace = getNamespace(this.accountGroupId(), this.partnerId());
    if (!localNamespace) {
      return;
    }

    const assistantCreated = await this.handleCreateAssistant(form, localNamespace);

    const toolUpdates: Promise<unknown>[] = [];
    const toolsToUpdate = form?.controls?.capabilities?.controls?.flatMap((capability) => {
      return capability.controls.tools.controls
        ?.filter((tool) => tool.dirty && !tool.disabled)
        .map((tool) => {
          return tool.toFunction(localNamespace);
        });
    });
    const uniqueToolsToUpdate = toolsToUpdate?.filter(
      (tool, index, self) => index === self.findIndex((t) => t.id === tool.id),
    );
    uniqueToolsToUpdate
      ?.filter((tool) => tool.id)
      .forEach((tool) => toolUpdates.push(this.aiAssistantService.upsertFunction(tool)));

    const promptModuleUpdates: Promise<unknown>[] = [];
    form?.controls?.capabilities?.controls?.forEach((capability) => {
      const promptModuleForms = capability.controls.promptModules;
      promptModuleForms.controls.forEach((promptModuleForm) => {
        if (!promptModuleForm.dirty || promptModuleForm.disabled) {
          return;
        }

        if (promptModuleForm.controls.namespace?.getRawValue()?.globalNamespace) {
          // If a global prompt module is edited, then we copy it, and its containing capability, to the local namespace
          promptModuleForm.controls.namespace.setValue(localNamespace);
          capability.controls.namespace.setValue(localNamespace);
          // Clear the ID to ensure a new prompt module is created
          promptModuleForm.controls.id.setValue(null);
          capability.controls.goalId.setValue(null);

          capability.markAsDirty();
        }
        const promptModuleId = promptModuleForm.controls.id.getRawValue() ?? undefined;
        const content = promptModuleForm.controls.instructions.value;
        if (promptModuleId || content) {
          promptModuleUpdates.push(
            this.aiAssistantService
              .upsertPromptModule(
                {
                  id: promptModuleId,
                  name: capability.controls.name.getRawValue() ?? undefined,
                  namespace: promptModuleForm.controls.namespace.getRawValue() ?? localNamespace,
                },
                content ?? '',
              )
              .then((promptModuleId) => {
                // If the prompt module was created, set the ID on the form so it's available when associating the prompt module with the capability
                if (promptModuleId && !promptModuleForm.controls.id.getRawValue()) {
                  promptModuleForm.controls.id.setValue(promptModuleId);
                }
                // Mark as pristine to avoid creating redundant versions of the prompt module on additionals submits
                promptModuleForm.markAsPristine();
              }),
          );
        }
      });
    });
    await Promise.all(promptModuleUpdates);

    // Capability updates
    const capabilityUpdates: Promise<unknown>[] = [];
    {
      // Capabilities depend on tools
      await Promise.all(toolUpdates);

      const capabilitiesToUpdate = form?.controls?.capabilities?.controls?.filter(
        (capability) => capability.dirty && capability.enabled,
      );

      capabilitiesToUpdate?.forEach((capability) => {
        if (capability.value.namespace?.globalNamespace) {
          capability.controls.namespace.setValue(localNamespace);
        }
        const goal = capability.toGoal(localNamespace);
        capabilityUpdates.push(
          this.aiAssistantService.upsertCapability(goal).then((id) => {
            // If the goal was created, set the ID on the form so it's available when associating the goal with the assistant
            if (id && !capability.controls.goalId.getRawValue()) {
              capability.controls.goalId.setValue(id);
            }
          }),
        );
      });
    }

    // Assistant update (more specifically `toAssistant`) depends on capability updates if any of the capabilities are new
    // could optimize by checking if any are new and doing this async if not
    await Promise.all(capabilityUpdates);

    const assistant = form.toAssistant(localNamespace);
    assistant.namespace = localNamespace;
    await firstValueFrom(this.aiAssistantFormV2Service.upsertAssistant(assistant));

    try {
      if (form.controls.knowledge.dirty) {
        await this.aiAssistantFormV2Service.updateAssistantKnowledge(
          this.appId(),
          this.aiAssistant()?.assistant.name || '',
          form.controls.knowledge,
        );
      }
    } catch (e) {
      console.error(`Error updating assistant knowledge: ${e}`);
    }

    if (form.controls.connections.dirty) {
      await firstValueFrom(
        this.aiAssistantFormV2Service.updateConnections(
          assistant,
          this.aiConnections() || [],
          this.assistantForm()?.controls.connections.controls.map((connectionForm) => ({
            connectionId: connectionForm.metadata.id,
            enabled: connectionForm.get('connected')?.value ?? false,
          })) || [],
        ),
      );
    }

    form.markAsPristine();
    this.saving.set(false);
    if (assistantCreated) {
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_CREATED');

      this.unsavedGuard.notifyStateChanged(false);
      const url = await firstValueFrom(
        this.aiAssistantService.buildAssistantConfigurationUrl(assistant.id || '', true),
      );
      await this.router.navigateByUrl(url, { replaceUrl: true });
    } else {
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');
      this.unsavedGuard.notifyStateChanged(false);
    }
  }

  private async handleCreateAssistant(form: AiAssistantForm, namespace: Namespace): Promise<boolean> {
    const mustCreateAssistant = !form.controls.id.getRawValue();
    if (!mustCreateAssistant) {
      return false;
    }
    const defaultAiAssistant = await firstValueFrom(
      this.aiAssistantFormV2Service.upsertAssistant(
        {
          namespace,
          type: AssistantType.ASSISTANT_TYPE_CUSTOM,
        },
        true,
      ),
    );
    const assistant = defaultAiAssistant.assistant;
    form.controls.id.setValue(assistant.id);

    // Put goals assistant was created with in configurableGoals
    const goalIds = form.controls.capabilities.controls.map((capability) => capability.controls.goalId.getRawValue());
    const goalsToAdd = assistant.configurableGoals.filter((goal) => !goalIds.includes(goal.goal.id));
    if (goalsToAdd.length > 0) {
      goalsToAdd.forEach((goal) => {
        form.controls.capabilities.push(new AiCapabilityForm(goal));
      });
      form.controls.capabilities.markAsDirty();
    }

    // TODO: Put knowledge assistant was created with in knowledge
    //       This is overriding any knowledge set by the user when creating an assistant...
    const defaultKnowledge = await firstValueFrom(this.aiAssistantFormV2Service.getKnowledge(assistant.id));
    if (defaultKnowledge.length > 0) {
      defaultKnowledge.forEach((knowledge) => {
        const newKnowledgeForm = new KnowledgeForm(knowledge);
        newKnowledgeForm.markAsDirty();
        form.controls.knowledge.push(newKnowledgeForm);
      });
      form.controls.knowledge.markAsDirty();
    }

    return true;
  }

  protected back(): void {
    this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.route);
  }

  viewRecordedMeetings() {
    const filters = [
      {
        fieldId: StandardIds.ActivityMeetingStatus,
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
        values: [{ string: 'Completed' }],
      },
      {
        fieldId: StandardIds.ActivitySourceName,
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
        values: [{ string: 'Meeting Analysis' }],
      },
    ];
    const filterParam = encodeFilters(filters);
    this.router.navigate(['/crm/activities'], {
      queryParams: { filter: filterParam },
    });
  }

  canDeactivate(): boolean {
    const form = this.assistantForm();
    return !(form && form.dirty);
  }

  protected async openDeleteAssistantConfirmationModal() {
    if (!this.isCustomAssistant()) {
      // Safety net to prevent deletion of non-custom assistants - this is not supported
      return;
    }
    const assistant = this.aiAssistant()?.assistant;
    if (!assistant) {
      return;
    }
    const deleteConfirmed = await firstValueFrom(
      this.confirmationModal.openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.TITLE',
        message: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.MESSAGE',
        confirmButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CONFIRM',
        cancelButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CANCEL',
        cancelOnEscapeKeyOrBackgroundClick: true,
      }),
    );
    if (!deleteConfirmed) {
      return;
    }

    try {
      await this.aiAssistantService.deleteAssistant({
        id: assistant.id,
        namespace: assistant.namespace,
      });
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETED');
      this.back();
    } catch (e) {
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETE_ERROR');
    }
  }
}
