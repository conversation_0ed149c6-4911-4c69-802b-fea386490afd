import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AiAssistantService } from '../services/ai-assistant.service';

export const configPageVersionGuard: CanActivateFn = (route, state) => {
  const aiAssistantService = inject(AiAssistantService);
  const router = inject(Router);
  if (aiAssistantService.showConfigPageV2()) {
    // Redirect to v2 version of the page
    return router.createUrlTree([state.url.replace('/edit', '/edit-v2')]);
  }
  return true;
};
