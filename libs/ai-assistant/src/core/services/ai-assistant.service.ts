import { computed, inject, Injectable, Signal } from '@angular/core';
import {
  AI_MEETING_BOOKING,
  CONFIG_PAGE_V2_FEATURE_FLAG,
  CUSTOM_ASSISTANTS,
  DEEPGRAM_SPEAK_FEATURE_FLAG,
  NEW_CUSTOM_AI_GOALS,
  SYSTEM_ASSISTANT_ID,
} from '../ai-assistant.constants';
import {
  AiAssistant,
  AiConnection,
  ConnectionType,
  CTAction,
  MY_LISTING_CONNECTION_NAME,
} from '../interfaces/ai-assistant.interface';

import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_CONNECTIONS_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  MARKET_ID_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
  VOICE_AI_AVAILABLE_TOKEN,
} from '../tokens';
import {
  Assistant,
  AssistantApiService,
  AssistantInterface,
  AssistantType,
  Connection,
  ConnectionApiService,
  FunctionApiService,
  FunctionInterface,
  GetAssistantRequest,
  GetAssistantRequestOptions,
  GoalApiService,
  GoalKey,
  GoalType,
  ListAssistantRequest,
  ListAssistantRequestFilters,
  Namespace,
  NamespaceInterface,
  PromptModuleApiService,
  PromptModuleInterface,
  PromptModuleKey,
  PromptModuleKeyInterface,
  GoalInterface,
  ModelType,
  Model,
} from '@vendasta/ai-assistants';
import { toSignal } from '@angular/core/rxjs-interop';
import { catchError, combineLatest, firstValueFrom, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

import { FeatureFlagService, FeatureFlagStatusInterface } from '@galaxy/partner';
import { AssistantIdToAssistantType, getNamespace, globalNamespace } from './ai-assistant-utils';
import { isNotNullOrUndefined } from '@vendasta/rx-utils';
import { URLBuilderService } from './url-builder.service';

const ADDITIONAL_INSTRUCTIONS_GOAL_ID = 'AdditionalInstructions';
const ADDITIONAL_INSTRUCTIONS_PROMPT_MODULE_ID = 'AdditionalInstructions';

@Injectable()
export class AiAssistantService {
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly marketId$ = inject(MARKET_ID_TOKEN);
  readonly defaultWorkforce$ = inject(AI_DEFAULT_WORKFORCE_TOKEN);
  private readonly namespaceConfig$ = inject(NAMESPACE_CONFIG_TOKEN);

  readonly defaultConnections$ = inject(AI_DEFAULT_CONNECTIONS_TOKEN);

  private readonly functionApiService = inject(FunctionApiService);
  private readonly aiConnectionApiService = inject(ConnectionApiService);
  private readonly aiAssistantApiService = inject(AssistantApiService);
  private readonly translateService = inject(TranslateService);
  private readonly featureFlagService = inject(FeatureFlagService);
  private readonly goalApiService = inject(GoalApiService);
  private readonly promptModuleApiService = inject(PromptModuleApiService);
  private readonly urlBuilder = inject(URLBuilderService);

  private readonly $featureFlags: Signal<FeatureFlagStatusInterface> = toSignal(
    combineLatest([this.partnerId$, this.marketId$]).pipe(
      switchMap(([partnerId, marketId]) =>
        this.featureFlagService.batchGetStatus(partnerId, marketId, [
          AI_MEETING_BOOKING,
          NEW_CUSTOM_AI_GOALS,
          CUSTOM_ASSISTANTS,
          DEEPGRAM_SPEAK_FEATURE_FLAG,
          CONFIG_PAGE_V2_FEATURE_FLAG,
        ]),
      ),
      shareReplay(1),
    ),
    { initialValue: {} },
  );

  public readonly $isVoiceAIAvailable = toSignal(inject(VOICE_AI_AVAILABLE_TOKEN));

  public readonly $isCustomAssistantsEnabled = computed(() => this.$featureFlags()[CUSTOM_ASSISTANTS]);
  public readonly $isAIMeetingBookingEnabled = computed(() => this.$featureFlags()[AI_MEETING_BOOKING]);
  public readonly $isNewCustomAIGoalsEnabled = computed(() => this.$featureFlags()[NEW_CUSTOM_AI_GOALS]);
  public readonly $isDeepgramEnabled = computed(() => this.$featureFlags()[DEEPGRAM_SPEAK_FEATURE_FLAG]);
  public readonly showConfigPageV2 = computed(() => this.$featureFlags()[CONFIG_PAGE_V2_FEATURE_FLAG]);

  activeConnectionsForAssistant(assistantId: string): Observable<AiConnection[]> {
    return this.namespaceConfig$.pipe(
      map((config) => config.namespace),
      switchMap((namespace) => this.listConnectionsForAssistant(namespace, assistantId)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  getAssistant(partnerId: string, accountGroupId: string, assistantId: string): Observable<AssistantInterface> {
    const req = new GetAssistantRequest({ id: assistantId });
    const namespace = getNamespace(accountGroupId, partnerId);
    if (namespace) {
      req.namespace = namespace;
    }

    return this.aiAssistantApiService.getAssistant(req).pipe(
      map((resp) => resp.assistant),
      catchError((err) => {
        console.error('Error getting AI assistant information', err);
        throw err;
      }),
    );
  }

  getGoal(goalId: string, namespace: NamespaceInterface): Observable<GoalInterface> {
    return this.goalApiService.get({ id: goalId, namespace: namespace }).pipe(
      map((resp) => resp.goal),
      catchError((err) => {
        console.error('Error getting AI goal', err);
        throw err;
      }),
    );
  }

  getSystemAssistant(): Observable<AssistantInterface> {
    const req = new GetAssistantRequest({
      id: SYSTEM_ASSISTANT_ID,
      namespace: new Namespace({
        systemNamespace: {},
      }),
      options: new GetAssistantRequestOptions({
        skipGoalsHydration: true,
      }),
    });

    return this.aiAssistantApiService.getAssistant(req).pipe(
      map((resp) => resp.assistant),
      catchError((err) => {
        console.error('Error getting system AI assistant information', err);
        throw err;
      }),
    );
  }

  listAssistants(namespace: Namespace, type?: AssistantType): Observable<Assistant[]> {
    const req = new ListAssistantRequest();
    const filters = new ListAssistantRequestFilters();
    filters.type = type ?? AssistantType.ASSISTANT_TYPE_UNSPECIFIED;

    if (namespace) {
      filters.namespace = namespace;
    } else {
      throw new Error('No account group or partner ID found');
    }

    req.filters = filters;

    return this.aiAssistantApiService.listAssistant(req).pipe(
      map((resp) => resp.assistants),
      catchError((e) => {
        console.error('Error getting AI workforce', e);
        throw new Error('Error getting AI workforce');
      }),
    );
  }

  getDefaultAssistant(partnerId: string, accountGroupId: string, assistantId: string): Observable<AiAssistant> {
    const namespace = getNamespace(accountGroupId, partnerId);
    const type = AssistantIdToAssistantType(assistantId);

    return this.aiAssistantApiService
      .buildDefaultAssistant({
        id: assistantId,
        namespace: namespace,
        type: type,
      })
      .pipe(
        map((resp) => {
          return {
            assistant: resp.assistant,
          };
        }),
        catchError((err) => {
          throw new Error('Error getting default assistant', { cause: err });
        }),
      );
  }

  listConnections(namespace: Namespace, includeAssistantKeys: boolean): Observable<AiConnection[]> {
    return this.defaultConnections$.pipe(
      switchMap((defaultAiConnections) =>
        this.aiConnectionApiService
          .listConnections({
            filters: {
              namespace: namespace,
            },
            includeAssistantKeys: includeAssistantKeys,
          })
          .pipe(
            map((resp): AiConnection[] => {
              const connections = resp?.connections || [];
              return connections.map((connection) => ({
                connection: connection,
                isDefault: false,
                cta: this.buildConnectionCTAFromUrl(connection),
              }));
            }),
            map((aiConnections) => {
              defaultAiConnections?.forEach((defaultAiConnection) => {
                const connection = aiConnections.find((c) => {
                  if (defaultAiConnection.connection.name === MY_LISTING_CONNECTION_NAME) {
                    return c.connection.name === defaultAiConnection.connection.name;
                  }
                  return (
                    c.connection.connectionType === defaultAiConnection.connection.connectionType ||
                    c.connection.name === defaultAiConnection.connection.name
                  );
                });
                if (!connection) {
                  // Default connections will only be added to the namespace if the connection type isn't in the existing connections
                  aiConnections.push(defaultAiConnection);
                }
              });
              return aiConnections;
            }),
            catchError(() => {
              throw new Error('Error getting namespace connections');
            }),
          ),
      ),
    );
  }

  listConnectionsForAssistant(namespace: Namespace, assistantId: string): Observable<AiConnection[]> {
    return this.listConnections(namespace, true).pipe(
      map((aiConnections) => {
        const assistantType = AssistantIdToAssistantType(assistantId);

        // Only include connections that are associated with this assistant
        return aiConnections
          .filter((aiConnection) => {
            return aiConnection?.connection?.supportedAssistantTypes?.includes(assistantType);
          })
          .filter(isNotNullOrUndefined);
      }),
    );
  }

  listAllAssistantsAssociatedToConnection(
    partnerId: string | undefined,
    accountGroupId: string | undefined,
    connectionID: string,
    connectionType: ConnectionType,
    assistantType?: AssistantType,
  ): Observable<Assistant[]> {
    if (!connectionID) {
      throw new Error('Connection ID is required');
    }
    return this.aiAssistantApiService
      .listAllAssistantsAssociatedToConnection({
        connectionKey: {
          id: connectionID,
          namespace: getNamespace(accountGroupId, partnerId),
          connectionType: connectionType,
        },
        filters: {
          type: assistantType,
        },
      })
      .pipe(
        map((resp) => resp.assistants),
        catchError(() => {
          throw new Error('Error getting assistants associated to connection');
        }),
      );
  }

  buildAiKnowledgeAppConfigurationUrl(assistantId: string): Observable<string> {
    return this.urlBuilder.aiKnowledgeAppConfigurationUrl(assistantId);
  }

  buildAppId(assistantId?: string): string {
    return assistantId ? `APP-AI${assistantId}` : '';
  }

  buildAssistantConfigurationUrl(assistantId: string, v2 = false): Observable<string> {
    return this.urlBuilder.assistantConfigurationUrl(assistantId, v2);
  }

  buildCreateAssistantUrl(): Observable<string> {
    return this.urlBuilder.createAssistantUrl();
  }

  buildAiAssistantChatUrl(accountGroupId: string | undefined, assistantId: string, conversationId?: string): string {
    if (accountGroupId) {
      return `/account/location/${accountGroupId}/inbox/ai-assistant/${assistantId}/chat/${conversationId ?? ''}`;
    } else {
      return `/inbox/ai-assistant/${assistantId}/chat/${conversationId ?? ''}`;
    }
  }

  hydrateAssistantWithDefaultInfo(
    assistant: AssistantInterface,
    defaultAssistants: AiAssistant[],
    accountGroupId: string,
  ): AiAssistant | null {
    if (!assistant) return null;

    let defaultInfo: AiAssistant | undefined;
    if (assistant.type === AssistantType.ASSISTANT_TYPE_CUSTOM) {
      defaultInfo = this.getCustomAIAssistantTemplate(assistant.id ?? '', accountGroupId);
    } else {
      defaultInfo = defaultAssistants?.find((defaultAssistant) => {
        if (
          assistant.type === AssistantType.ASSISTANT_TYPE_CUSTOM &&
          assistant.type === defaultAssistant.assistant.type
        ) {
          // One default template used for all custom assistants
          return true;
        }
        return defaultAssistant.assistant.id === assistant.id;
      });
    }
    if (!defaultInfo) {
      return {
        assistant: assistant,
      };
    }
    const aiAssistant = this.cloneAssistant(defaultInfo);
    aiAssistant.isDefault = false;

    const defaultName = aiAssistant.assistant.name ?? '';
    const translatedName = this.translateService.instant(defaultName);
    if (!assistant.name) {
      assistant.name = translatedName;
    } else if (translatedName.toLowerCase() !== assistant.name.toLowerCase()) {
      aiAssistant.subtitleKey = defaultName;
    }
    if (defaultInfo?.alertInformation) {
      aiAssistant.alertInformation = defaultInfo.alertInformation;
    }
    if (defaultInfo?.hideConnections) {
      aiAssistant.hideConnections = defaultInfo.hideConnections;
    }
    aiAssistant.assistant = assistant;
    return aiAssistant;
  }

  getCustomAIAssistantTemplate(assistantId: string, accountGroupId: string): AiAssistant {
    let chatUrl = '';
    if (assistantId) {
      chatUrl = this.buildAiAssistantChatUrl(accountGroupId, assistantId);
    }

    return {
      assistant: {
        name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CUSTOM_ASSISTANT.TITLE',
        type: AssistantType.ASSISTANT_TYPE_CUSTOM,
      },
      descriptionKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CUSTOM_ASSISTANT.DESCRIPTION',
      decoration: {
        gradientColor: '#9066F7',
      },
      cta: chatUrl
        ? {
            label: 'Chat',
            action: {
              url: chatUrl,
              showButton: true,
              newTab: false,
            },
          }
        : undefined,
    };
  }

  protected buildConnectionCTAFromUrl(connection: Connection): CTAction {
    const configurationUrl = connection.configurationUrl;
    if (!configurationUrl) {
      return {};
    }

    return {
      label: 'AI_ASSISTANT.SETTINGS.SETTINGS',
      action: {
        url: configurationUrl,
        relativeRoute: !!connection.namespace?.accountGroupNamespace,
        showButton: true,
      },
    };
  }

  cloneAssistant(assistant: AiAssistant) {
    const fn = assistant.computeCTA;

    assistant.computeCTA = undefined;

    const clonedAssistant = structuredClone(assistant);
    clonedAssistant.computeCTA = fn;
    assistant.computeCTA = fn;

    return clonedAssistant;
  }

  async upsertPromptModule(promptModule: PromptModuleInterface, content?: string): Promise<string> {
    const accountGroupId = await firstValueFrom(this.accountGroupId$);
    const partnerId = await firstValueFrom(this.partnerId$);

    if (!promptModule.id) {
      const response = await firstValueFrom(
        this.promptModuleApiService.create({
          namespace: getNamespace(accountGroupId, partnerId),
          name: promptModule.name,
          description: promptModule.description,
          content: content,
        }),
      );

      return response.id;
    } else {
      await firstValueFrom(
        this.promptModuleApiService.createVersion({
          id: promptModule.id,
          namespace: getNamespace(accountGroupId, partnerId),
          content: content,
          options: {
            shouldDeploy: true,
          },
        }),
      );

      return promptModule.id;
    }
  }

  async upsertAdditionalInstructions(
    assistantId: string | undefined,
    namespace: NamespaceInterface | undefined,
    additionalInstructions: string,
  ): Promise<GoalKey> {
    if (!assistantId) {
      throw new Error('Assistant ID is required to upsert additional instructions');
    }

    if (!namespace) {
      throw new Error('Namespace is required to upsert additional instructions');
    }

    try {
      const promptModule = await firstValueFrom(
        this.promptModuleApiService
          .get({
            id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_PROMPT_MODULE_ID, assistantId),
            namespace: namespace,
          })
          .pipe(
            map((resp) => resp.promptModule),
            catchError((err) => {
              if (err.status === 404) {
                return of(null);
              }
              throw err;
            }),
          ),
      );
      if (!promptModule) {
        await firstValueFrom(
          this.promptModuleApiService
            .create({
              id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_PROMPT_MODULE_ID, assistantId),
              namespace: namespace,
              name: 'Additional Instructions',
              description: 'User specific instructions for the AI Employee',
              content: additionalInstructions,
            })
            .pipe(
              catchError((err) => {
                throw new Error('Failed to create additional instructions prompt module', { cause: err });
              }),
            ),
        );
      } else {
        await firstValueFrom(
          this.promptModuleApiService
            .createVersion({
              id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_PROMPT_MODULE_ID, assistantId),
              namespace: namespace,
              content: additionalInstructions,
              options: {
                shouldDeploy: true,
              },
            })
            .pipe(
              catchError((err) => {
                throw new Error('Failed to create additional instructions prompt module version', { cause: err });
              }),
            ),
        );
      }

      const goal = await firstValueFrom(
        this.goalApiService
          .get({ id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_GOAL_ID, assistantId), namespace: namespace })
          .pipe(
            map((resp) => resp.goal),
            catchError((err) => {
              if (err.status === 404) {
                return of(null);
              }
              throw err;
            }),
          ),
      );
      if (!goal) {
        await firstValueFrom(
          this.goalApiService
            .upsert({
              goal: {
                id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_GOAL_ID, assistantId),
                namespace: namespace,
                name: 'Additional Instructions',
                description: 'User specific goal for the AI Employee',
                type: GoalType.GOAL_TYPE_CUSTOM,
                promptModules: [
                  new PromptModuleKey({
                    id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_PROMPT_MODULE_ID, assistantId),
                    namespace: namespace,
                  }),
                ],
              },
            })
            .pipe(
              catchError((err) => {
                throw new Error('Failed to upsert additional instructions goal', { cause: err });
              }),
            ),
        );
      }
    } catch (err) {
      throw new Error('Failed to upsert additional instructions goal', { cause: err });
    }

    return new GoalKey({
      id: this.buildSpecificID(ADDITIONAL_INSTRUCTIONS_GOAL_ID, assistantId),
      namespace: namespace,
    });
  }

  private buildSpecificID(defaultID: string, assistantId: string): string {
    return `${defaultID}-${assistantId}`;
  }

  getPromptModuleVersion(id: string, namespace: NamespaceInterface, version: string): Observable<string> {
    return this.promptModuleApiService.getVersion({ id: id, namespace: namespace, version: version }).pipe(
      map((resp) => resp.promptModuleVersion),
      map((promptModuleVersion) => promptModuleVersion?.content || ''),
    );
  }

  async getMultiPromptModuleVersions(keys: PromptModuleKeyInterface[]): Promise<Record<string, string>> {
    // TODO: Once there is a getMultiDeployedVersion (without hydration), fetch this in one call

    const requests = keys.map((key) =>
      firstValueFrom(
        this.promptModuleApiService
          .getDeployedVersion({ id: key.id, namespace: key.namespace })
          .pipe(map((response) => [key.id, response.deployedPromptModuleVersion.content])),
      ),
    );
    const responses = await Promise.all(requests);
    return Object.fromEntries(responses);
  }

  getFunction(id: string): Observable<FunctionInterface> {
    return combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
      switchMap(([partnerId, accountGroupId]) =>
        this.functionApiService
          .get({ id: id, namespace: getNamespace(accountGroupId, partnerId) })
          .pipe(map((resp) => resp.function)),
      ),
    );
  }

  listFunctions(options?: {
    includeGlobal?: boolean;
  }): Observable<{ functions: FunctionInterface[]; nextCursor: string; hasMore: boolean }> {
    const { includeGlobal = false } = options || {};

    return combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
      switchMap(([partnerId, accountGroupId]) =>
        this.functionApiService
          .list({
            filters: {
              namespaces: [getNamespace(accountGroupId, partnerId), globalNamespace]
                .filter((ns) => ns !== undefined)
                .filter((ns) => ns !== globalNamespace || includeGlobal),
            },
          })
          .pipe(
            map((res) => ({
              functions: res.functions,
              nextCursor: res.metadata.nextCursor,
              hasMore: res.metadata.hasMore,
            })),
          ),
      ),
    );
  }

  async upsertFunction(aiFunction: FunctionInterface): Promise<void> {
    await firstValueFrom(
      this.functionApiService.upsert({
        function: {
          ...aiFunction,
        },
      }),
    );
  }

  async upsertCapability(capability: GoalInterface): Promise<string | undefined> {
    if (!capability) {
      throw new Error('Goal is required to upsert capability');
    }
    if (!capability.id) {
      const result = await firstValueFrom(
        this.goalApiService.create({
          goal: capability,
        }),
      );
      return result.id;
    }

    await firstValueFrom(
      this.goalApiService.update({
        goal: capability,
      }),
    );
    return undefined;
  }

  async deleteAssistant(assistant: AssistantInterface): Promise<void> {
    await firstValueFrom(
      this.aiAssistantApiService.deleteAssistant({ id: assistant.id, namespace: assistant.namespace }),
    );
  }

  listAvailableModels(): Observable<Model[]> {
    return this.aiAssistantApiService
      .listAvailableModels({
        filters: {
          type: [ModelType.TYPE_LARGE_LANGUAGE_MODEL],
        },
      })
      .pipe(map((resp) => resp.models));
  }
}
