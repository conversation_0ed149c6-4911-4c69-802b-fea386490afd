import { ConfigApiService, OwnerType, ParticipantType, SmsService } from '@vendasta/smsv2';
import { combineLatest, map, shareReplay, switchMap, catchError, of } from 'rxjs';
import { inject, Injectable } from '@angular/core';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../tokens';

@Injectable()
export class SmsDetailsService {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly smsService = inject(SmsService);
  private readonly configApiService = inject(ConfigApiService);

  public readonly phoneNumber$ = combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
    switchMap(([accountGroupId, partnerId]) => {
      let ownerId = '',
        participantType = ParticipantType.PARTICIPANT_TYPE_UNKNOWN;

      if (accountGroupId) {
        ownerId = accountGroupId;
        participantType = ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP;
      } else if (partnerId) {
        ownerId = partnerId;
        participantType = ParticipantType.PARTICIPANT_TYPE_PARTNER;
      }

      if (!ownerId) {
        return of(null);
      }

      return this.smsService
        .getAccountInfo({
          internalId: ownerId,
          type: participantType,
        })
        .pipe(
          catchError((error) => {
            console.warn('SMS account info not available:', error.message);
            return of(null);
          }),
        );
    }),
    map((accountInfo) => accountInfo?.phoneNumber || ''),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  public readonly isRoutingToVoiceAi$ = combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
    switchMap(([accountGroupId, partnerId]) => {
      const ownerId = accountGroupId || partnerId;

      if (!ownerId) {
        return of({ phoneNumberConfig: null });
      }

      return this.configApiService
        .getPhoneNumberConfig({
          ownerId,
          ownerType: accountGroupId ? OwnerType.OWNER_TYPE_ACCOUNT_GROUP : OwnerType.OWNER_TYPE_PARTNER,
        })
        .pipe(
          catchError((error) => {
            console.warn('Phone number config not available:', error.message);
            return of({ phoneNumberConfig: null });
          }),
        );
    }),
    map((response) => !!response.phoneNumberConfig?.voiceAi?.enabled),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
}
