import { FormControl, FormGroup, Validators } from '@angular/forms';
import { KnowledgeSource, WebsiteScrapeConfigCrawlMode } from '@vendasta/embeddings';
import { urlValidator } from '@vendasta/forms';
import { FormArrayWithDirtyTracking } from './forms';

export type KnowledgeFormData = {
  name: FormControl<string>;
  content: FormControl<string>;
  url: FormControl<string>;
  crawlMode: FormControl<WebsiteScrapeConfigCrawlMode>;
  raw: FormControl<KnowledgeSource>;
};

export class KnowledgeForm extends FormGroup<KnowledgeFormData> {
  constructor(knowledge?: KnowledgeSource) {
    const isWebsite = !!knowledge?.config?.websiteScrapeConfig;
    const isCustomData = !!knowledge?.config?.customDataConfig;

    super({
      name: new FormControl(knowledge?.name || '', {
        nonNullable: true,
        validators: isCustomData ? [Validators.required] : [],
      }),
      content: new FormControl(knowledge?.config?.customDataConfig?.text || '', {
        nonNullable: true,
        validators: isCustomData ? [Validators.required] : [],
      }),
      url: new FormControl(knowledge?.config?.websiteScrapeConfig?.url || '', {
        nonNullable: true,
        validators: isWebsite ? [urlValidator, Validators.required] : [],
      }),
      crawlMode: new FormControl(
        knowledge?.config?.websiteScrapeConfig?.crawlMode ||
          WebsiteScrapeConfigCrawlMode.WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_SINGLE,
        { nonNullable: true },
      ),
      raw: new FormControl(knowledge ?? new KnowledgeSource(), { nonNullable: true }),
    });
  }

  toKnowledgeSource(): KnowledgeSource {
    const formValue = this.value;
    const knowledge = formValue.raw;
    if (!knowledge) {
      return new KnowledgeSource();
    }

    const newKnowledge: KnowledgeSource = new KnowledgeSource(knowledge);
    newKnowledge.name = formValue.name ?? '';

    if (newKnowledge.config?.websiteScrapeConfig) {
      newKnowledge.config.websiteScrapeConfig.url = formValue.url ?? '';
      newKnowledge.config.websiteScrapeConfig.crawlMode =
        formValue.crawlMode ?? WebsiteScrapeConfigCrawlMode.WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_INVALID;
    }

    if (newKnowledge.config?.customDataConfig) {
      newKnowledge.config.customDataConfig.text = formValue.content ?? '';
    }

    return newKnowledge;
  }
}

export class AiKnowledgeFormArray extends FormArrayWithDirtyTracking<KnowledgeForm> {
  constructor(knowledgeSources: KnowledgeSource[]) {
    super(knowledgeSources.map((knowledge) => new KnowledgeForm(knowledge)));
  }

  toKnowledgeSources(): KnowledgeSource[] {
    return this.controls.map((knowledgeForm) => knowledgeForm.toKnowledgeSource());
  }

  fromKnowledgeSources(knowledgeSources: KnowledgeSource[], initialLoad: boolean) {
    const forms = knowledgeSources.map((knowledge) => new KnowledgeForm(knowledge));
    this.controls = forms;
    if (!initialLoad) {
      this.markAsDirty();
    }
  }
}
