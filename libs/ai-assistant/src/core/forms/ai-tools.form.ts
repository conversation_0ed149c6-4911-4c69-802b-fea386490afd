import { Form<PERSON>rray, FormControl, FormGroup, Validators, AbstractControl } from '@angular/forms';
import {
  FunctionHeaderInterface,
  FunctionInterface,
  FunctionParameterInterface,
  FunctionParameterParameterLocation,
  NamespaceInterface,
  FunctionKeyInterface,
} from '@vendasta/ai-assistants';
import { FormArrayWithDirtyTracking } from './forms';
import { AiToolFormRegistryService } from '../services/ai-tool-form-registry.service';
import { urlValidator } from './forms.validators';

// Tools
type AiToolFormMetadata = {
  namespace?: NamespaceInterface;
} & (
  | {
      isNew: false;
      localId?: undefined;
    }
  | {
      isNew: true;
      localId: string;
    }
);

export type AiToolFormControls = {
  id: FormControl<string | null>;
  namespace: FormControl<NamespaceInterface | null>;
  generatesAnswer: FormControl<boolean | null>;
  description: FormControl<string | null>;
  method: FormControl<string | null>;
  url: FormControl<string | null>;
  headers: AiToolHeaderFormArray;
  parameters: AiToolParameterFormArray;
};

type AiToolFormOptions = {
  func?: FunctionInterface;
  metadata?: AiToolFormMetadata;
};

export class AiToolForm extends FormGroup<AiToolFormControls> {
  metadata: AiToolFormMetadata;
  private _originalValues: unknown; // Type could be defined as ~ ReturnValue<FormGroup<AiToolFormControls>, "getRawValue">

  constructor(opts?: AiToolFormOptions) {
    const {
      func,
      metadata = {
        isNew: false,
        namespace: func?.namespace,
      },
    } = opts ?? {};

    super({
      id: new FormControl(
        {
          value: func?.id ?? null,
          disabled: !metadata.isNew,
        },
        [Validators.required, Validators.pattern(/^[a-zA-Z0-9_-]{1,64}$/)],
      ),
      namespace: new FormControl({ value: func?.namespace ?? null, disabled: true }),
      generatesAnswer: new FormControl({ value: func?.generatesAnswer ?? false, disabled: true }),
      description: new FormControl(func?.description ?? null, Validators.required),
      method: new FormControl(
        func?.generatesAnswer ? null : func?.methodType || 'GET',
        func?.generatesAnswer ? null : Validators.required,
      ),
      url: new FormControl(func?.url ?? null, func?.generatesAnswer ? null : [urlValidator, Validators.required]),
      headers: new AiToolHeaderFormArray(func?.headers ?? []),
      parameters: new AiToolParameterFormArray(func?.functionParameters ?? []),
    });
    this.metadata = metadata;
    this._originalValues = this.getRawValue();

    this.controls.namespace.disable();
    if (func?.managed || func?.namespace?.globalNamespace) {
      this.disable();
    }
  }

  clone(): AiToolForm {
    const clonedForm = new AiToolForm({
      func: this.toFunction(this.controls.namespace.getRawValue() ?? undefined),
      metadata: this.metadata,
    });
    clonedForm.transferDirtyStateFrom(this);

    // Store the original values for dirty state comparison
    clonedForm._originalValues = this.getRawValue();

    if (this.disabled) {
      clonedForm.disable();
    }
    if (this.controls.id.disabled) {
      clonedForm.controls.id.disable();
    }

    return clonedForm;
  }

  override get dirty(): boolean {
    // Check if any values have changed from the original
    if (this._originalValues) {
      return JSON.stringify(this.getRawValue()) !== JSON.stringify(this._originalValues);
    }
    return super.dirty;
  }

  merge(other: AiToolForm) {
    this.controls.headers.clear();
    other.controls.headers.controls.forEach((otherHeaderForm) => this.controls.headers.push(otherHeaderForm));

    this.controls.parameters.clear();
    other.controls.parameters.controls.forEach((otherParamForm) => this.controls.parameters.push(otherParamForm));

    this.metadata = { ...other.metadata };

    // Copy the values
    this.setValue(other.getRawValue());

    // Recursively transfer dirty state from other form
    this.transferDirtyStateFrom(other);
  }

  private transferDirtyStateFrom(other: AbstractControl) {
    if (other instanceof FormGroup) {
      Object.keys(other.controls).forEach((key) => {
        const thisControl = this.get(key);
        const otherControl = other.get(key);
        if (thisControl && otherControl) {
          if (otherControl.dirty) {
            thisControl.markAsDirty({ emitEvent: true });
          }
          if (otherControl instanceof FormGroup || otherControl instanceof FormArray) {
            this.transferDirtyStateFrom(otherControl);
          }
        }
      });
    }
  }

  toFunction(fallbackNamespace?: NamespaceInterface | undefined): FunctionInterface {
    const { id, description, method, url, generatesAnswer, namespace } = this.getRawValue();
    const headers = this.controls.headers.toFunctionHeaders();
    const parameters = this.controls.parameters.toFunctionParameters();

    return {
      id: id ?? undefined,
      namespace: namespace ?? fallbackNamespace ?? undefined,
      description: description ?? undefined,
      methodType: method ?? undefined,
      url: url ?? undefined,
      generatesAnswer: generatesAnswer ?? undefined,
      headers: headers.length > 0 ? headers : undefined,
      functionParameters: parameters.length > 0 ? parameters : undefined,
    };
  }

  toFunctionKey(fallbackNamespace: NamespaceInterface | undefined): FunctionKeyInterface {
    return {
      id: this.controls.id.getRawValue() ?? undefined,
      namespace: this.controls.namespace.getRawValue() ?? fallbackNamespace ?? undefined,
    };
  }
}

export class AiToolFormArray extends FormArrayWithDirtyTracking<AiToolForm> {
  constructor(functions: FunctionInterface[], registry?: AiToolFormRegistryService) {
    if (registry) {
      super(
        functions.map((func) => {
          const toolId = func.id;
          if (toolId) {
            return registry.getOrRegister(toolId, () => new AiToolForm({ func }));
          }
          return new AiToolForm({ func });
        }),
      );
    } else {
      super(functions.map((func) => new AiToolForm({ func })));
    }
  }

  toFunctions(fallbackNamespace: NamespaceInterface | undefined): FunctionInterface[] {
    return this.controls.map((toolForm) => toolForm.toFunction(fallbackNamespace));
  }

  toFunctionKeys(fallbackNamespace: NamespaceInterface | undefined): FunctionKeyInterface[] {
    return this.controls.map((toolForm) => toolForm.toFunctionKey(fallbackNamespace));
  }
}

// Tool Headers
export type AiToolHeaderFormControls = {
  name: FormControl<string | null>;
  value: FormControl<string | null>;
};

export class AiToolHeaderForm extends FormGroup<AiToolHeaderFormControls> {
  constructor(header?: FunctionHeaderInterface) {
    super({
      name: new FormControl(header?.key ?? null, Validators.required),
      value: new FormControl(header?.value ?? null, Validators.required),
    });
  }

  toFunctionHeader(): FunctionHeaderInterface {
    const { name, value } = this.getRawValue();
    return {
      key: name ?? undefined,
      value: value ?? undefined,
    };
  }
}

export class AiToolHeaderFormArray extends FormArrayWithDirtyTracking<AiToolHeaderForm> {
  constructor(headers: FunctionHeaderInterface[]) {
    super(headers.map((header) => new AiToolHeaderForm(header)));
  }

  toFunctionHeaders() {
    return this.controls.map((headerForm) => headerForm.toFunctionHeader());
  }
}

// Tool Parameters
export type AiToolParameterFormControls = {
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  type: FormControl<string | null>;
  location: FormControl<FunctionParameterParameterLocation | null>;
  automaticallyFilled: FormControl<boolean | null>;
  nestedParameters: AiToolParameterFormArray;
};

export class AiToolParameterForm extends FormGroup<AiToolParameterFormControls> {
  constructor(toolParameter?: FunctionParameterInterface) {
    const nestedParameters =
      toolParameter?.type?.toLocaleLowerCase() === 'object'
        ? toolParameter?.properties
        : toolParameter?.type?.toLowerCase() === 'array'
          ? [toolParameter?.items].filter((item) => item !== undefined)
          : [];
    super({
      name: new FormControl(toolParameter?.name ?? null, Validators.required),
      description: new FormControl(toolParameter?.description ?? null),
      type: new FormControl(toolParameter?.type ?? 'string', Validators.required),
      location: new FormControl(
        toolParameter?.location ?? FunctionParameterParameterLocation.LOCATION_BODY,
        Validators.required,
      ),
      automaticallyFilled: new FormControl(false),
      nestedParameters: new AiToolParameterFormArray(nestedParameters ?? []),
    });
  }

  clone(): AiToolParameterForm {
    const clone = new AiToolParameterForm();

    clone.controls.nestedParameters.controls.push(
      ...(this.controls.nestedParameters.controls.map((nestedForm) => nestedForm.clone()) ?? []),
    );

    clone.patchValue(this.getRawValue());
    return clone;
  }

  merge(other: AiToolParameterForm) {
    this.controls.nestedParameters.clear();
    other.controls.nestedParameters.controls.forEach((otherParamForm) =>
      this.controls.nestedParameters.push(otherParamForm),
    );
    this.patchValue(other.getRawValue());
  }

  toFunctionParameter(): FunctionParameterInterface {
    const { name, description, type, location } = this.getRawValue();
    const nestedParameters = this.controls.nestedParameters.controls.map((nestedForm) =>
      nestedForm.toFunctionParameter(),
    );

    return {
      name: name ?? undefined,
      description: description ?? undefined,
      type: type ?? undefined,
      location: location ?? undefined,
      properties: type === 'object' ? nestedParameters : undefined,
      items: type === 'array' ? nestedParameters[0] : undefined,
    };
  }
}

export class AiToolParameterFormArray extends FormArrayWithDirtyTracking<AiToolParameterForm> {
  constructor(parameters: FunctionParameterInterface[]) {
    super(parameters.map((param) => new AiToolParameterForm(param)));
  }

  toFunctionParameters() {
    return this.controls.map((paramForm) => paramForm.toFunctionParameter());
  }
}
