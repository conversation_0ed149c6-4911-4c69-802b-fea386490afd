@if (state$ | async; as state) {
  <h2 mat-dialog-title>{{ 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.TITLE' | translate }}</h2>
  <mat-dialog-content>
    <form [formGroup]="form">
      <div class="item-info-pricing-container">
        <div class="item-name-icon-container">
          <inventory-ui-item-label
            [item]="{
              itemId: input.itemId,
              name: input.name,
              iconUrl: input.iconUrl,
              editionName: input.editionName,
            }"
            [showItemId]="false"
            [showEditionName]="true"
          />
        </div>
        <billing-ui-simple-price-display
          class="center-text-vertically"
          [price]="input.revenueComponent.revenue"
          [frequency]="this.billingFrequency | revenuePeriodToGalaxyFrequency"
        />
      </div>

      <glxy-form-field>
        <glxy-label>{{ 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.BILLING_TERMS' | translate }}</glxy-label>
        <mat-select formControlName="billingTerm" (selectionChange)="handleBillingTermChange($event.value)">
          <mat-option *ngFor="let option of termOptions" [value]="option.value">
            {{ option.label | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>

      @if (input.canScheduleActivations) {
        <glxy-form-field>
          <glxy-label>{{ 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.START_DATE' | translate }}</glxy-label>
          <glxy-label-hint>{{
            'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.START_DATE_HINT' | translate
          }}</glxy-label-hint>
          <input
            matInput
            placeholder="On contract start date"
            [matDatepicker]="startDate"
            formControlName="startDate"
            [min]="tomorrow"
          />
          <mat-datepicker-toggle matSuffix [for]="startDate" />
          <mat-datepicker #startDate />
        </glxy-form-field>
      }

      <glxy-form-row *ngIf="!form.controls.billingTerm.value">
        <glxy-form-field
          [suffixText]="this.billingFrequency | formatRevenuePeriod: form.controls.nBillingPeriods.value | translate"
        >
          <glxy-label>
            {{ 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.TERM_LENGTH' | translate }}
          </glxy-label>
          <input matInput type="number" formControlName="nBillingPeriods" min="1" />
          <glxy-hint>
            @if (state.estimatedEndDate) {
              {{
                'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.ESTIMATED_END_DATE'
                  | translate: { estimatedEndDate: state.estimatedEndDate | glxyDate }
              }}
            } @else {
              {{
                'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.ESTIMATED_END_DATE'
                  | translate
                    : { estimatedEndDate: 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.NOT_APPLICABLE' | translate }
              }}
            }
          </glxy-hint>
        </glxy-form-field>
      </glxy-form-row>
      @if (input.canEditInvoices) {
        <glxy-form-field>
          <div class="toggle-container">
            <mat-slide-toggle formControlName="billable">
              <span>{{ 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.INCLUDE_IN_INVOICES' | translate }}</span>
            </mat-slide-toggle>
            <mat-hint>{{
              'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.INCLUDE_IN_INVOICES_HINT' | translate
            }}</mat-hint>
          </div>
        </glxy-form-field>
      }
    </form>
  </mat-dialog-content>
  <mat-dialog-actions class="dialog-buttons">
    <button mat-stroked-button mat-dialog-close>
      {{ 'INVENTORY_UI.COMMON.ACTIONS.CANCEL' | translate }}
    </button>
    @if (input.canAddAnotherTerm) {
      <glxy-button-group>
        <button mat-flat-button color="primary" (click)="save()" [disabled]="!form.valid">
          {{ 'INVENTORY_UI.COMMON.ACTIONS.SAVE' | translate }}
        </button>
        <button class="dropdown-icon" mat-flat-button color="primary" [matMenuTriggerFor]="saveMenu">
          <mat-icon>arrow_drop_down</mat-icon>
        </button>
      </glxy-button-group>
      <mat-menu #saveMenu="matMenu">
        <button mat-menu-item (click)="save()" [disabled]="!form.valid">
          {{ 'INVENTORY_UI.COMMON.ACTIONS.SAVE' | translate }}
        </button>
        <button mat-menu-item (click)="save(true)" [disabled]="!form.valid">
          {{ 'INVENTORY_UI.BILLING_PERIOD.SAVE_AND_ADD_ANOTHER_TERM' | translate }}
        </button>
      </mat-menu>
    } @else {
      <button mat-flat-button color="primary" (click)="save()" [disabled]="!form.valid">
        {{ 'INVENTORY_UI.COMMON.ACTIONS.SAVE' | translate }}
      </button>
    }
  </mat-dialog-actions>
}
