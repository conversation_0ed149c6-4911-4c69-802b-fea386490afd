import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { BillingUiModule } from '@vendasta/billing-ui';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyButtonGroupModule } from '@vendasta/galaxy/button-group';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { RevenuePeriod, DurationInterface, DurationPeriod } from '@vendasta/sales-orders';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { combineLatest, Observable } from 'rxjs';
import { map, shareReplay, startWith, take } from 'rxjs/operators';
import { ItemCommonModule } from '../common/item-common.module';
import { FormatRevenuePeriod } from './format-revenue-period.pipe';
import { RevenuePeriodToGalaxyFrequency } from './sales-to-galaxy-frequency.pipe';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { GalaxyDateAdapter, MY_DATE_FORMATS } from '@vendasta/galaxy/datepicker/src/date-adapter';
import { getLocale } from '@vendasta/galaxy/utility/locale';
import { addDays } from '@vendasta/galaxy/utility/date-utils';

dayjs.extend(utc);

export interface BillingTermDialogInput {
  itemId: string;
  name: string;
  editionName: string | undefined;
  iconUrl: string;
  revenueComponent: { revenue: number; period: RevenuePeriod };
  startDate: Date;
  endDate: Date | null;
  contractStartDate: Date;
  canAddAnotherTerm?: boolean;
  nonBillable?: boolean;
  canEditInvoices?: boolean;
  canScheduleActivations?: boolean;
  duration?: DurationInterface;
}

export interface BillingTermDialogOutput {
  startDate: Date | null;
  endDate: Date | null;
  billingFrequency: RevenuePeriod;
  addAnotherTerm: boolean;
  nonBillable: boolean;
  duration?: DurationInterface;
}

const MATERIAL_IMPORTS = [
  MatDialogModule,
  MatButtonModule,
  MatInputModule,
  MatDatepickerModule,
  MatNativeDateModule,
  MatSelectModule,
  MatCheckboxModule,
  ReactiveFormsModule,
  MatMenuModule,
  MatIconModule,
];
const GLXY_IMPORTS = [GalaxyAvatarModule, GalaxyFormFieldModule, GalaxyPipesModule, GalaxyButtonGroupModule];
export const IMPORTS = [
  ...MATERIAL_IMPORTS,
  ...GLXY_IMPORTS,
  CommonModule,
  BillingUiModule,
  ItemCommonModule,
  TranslateModule,
  RevenuePeriodToGalaxyFrequency,
  FormatRevenuePeriod,
  MatSlideToggle,
];

export const PROVIDERS = [
  { provide: DateAdapter, useClass: GalaxyDateAdapter },
  { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
  { provide: MAT_DATE_LOCALE, useValue: getLocale() },
];

interface State {
  estimatedEndDate: Date | null;
}

interface TermOptions {
  label: string;
  value: boolean;
}

@Component({
  selector: 'inventory-ui-billing-term-dialog',
  imports: [IMPORTS],
  providers: PROVIDERS,
  templateUrl: './billing-term-dialog.component.html',
  styleUrl: './billing-term-dialog.component.scss',
})
export class BillingTermDialogComponent implements OnInit {
  protected form!: FormGroup;
  protected tomorrow = addDays(new Date(), 1);
  protected estimatedEndDate$!: Observable<Date | null>;
  protected state$!: Observable<State>;
  protected termOptions: TermOptions[] = [
    { label: 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.INDEFINITE_TERM', value: true },
    { label: 'INVENTORY_UI.EDIT_BILLING_TERMS_DIALOG.FORM.FIXED_TERM', value: false },
  ];
  protected billingFrequency!: RevenuePeriod;

  constructor(
    private readonly dialogRef: MatDialogRef<BillingTermDialogComponent>,
    @Inject(MAT_DIALOG_DATA) protected input: BillingTermDialogInput,
    private formBuilder: FormBuilder,
  ) {}

  ngOnInit(): void {
    const startDate = this.input.startDate;
    let endDate = this.input.endDate;
    this.billingFrequency = this.input.revenueComponent.period;
    const isYearlyBilling = this.billingFrequency === RevenuePeriod.YEARLY;
    const endDateWithinYearlyBillingWindow = dayjs(endDate).isBefore(dayjs(startDate).add(1, 'year').toDate());
    if ((endDate && dayjs(endDate).isSame(0)) || dayjs(endDate).isBefore(dayjs(startDate))) {
      endDate = null;
    }
    if (isYearlyBilling && endDateWithinYearlyBillingWindow) {
      endDate = dayjs(startDate).add(1, 'year').toDate();
    }

    let nBillingPeriods = 0;
    let billIndefinitely = false;
    if (this.input.duration) {
      // If there is a duration passed in, then we are not billing indefinitely
      billIndefinitely = false;
      nBillingPeriods = this.input.duration.value || 0;
      const computeFrequency = this.billingFrequency === RevenuePeriod.YEARLY ? 'year' : 'month';
      endDate = dayjs(startDate).add(nBillingPeriods, computeFrequency).toDate();
    } else {
      [nBillingPeriods, billIndefinitely] = computeNBillingPeriods(
        startDate ?? this.input.contractStartDate,
        endDate,
        this.billingFrequency,
      );
    }

    this.form = this.formBuilder.group({
      startDate: [startDate],
      nBillingPeriods: [nBillingPeriods, Validators.required],
      billingTerm: [billIndefinitely],
      billable: [!this.input.nonBillable],
    });
    this.estimatedEndDate$ = this.initEstimatedEndDate(
      startDate ?? this.input.contractStartDate,
      nBillingPeriods,
      billIndefinitely,
    );
    this.state$ = this.estimatedEndDate$.pipe(
      map((estimatedEndDate) => {
        return { estimatedEndDate: estimatedEndDate };
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    if (this.billingFrequency === RevenuePeriod.ONETIME) {
      this.disableNBillingPeriods();
      this.disableBillIndefinitely();
      return;
    }
    if (billIndefinitely) {
      this.disableNBillingPeriods();
    }
  }

  save(addAnotherTerm = false): void {
    this.estimatedEndDate$.pipe(take(1)).subscribe((endDate) => {
      const output: BillingTermDialogOutput = {
        startDate: this.form.controls.startDate.value
          ? new Date(new Date(this.form.controls.startDate.value).setHours(7, 0, 0, 0))
          : null,
        endDate: endDate ? new Date(new Date(endDate).setHours(7, 0, 0, 0)) : null,
        billingFrequency: this.billingFrequency,
        addAnotherTerm: addAnotherTerm,
        nonBillable: !this.form.controls.billable.value,
      };

      if (endDate) {
        output.duration = {
          value: this.form.controls.nBillingPeriods.value,
          duration: this.billingFrequency === RevenuePeriod.YEARLY ? DurationPeriod.YEAR : DurationPeriod.MONTH,
        } as DurationInterface;
      }

      this.dialogRef.close(output);
    });
  }

  private initEstimatedEndDate(
    initStartDate: Date,
    initNBillingPeriods: number,
    initBillIndefinitely: boolean,
  ): Observable<Date | null> {
    const startDate$ = this.form.controls.startDate.valueChanges.pipe(startWith(initStartDate));
    const nBillingPeriods$ = this.form.controls.nBillingPeriods.valueChanges.pipe(startWith(initNBillingPeriods));
    const billIndefinitely$ = this.form.controls.billingTerm.valueChanges.pipe(startWith(initBillIndefinitely));
    return combineLatest([startDate$, nBillingPeriods$, billIndefinitely$]).pipe(
      map(([startDate, nBillingPeriods, billIndefinitely]) => {
        if (this.billingFrequency === RevenuePeriod.ONETIME || billIndefinitely) {
          return null;
        }
        if (this.billingFrequency === RevenuePeriod.YEARLY) {
          return dayjs(startDate).add(nBillingPeriods, 'year').toDate();
        }
        return dayjs(startDate).add(nBillingPeriods, 'month').toDate();
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  protected handleBillingTermChange(billIndefinitely: boolean): void {
    if (!billIndefinitely) {
      this.enableNBillingPeriods();
    } else {
      this.disableNBillingPeriods();
    }
  }

  private enableNBillingPeriods(): void {
    this.form.controls.nBillingPeriods.patchValue(1);
    this.form.controls.nBillingPeriods.enable();
    this.form.controls.nBillingPeriods.updateValueAndValidity();
  }

  private disableNBillingPeriods(): void {
    this.form.controls.nBillingPeriods.patchValue('');
    this.form.controls.nBillingPeriods.disable();
    this.form.controls.nBillingPeriods.updateValueAndValidity();
  }

  private disableBillIndefinitely(): void {
    this.form.controls.billingTerm.patchValue(false);
    this.form.controls.billingTerm.disable();
    this.form.controls.billingTerm.updateValueAndValidity();
  }
}

function computeNBillingPeriods(
  startDate: Date,
  endDate: Date | null,
  billingFrequency: RevenuePeriod,
): [number, boolean] {
  if (!endDate) {
    return [1, true]; // Bill indefinitely
  }

  const parsedStartDate = dayjs(startDate);
  const parsedEndDate = dayjs(endDate);
  if (billingFrequency === RevenuePeriod.YEARLY) {
    return [parsedEndDate.diff(parsedStartDate, 'year'), false];
  }
  return [parsedEndDate.diff(parsedStartDate, 'month'), false];
}
