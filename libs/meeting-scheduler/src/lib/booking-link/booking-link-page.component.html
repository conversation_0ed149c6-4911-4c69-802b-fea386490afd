<div class="page-container">
  <div class="top-spacer"></div>

  <div *ngIf="isUpdate && (loading$ | async)" class="mat-elevation-z1 shimmer-container">
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
  </div>

  <mat-expansion-panel *ngIf="!isUpdate || (loading$ | async) === false" class="settings-card" expanded="true">
    <mat-expansion-panel-header class="expansion-header">
      <mat-panel-title class="settings-card-title">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.TITLE' | translate }}
      </mat-panel-title>
      <mat-panel-description class="settings-card-description">
        {{ aboutCardDescription }}
      </mat-panel-description>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <glxy-form-field>
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.NAME' | translate }}
        </glxy-label>
        <input matInput required formControlName="name" />
        <glxy-error *ngIf="form?.get('name').invalid && (form.get('name')?.dirty || form.get('name')?.touched)">
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.NAME_VALIDATION_REQUIRED' | translate }}
        </glxy-error>
      </glxy-form-field>
      <span> </span>
      <glxy-form-field [prefixText]="linkPrefix$ | async">
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LINK_LABEL' | translate }}
        </glxy-label>
        <input matInput required formControlName="slug" />
        <button *ngIf="slugChanged" mat-icon-button matSuffix (click)="revertSlug()">
          <mat-icon>undo</mat-icon>
        </button>
        <glxy-error *ngIf="form?.get('slug').invalid && (form.get('slug')?.dirty || form.get('slug')?.touched)">
          {{ getSlugValidationError() }}
        </glxy-error>
      </glxy-form-field>
      <!--   Select meeting type & location   -->
      <div *ngIf="featureMeetingPhysicalLocationEnabled$ | async">
        <glxy-form-field>
          <glxy-label>
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION_LABEL' | translate }}
          </glxy-label>
          <mat-select formControlName="meetingLocation">
            <mat-option value="video"
              >{{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.VIDEO' | translate }}
            </mat-option>
            <mat-option value="inPerson"
              >{{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON_MEETING' | translate }}
            </mat-option>
          </mat-select>
        </glxy-form-field>

        <mat-card *ngIf="form.controls.meetingLocation.value === 'inPerson'" class="location-card">
          <mat-card-content>
            <glxy-form-field>
              <glxy-label style="font-weight: bold">
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.SET_LOCATION_LABEL' | translate }}
              </glxy-label>
              <mat-radio-group layout="row" formControlName="radioSelection" class="radio-group">
                <mat-radio-button value="userSite" class="radio-option">
                  {{
                    'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.USER_SITE_MEETING_LABEL'
                      | translate
                  }}
                </mat-radio-button>
                <mat-radio-button value="clientSite" class="radio-option">
                  {{
                    'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.CLIENT_SITE_MEETING_LABEL'
                      | translate
                  }}
                </mat-radio-button>
              </mat-radio-group>
            </glxy-form-field>

            <glxy-form-field
              *ngIf="form.controls.radioSelection.value === 'userSite'"
              appearance="outline"
              class="fullwidth"
            >
              <glxy-label>
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.IN_PERSON_MEETING_INPUT_FIELD' | translate }}
              </glxy-label>
              <textarea
                matAutosizeMinRows="4"
                matInput
                matTextareaAutosize
                formControlName="inPersonAddress"
                [placeholder]="'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.IN_PERSON_MEETING_PLACEHOLDER' | translate"
              ></textarea>
            </glxy-form-field>

            <glxy-form-field
              *ngIf="form.controls.radioSelection.value === 'clientSite'"
              appearance="outline"
              class="fullwidth"
            >
              <glxy-label>
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SITE_MEETING_INPUT_FIELD' | translate }}
              </glxy-label>
              <textarea
                matInput
                matTextareaAutosize
                matAutosizeMinRows="4"
                formControlName="locationGuidelines"
                maxlength="200"
                [placeholder]="
                  'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SITE_MEETING_PLACEHOLDER' | translate
                "
              ></textarea>
              <mat-hint align="end">{{ form?.get('locationGuidelines')?.value?.length || 0 }}/200</mat-hint>
            </glxy-form-field>

            <glxy-form-field
              *ngIf="
                form.controls.radioSelection.value === 'clientSite' || form.controls.radioSelection.value === 'userSite'
              "
              appearance="outline"
              class="fullwidth"
            >
              <div class="toggle-space">
                <mat-slide-toggle formControlName="isVideoLinkRequired">
                  <div class="slider-text">
                    {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.HYBRID_INPUT_FIELD' | translate }}
                  </div>
                </mat-slide-toggle>
              </div>
            </glxy-form-field>
          </mat-card-content>
        </mat-card>
      </div>

      <glxy-form-field>
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.DURATION_LABEL' | translate }}
        </glxy-label>
      </glxy-form-field>
      <mat-button-toggle-group class="duration-container" formControlName="durationSelection">
        <mat-button-toggle *ngFor="let duration of durations" class="duration" [value]="duration">
          {{ duration }}
        </mat-button-toggle>
        <mat-button-toggle class="duration" [value]="customDuration">
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CUSTOM_DURATION' | translate }}
        </mat-button-toggle>
      </mat-button-toggle-group>
      <glxy-form-field appearance="outline" class="fullwidth custom-duration" *ngIf="isCustomDuration">
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CUSTOM_DURATION_INPUT' | translate }}
        </glxy-label>
        <input matInput formControlName="durationInput" />
      </glxy-form-field>
      <glxy-error *ngIf="form?.get('durationInput').hasError('min')">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.MEETING_DURATION_ERROR' | translate }}
      </glxy-error>
      <p></p>
      <glxy-form-field appearance="outline" class="fullwidth">
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.DESCRIPTION' | translate }}
        </glxy-label>
        <textarea matAutosizeMinRows="4" matInput matTextareaAutosize formControlName="description"></textarea>
      </glxy-form-field>
      <glxy-form-field>
        <glxy-label>
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.COLOR_LABEL' | translate }}
        </glxy-label>
      </glxy-form-field>
      <div class="color-container">
        <button
          *ngFor="let color of colors"
          mat-mini-fab
          class="color-selector"
          [ngStyle]="{ 'background-color': color }"
          (click)="setColor(color)"
        >
          <mat-icon *ngIf="color === form?.get('color').value">check</mat-icon>
        </button>
      </div>
    </form>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(showHostUserSection$ | async) === true && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="true"
  >
    <mat-expansion-panel-header>
      <mat-panel-title class="settings-card-title">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.TITLE' | translate }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <form [formGroup]="form" class="team-member">
      <div *ngIf="featureMeetingRoundRobinEnabled$ | async">
        <mat-radio-group formControlName="radioChoice" class="radio-group">
          <mat-radio-button [value]="TeamEventMeetingType.ROUND_ROBIN">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.ROUND_ROBING_LABEL' | translate }}
          </mat-radio-button>
          <mat-radio-button [value]="TeamEventMeetingType.CLIENT_CHOICE">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SELECTION_LABEL' | translate }}
          </mat-radio-button>
          <mat-radio-button *ngIf="featureMeetingMultiHostEnabled$ | async" [value]="TeamEventMeetingType.MULTI_HOST">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.MULTI_HOST_LABEL' | translate }}
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <div class="selection-container">
        <div class="selection-text">
          {{ 'MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.SELECTION' | translate }}
        </div>
      </div>
      <div class="parent-container">
        <glxy-form-field>
          <mat-chip-grid #teamMemberChipGrid aria-label="Team Member selection">
            <ng-container *ngFor="let teamMember of selectedTeamMembers; let i = index">
              <mat-chip-row *ngIf="hostUserIds?.includes(teamMember.userId)" (removed)="removeTeamMember(i)">
                <div class="team-member-list">
                  <span>
                    {{ teamMember.displayName | titlecase }}
                  </span>

                  <mat-icon *ngIf="!teamMember.isConfigured" class="warn-icon">warning</mat-icon>
                </div>
                <button matChipRemove [attr.aria-label]="'remove ' + teamMember.displayName">
                  <mat-icon>cancel</mat-icon>
                </button>
              </mat-chip-row>
            </ng-container>

            <input
              #teamMemberInput
              [placeholder]="'MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.TEAM_MEMBER_PLACEHOLDER' | translate"
              [formControl]="teamMemberCtrl"
              [matChipInputFor]="teamMemberChipGrid"
              [matAutocomplete]="auto"
              [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
              (matChipInputTokenEnd)="addTeamMemberFromInput($event)"
            />
          </mat-chip-grid>
          <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selectedTeamMember($event)">
            <mat-option *ngFor="let teamMember of filteredTeamMembers | async" [value]="teamMember">
              <div class="team-member-list">
                <span>
                  {{ teamMember.displayName | titlecase }}
                </span>
                <mat-icon *ngIf="!teamMember.isConfigured" class="warn-icon">warning</mat-icon>
              </div>
            </mat-option>
          </mat-autocomplete>
          <glxy-error
            *ngIf="
              teamMemberCtrl.touched &&
              selectedTeamMembers.length < 1 &&
              form.controls.radioChoice.value !== TeamEventMeetingType.MULTI_HOST
            "
          >
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBERS_VALIDATION_REQUIRED' | translate: { count: 'one' } }}
          </glxy-error>
          <glxy-error
            *ngIf="
              teamMemberCtrl.touched &&
              selectedTeamMembers.length < 2 &&
              form.controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST
            "
          >
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBERS_VALIDATION_REQUIRED' | translate: { count: 'two' } }}
          </glxy-error>
          <glxy-error
            *ngIf="
              teamMemberCtrl.touched &&
              selectedTeamMembers.length > 5 &&
              form.controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST
            "
          >
            {{
              'MEETING_SCHEDULER.BOOKING_LINK.MAX_MULTI_HOST_TEAM_MEMBERS_VALIDATION_REQUIRED'
                | translate: { count: 'five' }
            }}
          </glxy-error>
        </glxy-form-field>
      </div>

      <glxy-alert *ngIf="(showAlert$ | async) === true" type="warning">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.INDICATOR' | translate }}
      </glxy-alert>
    </form>
  </mat-expansion-panel>

  <mat-expansion-panel
    data-testid="availability-selection-panel"
    *ngIf="(featureMeetingExpansionEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card day-card"
    expanded="{{ form.controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST }}"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="Availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.PROMPT' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <div>
      <meeting-scheduler-settings-advanced-availability-card
        [meetingId]="meetingId"
        [selectedHosts]="selectedTeamMembers"
        [hostAvailability]="hostAvailability$"
        [isMultiHost]="form.controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST"
        [displayAvailabilitySelectionError]="displayAvailabilitySelectionError"
        (hasSelectedAvailability)="hasSelectedAvailability($event)"
      ></meeting-scheduler-settings-advanced-availability-card>
    </div>
  </mat-expansion-panel>

  <mat-expansion-panel *ngIf="!isUpdate || (loading$ | async) === false" class="settings-card" expanded="false">
    <mat-expansion-panel-header class="expansion-header">
      <mat-panel-title class="settings-card-title">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TITLE' | translate }}
      </mat-panel-title>
      <mat-panel-description class="settings-card-description">
        {{ questionsCardDescription }}
      </mat-panel-description>
    </mat-expansion-panel-header>
    <glxy-form-field>
      <glxy-label>
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.DEFAULT.QUESTIONS_SECTION' | translate }}
      </glxy-label>
    </glxy-form-field>
    <mat-accordion class="deafult-question-accordion">
      <mat-expansion-panel disabled>
        <mat-expansion-panel-header class="custom-panel-header">
          <mat-panel-title class="default-form-field-title">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_FIRST_NAME' | translate }}
          </mat-panel-title>
          <mat-panel-description class="default-form-field-description">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT' | translate }}
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
      <mat-expansion-panel disabled>
        <mat-expansion-panel-header class="custom-panel-header">
          <mat-panel-title class="default-form-field-title nowrap">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_LAST_NAME' | translate }}
          </mat-panel-title>
          <mat-panel-description class="default-form-field-description">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT' | translate }}
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
      <mat-expansion-panel disabled>
        <mat-expansion-panel-header class="custom-panel-header">
          <mat-panel-title class="default-form-field-title nowrap">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_EMAIL' | translate }}
          </mat-panel-title>
          <mat-panel-description class="default-form-field-description">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_EMAIL' | translate }}
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
      <mat-expansion-panel disabled>
        <mat-expansion-panel-header class="custom-panel-header">
          <mat-panel-title class="default-form-field-title nowrap">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_PHONE_NUMBER' | translate }}
          </mat-panel-title>
          <mat-panel-description class="default-form-field-description">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_PHONE_NUMBER' | translate }}
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
      <mat-expansion-panel disabled>
        <mat-expansion-panel-header class="custom-panel-header">
          <mat-panel-title class="default-form-field-title nowrap">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_COMMENTS' | translate }}
          </mat-panel-title>
          <mat-panel-description class="default-form-field-description">
            {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT_AREA' | translate }}
          </mat-panel-description>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    </mat-accordion>

    <glxy-form-field>
      <glxy-label>
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.Custom.QUESTIONS_SECTION' | translate }}
      </glxy-label>
    </glxy-form-field>
    <mat-accordion
      #accordion="matAccordion"
      cdkDropList
      (cdkDropListDropped)="changeFieldOrder($event)"
      multi="true"
      class="drag-boundary"
    >
      <ng-container *ngFor="let field of form?.controls.customFields['controls']; let i = index">
        <mat-expansion-panel [formGroup]="field" cdkDrag cdkDragLockAxis="y" cdkDragBoundary=".drag-boundary">
          <mat-expansion-panel-header>
            <mat-panel-title class="form-field-title">
              <mat-icon cdkDragHandle class="handle-icon">drag_indicator</mat-icon>
              <div class="form-field-title-text">
                <span>{{ field.value.label }}{{ field.value.required ? '*' : '' }}</span>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div class="form-field-content">
            <div class="form-field-options">
              <mat-checkbox formControlName="required">
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.REQUIRED' | translate }}
              </mat-checkbox>
            </div>
            <mat-form-field appearance="outline" class="form-field-spacing">
              <mat-label>
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE' | translate }}
              </mat-label>
              <mat-select required formControlName="type">
                <mat-option *ngFor="let fieldType of fieldTypeKeys" [value]="fieldType">
                  {{ describeFieldType(fieldType) }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>
                {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.LABEL' | translate }}
              </mat-label>
              <input matInput required formControlName="label" />
              <mat-error *ngIf="field.controls.label.invalid">
                {{ fieldLabelErrorMessage(field.controls.label.value) }}
              </mat-error>
            </mat-form-field>
          </div>
          <mat-action-row>
            <button mat-stroked-button color="warn" (click)="deleteField(i)">
              {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DELETE' | translate }}
            </button>
          </mat-action-row>
        </mat-expansion-panel>
      </ng-container>
      <div class="button-container">
        <button
          mat-stroked-button
          color="primary"
          class="add-field-button"
          [class.active]="!form?.controls?.customFields.invalid"
          [disabled]="form?.controls?.customFields.invalid"
          (click)="addFormField()"
        >
          <span class="plus-sign">+</span> {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ADD' | translate }}
        </button>
      </div>
    </mat-accordion>

    <div *ngIf="form?.controls?.customFields.invalid" class="custom-fields-errors">
      <mat-error *ngIf="fieldsHaveError('noDuplicates')">
        {{ 'MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_DUPLICATES' | translate }}
      </mat-error>
    </div>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureEmailTemplateEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description>
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL.DESCRIPTION' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <glxy-form-field>
        <glxy-label>
          {{ 'MEETING_SCHEDULER.CUSTOMIZE_INVITATION_EMAIL.SUBJECT' | translate }}
        </glxy-label>
        <input
          matInput
          formControlName="emailSubject"
          [placeholder]="
            'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL_SUBJECT.PLACEHOLDER' | translate
          "
        />
      </glxy-form-field>
      <glxy-form-field appearance="outline" class="fullwidth">
        <glxy-label>
          {{ 'MEETING_SCHEDULER.CUSTOMIZE_INVITATION_EMAIL.DESCRIPTION' | translate }}
        </glxy-label>
        <textarea
          matAutosizeMinRows="4"
          matInput
          matTextareaAutosize
          formControlName="emailDescription"
          [placeholder]="
            'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL_DESCRIPTION.PLACEHOLDER' | translate
          "
        ></textarea>
      </glxy-form-field>
    </form>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureMeetingExpansionEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.DESCRIPTION' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <mat-button-toggle-group formControlName="availabilityIncrement" [value]="null">
        <mat-button-toggle *ngFor="let option of increments" class="toggle-increments" [value]="option.seconds">
          {{ option.label }}
        </mat-button-toggle>
      </mat-button-toggle-group>
    </form>
    <glxy-form-field class="availability-hint">
      <glxy-label>
        {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.HINT' | translate }}
      </glxy-label>
    </glxy-form-field>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureMeetingBeforeBufferEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION_BEFORE.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="Availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION_BEFORE.DESCRIPTION' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <mat-button-toggle-group formControlName="bufferDurationBeforeMeeting">
        <mat-button-toggle *ngFor="let buffer of buffers" class="toggle-increments" [value]="buffer.seconds">
          {{ buffer.label }}
        </mat-button-toggle>
      </mat-button-toggle-group>
    </form>
    <glxy-form-field class="availability-hint">
      <glxy-label>
        {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.HINT' | translate }}
      </glxy-label>
    </glxy-form-field>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureMeetingExpansionEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.DESCRIPTION' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <mat-button-toggle-group formControlName="bufferDurationAfterMeeting">
        <mat-button-toggle *ngFor="let buffer of buffers" class="toggle-increments" [value]="buffer.seconds">
          {{ buffer.label }}
        </mat-button-toggle>
      </mat-button-toggle-group>
    </form>
    <glxy-form-field class="availability-hint">
      <glxy-label>
        {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.HINT' | translate }}
      </glxy-label>
    </glxy-form-field>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureMeetingExpansionEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.NOTICE_TIME.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.NOTICE_TIME.DESCRIPTION' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <mat-button-toggle-group formControlName="noticeTime">
        <mat-button-toggle
          *ngFor="let advNotice of advanceNotice"
          class="toggle-increments"
          [value]="advNotice.seconds"
        >
          {{ advNotice.label }}
        </mat-button-toggle>
      </mat-button-toggle-group>
    </form>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureMeetingEmbedEnabled$ | async) && isUpdate && (loading$ | async) === false"
    class="settings-card day-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ADD_TO_WEBSITE.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ADD_TO_WEBSITE.PROMPT' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <div>
      <meeting-scheduler-settings-add-to-website-card
        [isUpdate]="isUpdate"
        [calendarId]="calendarId"
        [name]="eventTypeName"
        [id]="meetingTypeId"
      ></meeting-scheduler-settings-add-to-website-card>
    </div>
  </mat-expansion-panel>

  <mat-expansion-panel
    *ngIf="(featureDateRangeEnabled$ | async) && (!isUpdate || (loading$ | async) === false)"
    class="settings-card day-card"
    expanded="false"
  >
    <mat-expansion-panel-header class="expansion-header">
      <div class="title-description">
        <mat-panel-title class="settings-card-title">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.DATE_RANGE.TITLE' | translate }}
        </mat-panel-title>
        <mat-panel-description class="availability-card-description">
          {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.DATE_RANGE.PROMPT' | translate }}
        </mat-panel-description>
      </div>
    </mat-expansion-panel-header>
    <form [formGroup]="form">
      <div class="range-type-section">
        <mat-radio-group formControlName="dateRangeType" class="range-type-group">
          <!-- Relative Date Range Option -->
          <div class="radio-option">
            <mat-radio-button [value]="DateRangeType.RELATIVE" class="radio-button">
              <div class="controls-inline">
                <glxy-form-field class="value-field" hideRequiredLabel="true">
                  <input matInput type="number" formControlName="relativeValue" [min]="1" class="relative-input" />
                </glxy-form-field>

                <glxy-form-field class="unit-field" hideRequiredLabel="true">
                  <mat-select formControlName="relativeUnit" class="unit-select">
                    <mat-option *ngFor="let unit of timeUnits" [value]="unit.value">
                      {{ unit.label }}
                    </mat-option>
                  </mat-select>
                </glxy-form-field>

                <span class="suffix-text">into the future</span>
              </div>
            </mat-radio-button>

            <!-- Error messages for relative option -->
            <div class="radio-option-errors" *ngIf="isRelativeMode">
              <glxy-error
                *ngIf="
                  form.get('relativeValue')?.invalid &&
                  (form.get('relativeValue')?.dirty || form.get('relativeValue')?.touched)
                "
              >
                {{ relativeValueError }}
              </glxy-error>
              <glxy-error
                *ngIf="
                  form.get('relativeUnit')?.invalid &&
                  (form.get('relativeUnit')?.dirty || form.get('relativeUnit')?.touched)
                "
              >
                Unit is required
              </glxy-error>
            </div>
          </div>

          <!-- Custom Date Range Option -->
          <div class="radio-option">
            <mat-radio-button [value]="DateRangeType.CUSTOM" class="radio-button">
              <div class="controls-inline">
                <glxy-form-field class="date-field" hideRequiredLabel="true">
                  <input
                    matInput
                    [matDatepicker]="startPicker"
                    formControlName="startDate"
                    placeholder="Select start date"
                    [min]="today"
                    readonly
                  />
                  <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                  <mat-datepicker #startPicker></mat-datepicker>
                </glxy-form-field>

                <span class="date-label">to</span>
                <glxy-form-field class="date-field" hideRequiredLabel="true">
                  <input
                    matInput
                    [matDatepicker]="endPicker"
                    formControlName="endDate"
                    placeholder="Select end date"
                    [min]="today"
                    readonly
                  />
                  <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                  <mat-datepicker #endPicker></mat-datepicker>
                </glxy-form-field>
              </div>
            </mat-radio-button>

            <!-- Error messages for custom option -->
            <div class="radio-option-errors" *ngIf="isCustomMode">
              <glxy-error
                *ngIf="
                  form.get('startDate')?.invalid && (form.get('startDate')?.dirty || form.get('startDate')?.touched)
                "
              >
                {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.START_DATE_REQUIRED' | translate }}
              </glxy-error>
              <glxy-error
                *ngIf="form.get('endDate')?.invalid && (form.get('endDate')?.dirty || form.get('endDate')?.touched)"
              >
                {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.END_DATE_REQUIRED' | translate }}
              </glxy-error>
              <glxy-error *ngIf="hasDateRangeError">
                {{ 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.INVALID_RANGE' | translate }}
              </glxy-error>
            </div>
          </div>
        </mat-radio-group>
      </div>
    </form>
  </mat-expansion-panel>

  <glxy-sticky-footer [rightAligned]="true">
    <button mat-flat-button color="primary" type="submit" (click)="createOrUpdateMeetingType()">
      {{ buttonText }}
    </button>
  </glxy-sticky-footer>
</div>
