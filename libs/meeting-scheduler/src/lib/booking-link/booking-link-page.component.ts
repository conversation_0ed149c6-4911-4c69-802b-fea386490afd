import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatAccordion } from '@angular/material/expansion';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  Calendar,
  DateRangeType,
  durationStringToMinutes,
  EventTypeDateRangeInterface,
  FormFieldType,
  HostUser,
  MeetingLocationType,
  MeetingType,
  MeetingTypeFormField,
  Preferences,
  TeamEventMeetingType,
  RelativeTimeUnit,
} from '@vendasta/meetings';
import camelCase from 'lodash/camelCase';
import { combineLatest, from, Observable, Subscription } from 'rxjs';
import { first, map, mapTo, shareReplay, startWith, switchMap, take, tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { MeetingSchedulerStoreService } from '../data-providers/meeting-scheduler-store.service';
import {
  FEATURE_GROUPS_AND_SERVICES_TOKEN,
  FEATURE_MEETING_BEFORE_BUFFER_TOKEN,
  FEATURE_MEETING_DATE_RANGE_TOKEN,
  FEATURE_MEETING_EMAIL_TEMPLATE_TOKEN,
  FEATURE_MEETING_EMBED_CODE_TOKEN,
  FEATURE_MEETING_EXPANSION_TOKEN,
  FEATURE_MEETING_PHYSICAL_LOCATION_TOKEN,
  FEATURE_MEETING_ROUNDROBIN_TOKEN,
  HOST_ID_TOKEN,
  MEETING_LIST_PAGE_TOKEN,
  MEETING_TYPE_LIST_VIEW_PAGE_TOKEN,
  FEATURE_MEETING_MULTIHOST_TOKEN,
} from '../data-providers/providers';
import { UpdateBookingLinkConfirmationDialogComponent } from './update-booking-link-confirmation-dialog/update-booking-link-confirmation-dialog.component';
import { DailyAvailability } from '../interface';
import { ScheduleSettingsViewService } from '../schedule-settings/schedule-settings-view.service';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { BusinessNavConfigService } from '@vendasta/business-nav';
import { WhitelabelService } from '@galaxy/partner';
import { EVENTS, POSTHOG_ACTIONS, POSTHOG_CATEGORIES, POSTHOG_KEYS } from '../constants';
import { MatChipGrid, MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { AvailabilityRuleList } from '@vendasta/meetings/lib/_internal/objects/meeting-host';

interface DurationOption {
  seconds: number;
  label: string;
}

@Component({
  selector: 'meeting-scheduler-booking-link',
  templateUrl: './booking-link-page.component.html',
  styleUrls: ['./booking-link-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BookingLinkPageComponent implements OnInit, OnDestroy {
  hostPreferences: Preferences;
  address: string;

  buttonText: string;
  linkPrefix$: Observable<string>;
  readonly customDuration = 'custom';
  readonly durations = ['15', '30', '45', '60'];
  readonly increments: DurationOption[] = [
    { seconds: 900, label: '15' },
    { seconds: 1800, label: '30' },
    { seconds: 3600, label: '60' },
  ];
  readonly buffers: DurationOption[] = [
    { seconds: 0, label: '0' },
    { seconds: 300, label: '5' },
    { seconds: 600, label: '10' },
    { seconds: 900, label: '15' },
    { seconds: 1800, label: '30' },
    { seconds: 3600, label: '60' },
  ];
  hourTime = 3600;
  readonly advanceNotice: DurationOption[] = [
    { seconds: 0, label: '0' },
    { seconds: this.hourTime, label: `1` },
    { seconds: this.hourTime * 2, label: `2` },
    { seconds: this.hourTime * 4, label: `4` },
    { seconds: this.hourTime * 12, label: `12` },
    { seconds: this.hourTime * 24, label: `24` },
    { seconds: this.hourTime * 48, label: `48` },
  ];

  teamMember: UntypedFormGroup;
  teamMemberCtrl = new FormControl();
  selectedTeamMembers: HostUser[] = [];
  filteredTeamMembers: Observable<HostUser[]>;
  allTeamMembers: any[] = [];
  separatorKeysCodes = [13, 188]; // Enter and comma as separators.
  isUpdate = false;
  loading$: Observable<boolean>;
  showHostUserSection$: Observable<boolean>;
  private meetingTypes$: Observable<MeetingType[]>;
  private meetingTypeToUpdate$: Observable<MeetingType>;
  meetingId;
  timezone;
  meetingIdNotNull = false;
  savingAvailabilityForm = false;

  calendar: Calendar | null = null;

  get teamMemberListArray(): UntypedFormArray {
    return this.teamMember.get('teamMemberName') as UntypedFormArray;
  }

  private originalSlug: string;
  private usedSlugs: string[] = [];
  private readonly slugRandomness = uuidv4().split('-')[0];

  readonly colors = [
    '#EE5353',
    '#F778B4',
    '#E27EFF',
    '#8988FC',
    '#4A91E9',
    '#0BC0D7',
    '#34C66F',
    '#67C820',
    '#DFC12C',
    '#F49A31',
  ];

  fieldTypes = new Map<FormFieldType, string>([
    [
      FormFieldType.FORM_FIELD_TYPE_TEXT,
      this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT'),
    ],
    [
      FormFieldType.FORM_FIELD_TYPE_EMAIL,
      this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_EMAIL'),
    ],
    [
      FormFieldType.FORM_FIELD_TYPE_PHONE_NUMBER,
      this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_PHONE_NUMBER'),
    ],
  ]);
  fieldTypeKeys = Array.from(this.fieldTypes.keys());
  fieldLabelMax = 50;
  hostUserIds: string[] = [];
  calendarId: string;
  meetingTypeId: string;
  hostid: string;
  applicationContext: any;
  businessId: string;
  partnerid: string;
  context: string;
  eventTypeName: string;
  isAvailabilitySelected: boolean;
  displayAvailabilitySelectionError: boolean;
  dateRangeConfig: EventTypeDateRangeInterface;

  public groupCalendars$: Observable<Calendar[]>;
  isShowContent = false;
  private readonly subscriptions: Subscription[] = [];
  showAlert$: Observable<boolean>;
  teamMembers$: Observable<HostUser[]>;
  hostAvailability$: Observable<Map<string, AvailabilityRuleList>>;
  @ViewChild('accordion') private readonly accordion: MatAccordion;
  @ViewChild('teamMemberChipGrid') teamMemberChipGrid: MatChipGrid;
  @ViewChild('teamMemberInput') teamMemberInput: ElementRef<HTMLInputElement>;

  customFields = this.formBuilder.array([], this.noDuplicates());

  form = this.formBuilder.group({
    id: this.formBuilder.control(''),
    name: this.formBuilder.control('', [Validators.required]),
    durationSelection: this.formBuilder.control('15', [Validators.required]),
    durationInput: this.formBuilder.control('15', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.min(15),
    ]),
    description: this.formBuilder.control(''),
    color: this.formBuilder.control(this.colors[0]),
    meetingLocation: this.formBuilder.control('video'), // Default value
    radioSelection: this.formBuilder.control('userSite'),
    radioChoice: this.formBuilder.control(TeamEventMeetingType.ROUND_ROBIN), // Setting default value
    emailSubject: this.formBuilder.control(''),
    emailDescription: this.formBuilder.control(''),
    inPersonAddress: this.formBuilder.control(''),
    locationGuidelines: this.formBuilder.control(''),
    slug: this.formBuilder.control('', [Validators.required, Validators.pattern('[a-zA-Z0-9_-]+'), this.uniqueSlug()]),
    customFields: this.customFields,
    availabilityIncrement: this.formBuilder.control('-1'),
    bufferDurationAfterMeeting: this.formBuilder.control('-1'),
    bufferDurationBeforeMeeting: this.formBuilder.control('-1'),
    noticeTime: this.formBuilder.control('-1'),
    isVideoLinkRequired: this.formBuilder.control(true),
    // Date range controls
    dateRangeType: this.formBuilder.control(DateRangeType.RELATIVE, [Validators.required]),
    relativeValue: this.formBuilder.control(4, [Validators.required, Validators.min(1), Validators.max(365)]),
    relativeUnit: this.formBuilder.control(RelativeTimeUnit.WEEKS, [Validators.required]),
    startDate: this.formBuilder.control(null),
    endDate: this.formBuilder.control(null),
  });

  // Date range properties
  DateRangeType = DateRangeType;
  RelativeTimeUnit = RelativeTimeUnit;

  timeUnits = [
    { value: RelativeTimeUnit.DAYS, label: 'Days' },
    { value: RelativeTimeUnit.WEEKS, label: 'Weeks' },
    { value: RelativeTimeUnit.MONTHS, label: 'Months' },
  ];

  // Today's date for date picker minimum
  today = new Date();

  constructor(
    @Inject(MEETING_LIST_PAGE_TOKEN) public readonly meetingListLink$: Observable<string>,
    @Inject(MEETING_TYPE_LIST_VIEW_PAGE_TOKEN) public readonly meetingTypeListViewPage$: Observable<string>,
    @Inject(HOST_ID_TOKEN) private readonly _hostId$: Observable<string>,
    private accountGroupService: AccountGroupService,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly storeService: MeetingSchedulerStoreService,
    private readonly alerts: SnackbarService,
    private readonly dialog: MatDialog,
    private service: BusinessNavConfigService,
    private readonly scheduleSettingsViewService: ScheduleSettingsViewService,
    private whitelabelService: WhitelabelService,
    private cdr: ChangeDetectorRef,
    @Inject(FEATURE_MEETING_EMAIL_TEMPLATE_TOKEN)
    readonly featureEmailTemplateEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_EXPANSION_TOKEN)
    readonly featureMeetingExpansionEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_EMBED_CODE_TOKEN)
    readonly featureMeetingEmbedEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_PHYSICAL_LOCATION_TOKEN)
    readonly featureMeetingPhysicalLocationEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_BEFORE_BUFFER_TOKEN)
    readonly featureMeetingBeforeBufferEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_ROUNDROBIN_TOKEN)
    readonly featureMeetingRoundRobinEnabled$: Observable<boolean>,
    private readonly analyticsService: ProductAnalyticsService,
    @Inject(FEATURE_GROUPS_AND_SERVICES_TOKEN)
    readonly featureGroupsandServicesEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_MULTIHOST_TOKEN)
    readonly featureMeetingMultiHostEnabled$: Observable<boolean>,
    @Inject(FEATURE_MEETING_DATE_RANGE_TOKEN)
    readonly featureDateRangeEnabled$: Observable<boolean>,
  ) {}

  async ngOnInit() {
    this.storeService.selectedToggle = EVENTS;
    this.subscriptions.push(
      this._hostId$.subscribe((id) => {
        this.hostid = id;
      }),
    );
    const inPersonAddressControl = this.form.get('inPersonAddress');
    const locationGuidelinesControl = this.form.get('locationGuidelines');

    // Clear all validators first
    inPersonAddressControl?.clearValidators();
    locationGuidelinesControl?.clearValidators();

    this.groupCalendars$ = this.storeService.loadGroupCalendars();
    this.calendarId = this.route.snapshot.params.calendarId || '';
    await this.loadCalendar();

    const hostsResponse$ = this.storeService.loadHostsForCalendar(this.calendarId);

    // Extract team members from the response
    this.teamMembers$ = hostsResponse$.pipe(map((response) => response.hostUsers || []));

    // Extract host availability from the response
    this.hostAvailability$ = hostsResponse$.pipe(
      map((response) => response.hostAvailability || new Map<string, AvailabilityRuleList>()),
    );

    this.subscriptions.push(
      this.teamMembers$
        .pipe(map((teamMembers) => teamMembers.filter(({ displayName }) => !!displayName)))
        .subscribe((teamMembers) => {
          this.allTeamMembers = teamMembers;
          this.filteredTeamMembers = this.teamMemberCtrl.valueChanges.pipe(
            startWith(''),
            map((value) => this._filter(value || '')),
          );
          if (this.hostUserIds && this.hostUserIds.length > 0) {
            this.selectedTeamMembers = this.allTeamMembers.filter((member) => this.hostUserIds.includes(member.userId));
          }
        }),
    );

    this.showHostUserSection$ = this.groupCalendars$.pipe(
      map((calendars) => Boolean((calendars || []).find((c) => c.id === this.calendarId))),
    );
    const meetingTypeId = this.route.snapshot.params.eventTypeId || '';
    this.meetingTypeId = meetingTypeId;
    this.isUpdate = meetingTypeId !== '';
    this.buttonText = this.isUpdate
      ? this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.UPDATE')
      : this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.CREATE');
    this.cdr.markForCheck();
    this.linkPrefix$ = this.storeService.loadPersonalGeneralBookingLink().pipe(
      map((url) => {
        const parts = url.replace('https://', '').replace('http://', '').split('/');
        return (parts.length > 0 ? parts[0] : '') + '/you/';
      }),
    );

    this.timezone = this.hostPreferences?.timezone;

    this.subscriptions.push(
      this.form.controls.meetingLocation.valueChanges.subscribe((value) => {
        inPersonAddressControl?.clearValidators();
        locationGuidelinesControl?.clearValidators();
        if (value !== 'video') {
          if (this.form.get('radioSelection').value === 'userSite') {
            inPersonAddressControl?.setValidators([Validators.required]);
          } else {
            locationGuidelinesControl?.setValidators([Validators.required]);
          }
        }
        inPersonAddressControl?.updateValueAndValidity();
        locationGuidelinesControl?.updateValueAndValidity();
      }),
      this.form.controls.isVideoLinkRequired.valueChanges.subscribe(),
      this.form.controls.radioSelection.valueChanges.subscribe((value) => {
        inPersonAddressControl?.clearValidators();
        locationGuidelinesControl?.clearValidators();
        if (this.form.get('meetingLocation').value !== 'video') {
          if (value === 'userSite') {
            inPersonAddressControl?.setValidators([Validators.required]);
          } else {
            locationGuidelinesControl?.setValidators([Validators.required]);
          }
        }
        inPersonAddressControl?.updateValueAndValidity();
        locationGuidelinesControl?.updateValueAndValidity();
      }),
    );

    this.subscriptions.push(
      this.form.controls.name.valueChanges.subscribe(() => {
        const controls = this.form.controls;
        const name = controls.name.value as string;
        this.eventTypeName = name;
        this.form.get('slug')?.markAsTouched();
        if (!this.isUpdate && !controls.slug.dirty) {
          let generatedSlug = name
            .toLowerCase()
            .replace(/\s/g, '-')
            .replace(/[^0-9a-zA-Z_-]/g, '');
          if (this.usedSlugs.includes(generatedSlug)) {
            generatedSlug += '-' + this.slugRandomness;
          }
          controls.slug.setValue(generatedSlug);
          controls.slug.markAsPristine();
        }
      }),
    );
    this.subscriptions.push(
      this.form.controls.durationSelection.valueChanges.subscribe(() => {
        const controls = this.form.controls;
        const selection = controls.durationSelection.value;
        const input = controls.durationInput;
        if (selection !== this.customDuration && selection !== input.value) {
          input.setValue(selection);
        }
      }),
    );

    // Date range form subscriptions
    this.subscriptions.push(
      this.form.controls.dateRangeType.valueChanges.subscribe(() => {
        this.updateDateRangeValidators();
      }),
    );

    // We don't necessarily need to have the full list of meeting types here,
    // but the dataset should be small and potentially loaded already. We may also want
    // to do client side validation that the bookingLink is unique for form validation
    // when we enable them to change it. As an alternative we could load just the one
    // meeting type for the update mode and call an API to list just bookingLinks for
    // the user, but the performance improvements should be trivial.
    this.meetingTypes$ = this.storeService.loadMeetingTypes({ calendarId: this.calendarId, returnCache: true });
    this.meetingTypeToUpdate$ = this.meetingTypes$.pipe(
      first(),
      tap((meetingTypes) => {
        this.usedSlugs = meetingTypes
          .filter((mt) => mt.meetingTypeSlug !== meetingTypeId && mt.id !== meetingTypeId)
          .map((mt) => mt.meetingTypeSlug);
        // A meeting type starts by having its slug equal its identifiers.
        // We allow a meeting type to save its own slug
      }),
      map((meetingTypes) => {
        const meetingType = meetingTypes.find((mt) => mt.meetingTypeSlug === meetingTypeId || mt.id === meetingTypeId);
        this.originalSlug = meetingType ? meetingType.meetingTypeSlug : '';
        return meetingType;
      }),
    );

    this.showAlert$ = this.teamMembers$.pipe(
      map((teamMembers) => {
        const unconfiguredTeamMembers = teamMembers.filter((teamMember) => !teamMember.isConfigured);
        return unconfiguredTeamMembers.length > 0;
      }),
    );

    this.loading$ = this.meetingTypeToUpdate$.pipe(mapTo(false));
    this.subscriptions.push(
      this.meetingTypeToUpdate$.subscribe((meetingType) => {
        if (!meetingType) {
          this.setAddressBasedOnContext();
          return;
        }
        this.meetingId = meetingType.id;
        this.meetingIdNotNull = true;
        const controls = this.form.controls;
        controls.id.setValue(meetingType.id);
        this.eventTypeName = (meetingType.name || '').trim();
        controls.name.setValue(this.eventTypeName);
        controls.description.setValue((meetingType.description || '').trim());
        controls.color.setValue(meetingType.hexColor);
        controls.slug.setValue(meetingType.meetingTypeSlug);
        controls.emailSubject.setValue((meetingType.emailSubject || '').trim());
        controls.emailDescription.setValue((meetingType.emailDescription || '').trim());
        this.customFields.clear();
        controls.availabilityIncrement.setValue(meetingType.availabilityIncrement);
        controls.bufferDurationAfterMeeting.setValue(meetingType.bufferDurationAfterMeeting);
        controls.bufferDurationBeforeMeeting.setValue(meetingType.bufferDurationBeforeMeeting);
        controls.noticeTime.setValue(meetingType.noticeTime);
        controls.isVideoLinkRequired.setValue(!meetingType.isVideoLinkDisabled);
        if (meetingType.location === '') {
          this.setAddressBasedOnContext();
        } else {
          controls.inPersonAddress.setValue((meetingType.location || '').trim());
        }
        if (meetingType.locationType !== MeetingLocationType.VIDEO) {
          controls.meetingLocation.setValue('inPerson');
          if (meetingType.locationType === MeetingLocationType.IN_PERSON_USER_SITE) {
            controls.radioSelection.setValue('userSite');
            controls.inPersonAddress.setValue((meetingType.location || '').trim());
          } else {
            controls.radioSelection.setValue('clientSite');
            controls.locationGuidelines.setValue((meetingType.locationGuidelines || '').trim());
          }
        }
        if (meetingType.isClientChoice) {
          controls.radioChoice.setValue(TeamEventMeetingType.CLIENT_CHOICE);
        } else {
          controls.radioChoice.setValue(
            meetingType?.meetingType ? meetingType?.meetingType : TeamEventMeetingType.ROUND_ROBIN,
          );
        }

        meetingType.form.fields.forEach((field: MeetingTypeFormField) => {
          this.customFields.push(this.groupFromFormField(field));
        });
        const durationMinutes = durationStringToMinutes({ duration: meetingType.duration }) + '';
        if (this.durations.includes(durationMinutes)) {
          controls.durationSelection.setValue(durationMinutes);
        } else {
          controls.durationSelection.setValue(this.customDuration);
        }
        controls.durationInput.setValue(durationMinutes);
        if (meetingType.hostUserIds.length > 0) {
          this.hostUserIds = meetingType.hostUserIds;
          this.selectedTeamMembers = this.allTeamMembers.filter((member) => this.hostUserIds.includes(member.userId));
        } else {
          this.subscriptions.push(
            this.teamMembers$.subscribe((hostUsers) => {
              this.hostUserIds = hostUsers.map((user) => user.userId);
              this.selectedTeamMembers = this.allTeamMembers.filter((member) =>
                this.hostUserIds.includes(member.userId),
              );
            }),
          );
        }

        this.dateRangeConfig = meetingType.dateRange;

        // Initialize form with date range data if available
        if (this.dateRangeConfig) {
          if (!this.dateRangeConfig.dateRangeType) {
            this.dateRangeConfig.dateRangeType = DateRangeType.RELATIVE;
          }

          this.initializeDateRangeForm();
        }
      }),
    );

    this.filteredTeamMembers = this.teamMemberCtrl.valueChanges.pipe(
      startWith(''),
      switchMap((searchTerm: any) => this.teamMembers$.pipe(map(() => this._filter(searchTerm?.displayName || '')))),
    );
  }

  hasSelectedAvailability(isAvailabilitySelected: boolean) {
    this.isAvailabilitySelected = isAvailabilitySelected;
  }

  setAddressBasedOnContext(): void {
    if (this.context === 'SMB') {
      this.getSMBAddress(this.businessId).then((ad) => {
        this.address = ad;
        this.form.controls['inPersonAddress'].setValue(this.address);
      });
    } else {
      const formattedAddress$ = this.getPartnerAddress$();
      this.subscriptions.push(
        formattedAddress$.subscribe((formattedAddress) => {
          this.address = formattedAddress;
          this.form.controls['inPersonAddress'].setValue(this.address);
        }),
      );
    }
  }

  loadCalendar(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.storeService.loadCalendar(this.calendarId).subscribe(
        (calendar) => {
          this.calendar = calendar; // Store the response in a variable
          this.applicationContext = calendar.applicationContext;
          this.context = this.applicationContext.user_context;

          if (this.context === 'SMB') {
            this.businessId = this.applicationContext.business_id;
          } else {
            this.partnerid = this.applicationContext.partner_id;
          }

          resolve();
        },
        (error) => {
          console.error('Error loading calendar:', error);
          reject(error);
        },
      );
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  get isCustomDuration(): boolean {
    return this.form?.get('durationSelection').value === this.customDuration;
  }

  get slugChanged(): boolean {
    return this.isUpdate && this.originalSlug !== (this.form.controls.slug.value as string);
  }

  // Date range getters
  get isRelativeMode(): boolean {
    return this.form.get('dateRangeType')?.value === DateRangeType.RELATIVE;
  }

  get isCustomMode(): boolean {
    return this.form.get('dateRangeType')?.value === DateRangeType.CUSTOM;
  }

  get relativeValueError(): string | null {
    const control = this.form.get('relativeValue');
    if (control?.hasError('required')) {
      return 'Value is required';
    }
    if (control?.hasError('min')) {
      return 'Value must be at least 1';
    }
    return null;
  }
  get hasDateRangeError(): boolean {
    return this.form.hasError('dateRangeInvalid');
  }

  uniqueSlug(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const slug = control.value as string;
      return this.usedSlugs.includes(slug)
        ? {
            unique: {
              usedSlugs: this.usedSlugs,
              slug: slug,
            },
          }
        : null;
    };
  }

  select(event: boolean, hostUser: HostUser): void {
    if (this.hostUserIds.indexOf(hostUser.userId) < 0) {
      this.hostUserIds.push(hostUser.userId);
    } else {
      this.hostUserIds = this.hostUserIds.filter((userId) => userId !== hostUser.userId);
    }
  }

  onShow(checked: boolean): void {
    this.isShowContent = checked;
  }

  revertSlug(): void {
    this.form.controls.slug.setValue(this.originalSlug);
  }

  getSlugValidationError(): string {
    const errors = this.form.controls.slug.errors;
    if (errors.required) {
      return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_REQUIRED');
    } else if (errors.pattern) {
      return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_PATTERN');
    } else if (errors.unique) {
      return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_UNIQUE');
    }
    return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_UNKNOWN');
  }

  setColor(color: string): void {
    this.form.controls.color.setValue(color);
  }

  async createOrUpdateMeetingType(): Promise<void> {
    this.teamMemberCtrl.markAsTouched();
    //Check if Form is invalid and return blank to avoid request being submitted to API for meeting update
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    // Handle team member selection
    // Team members are required only for team event types
    let isTeamRequired = false;
    this.subscriptions.push(
      this.showHostUserSection$.subscribe((value: boolean) => {
        isTeamRequired = value;
      }),
    );

    const controls = this.form.controls;

    if (isTeamRequired && this.selectedTeamMembers.length < 1) {
      this.teamMemberInput.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      return;
    }

    if (
      isTeamRequired &&
      (this.selectedTeamMembers.length < 2 || this.selectedTeamMembers.length > 5) &&
      controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST
    ) {
      this.teamMemberInput.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      return;
    }

    if (controls.radioChoice.value === TeamEventMeetingType.MULTI_HOST && !this.isAvailabilitySelected) {
      this.displayAvailabilitySelectionError = true;
      this.scrollToAvailabilityPanel();
      return;
    }

    let locationType = MeetingLocationType.VIDEO as MeetingLocationType;
    let location = '';
    let locationGuidelines = '';

    if (controls.meetingLocation.value === 'inPerson') {
      if (controls.radioSelection.value === 'userSite') {
        locationType = MeetingLocationType.IN_PERSON_USER_SITE;
        location = controls.inPersonAddress.value;
      } else if (controls.radioSelection.value === 'clientSite') {
        locationType = MeetingLocationType.IN_PERSON_CLIENT_SITE;
        locationGuidelines = controls.locationGuidelines.value;
      }
    }

    const mt = {
      calendarId: this.calendarId,
      id: controls.id.value as string,
      name: controls.name.value as string,
      duration: (controls.durationInput.value as string) + 'm',
      description: controls.description.value as string,
      hexColor: controls.color.value as string,
      meetingTypeSlug: controls.slug.value as string,
      emailSubject: controls.emailSubject.value as string,
      emailDescription: controls.emailDescription.value as string,
      availabilityIncrement: controls.availabilityIncrement.value as number,
      bufferDurationAfterMeeting: controls.bufferDurationAfterMeeting.value as number,
      bufferDurationBeforeMeeting: controls.bufferDurationBeforeMeeting.value as number,
      noticeTime: controls.noticeTime.value as number,
      isClientChoice: controls.radioChoice.value === TeamEventMeetingType.CLIENT_CHOICE,
      isVideoLinkDisabled: !controls.isVideoLinkRequired.value as boolean,
      locationType: locationType as MeetingLocationType,
      location: location as string,
      locationGuidelines: locationGuidelines as string,
      form: {
        fields: controls.customFields.value.map((field: MeetingTypeFormField) => {
          field.id = this.deriveIDFromLabel(field.label);
          return field;
        }),
      },
      hostUserIds: this.hostUserIds,
      meetingType: controls.radioChoice.value,
      dateRange: this.buildDateRangeConfig(),
    } as MeetingType;
    this.meetingId = mt.id;

    if (this.isUpdate) {
      const newSlug = controls.slug.value as string;
      if (this.originalSlug !== newSlug) {
        this.dialog.open(UpdateBookingLinkConfirmationDialogComponent, {
          maxWidth: '500px',
          data: {
            originalSlug: this.originalSlug,
            newSlug: newSlug,
            confirmationCallback: () => from(this.updateMeetingType(mt.id, mt)),
          },
        });
      } else {
        await this.updateMeetingType(mt.id, mt);
      }
    } else {
      try {
        const id = await this.storeService.createMeetingType(mt);
        this.analyticsService.trackEvent(
          POSTHOG_KEYS.EVENT_TYPE_CREATED,
          POSTHOG_CATEGORIES.USER,
          POSTHOG_ACTIONS.CLICK,
        );
        this.alerts.openSuccessSnack('MEETING_SCHEDULER.BOOKING_LINK.CREATED_SUCCESS');
        this.navigateToMeetingList();
        this.setAvailability(id);
      } catch (err) {
        this.alerts.openErrorSnack('MEETING_SCHEDULER.BOOKING_LINK.CREATED_ERROR');
        console.error(err);
      }
    }
  }

  async updateMeetingType(id: string, meetingType: MeetingType): Promise<void> {
    try {
      await this.storeService.updateMeetingType(id, meetingType);
      this.analyticsService.trackEvent(POSTHOG_KEYS.EVENT_TYPE_UPDATED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
      this.setAvailability(id);
      this.alerts.openSuccessSnack('MEETING_SCHEDULER.BOOKING_LINK.UPDATED_SUCCESS');
      this.navigateToMeetingList();
    } catch (err) {
      this.alerts.openErrorSnack('MEETING_SCHEDULER.BOOKING_LINK.UPDATED_ERROR');
      console.error(err);
    }
  }

  async getSMBAddress(businessid: string): Promise<string> {
    const accountGroupProjectionFilter = new ProjectionFilter({
      napData: true,
      accountGroupExternalIdentifiers: true,
      marketingInfo: true,
    });

    const business$ = this.accountGroupService.get(businessid, accountGroupProjectionFilter);

    return combineLatest([this.service.partnerId$, business$])
      .pipe(take(1))
      .toPromise()
      .then(([, business]) => {
        const napData = business?.napData;
        return napData
          ? this.buildAddress(napData.address, napData.city, napData.zip, napData.state, napData.country)
          : '';
      });
  }

  getPartnerAddress$() {
    return this.partnerId$.pipe(
      switchMap((partnerId) => this.whitelabelService.getConfiguration(partnerId)),
      map((config) => {
        const address = config?.mailingConfiguration?.mailingAddress;
        const city = config?.mailingConfiguration?.mailingCity;
        const state = config?.mailingConfiguration?.mailingState;
        const zip = config?.mailingConfiguration?.mailingPostalCode;
        const country = config?.mailingConfiguration?.mailingCountry;
        return this.buildAddress(address, city, zip, state, country);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  buildAddress(address: string, city: string, zip: string, state: string, country: string): string {
    const parts = [];
    if (address) parts.push(address);
    if (city) parts.push(city);
    if (zip) parts.push(zip);
    if (state) parts.push(state);
    if (country) parts.push(country);
    const formattedAddress = parts.join(', ');
    return formattedAddress;
  }

  get aboutCardDescription(): string {
    const controls = this.form?.controls;

    if (!controls) return '';
    const durationShorthand =
      controls.durationInput.value + ' ' + this.translate.instant('MEETING_SCHEDULER.COMMON.DURATIONS.MINUTES');
    if (controls.name.value !== '') {
      let name = controls.name.value;
      const maxLen = 25;
      if (name.length > maxLen) {
        name = name.substr(0, maxLen) + '...';
      }
      return name + ', ' + durationShorthand;
    }
    return durationShorthand;
  }

  get questionsCardDescription(): string {
    const customFields = this.form?.controls?.customFields.value;
    if (customFields) {
      return (
        customFields.length +
        ' ' +
        this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DESCRIPTION_SUFFIX')
      );
    }
    return '';
  }

  describeFieldType(type: FormFieldType): string {
    return this.fieldTypes.get(type) || '';
  }

  addTeamMemberFromInput(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value) {
      const foundMember = this.allTeamMembers.find(
        (member) => member.displayName.toLowerCase() === value.toLowerCase(),
      );
      if (foundMember && !this.selectedTeamMembers.some((member) => member.userId === foundMember.userId)) {
        this.selectedTeamMembers.push(foundMember);
        this.hostUserIds.push(foundMember.userId);
      }
      event.chipInput?.clear();
      this.teamMemberCtrl.setValue(null);
    }
  }

  removeTeamMember(index: number): void {
    const teamMember = this.selectedTeamMembers[index];
    if (teamMember) {
      this.selectedTeamMembers.splice(index, 1);
      this.hostUserIds = this.hostUserIds.filter((id) => id !== teamMember.userId);
    }
  }

  selectedTeamMember(event: MatAutocompleteSelectedEvent): void {
    const teamMember = event.option.value;
    if (!this.selectedTeamMembers.some((member) => member.userId === teamMember.userId)) {
      this.selectedTeamMembers.push(teamMember);
      this.hostUserIds.push(teamMember.userId);
    }
    if (this.teamMemberInput) this.teamMemberInput.nativeElement.value = '';
    this.teamMemberCtrl.setValue(null);
  }

  private _filter(value: string): any[] {
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    return this.allTeamMembers.filter((teamMember) => teamMember.displayName.toLowerCase().includes(filterValue));
  }

  fieldLabelErrorMessage(label: string): void {
    if (label.length > 0) {
      return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_LABEL_MAX', {
        max: this.fieldLabelMax,
      });
    }
    return this.translate.instant('MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_LABEL_REQUIRED');
  }

  addFormField(): void {
    if (this.form.controls.customFields.invalid) {
      return;
    }
    const customFields = this.form.controls.customFields as UntypedFormArray;
    customFields.push(
      this.groupFromFormField({
        id: '',
        label: '',
        type: FormFieldType.FORM_FIELD_TYPE_TEXT,
        required: false,
      }),
    );
  }

  changeFieldOrder(event: CdkDragDrop<string[]>): void {
    const customFields = this.form.controls.customFields as UntypedFormArray;
    const controls = customFields.controls;
    const values = customFields.value;
    moveItemInArray(controls, event.previousIndex, event.currentIndex);
    moveItemInArray(values, event.previousIndex, event.currentIndex);
  }

  openAllFields(): void {
    this.accordion.openAll();
  }

  closeAllFields(): void {
    this.accordion.closeAll();
  }

  deleteField(index: number): void {
    const customFields = this.form.controls.customFields as UntypedFormArray;
    customFields.removeAt(index);
  }

  fieldsHaveError(err: string): boolean {
    return this.form.controls.customFields.hasError(err);
  }

  private deriveIDFromLabel(label: string): string {
    return camelCase(label);
  }

  private groupFromFormField(field: MeetingTypeFormField): UntypedFormGroup {
    return this.formBuilder.group({
      id: this.formBuilder.control(this.deriveIDFromLabel(field.label)),
      label: this.formBuilder.control(field.label || '', Validators.maxLength(this.fieldLabelMax)),
      type: this.formBuilder.control(field.type || FormFieldType.FORM_FIELD_TYPE_TEXT),
      required: this.formBuilder.control(field.required || false),
    });
  }

  private noDuplicates(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const fields = control.value as MeetingTypeFormField[];
      const ids = fields.map((field) => this.deriveIDFromLabel(field.label)).filter((id) => id !== '');
      if (ids.some((id) => ids.indexOf(id) !== ids.lastIndexOf(id))) {
        return { noDuplicates: {} };
      }
      return null;
    };
  }

  private async navigateToMeetingList(): Promise<unknown> {
    let isFeatureFlagEnabled = false;
    this.subscriptions.push(
      this.featureGroupsandServicesEnabled$.subscribe((isEnabled) => {
        isFeatureFlagEnabled = isEnabled;
      }),
    );

    if (isFeatureFlagEnabled) {
      const meetingTypeListLink = await this.meetingTypeListViewPage$.pipe(take(1)).toPromise();
      return this.router.navigate([meetingTypeListLink]);
    }
    const meetingListLink = await this.meetingListLink$.pipe(take(1)).toPromise();
    return this.router.navigate([meetingListLink]);
  }
  setAvailability(meetingTypeId: string): void {
    this.savingAvailabilityForm = true;
    const data = this.storeService.dataSubject$$.value;
    const dayEnabled = data?.availability;
    const dayRanges = data?.timeRanges;
    const availability: DailyAvailability = { days: new Map() };
    if (dayEnabled !== undefined) {
      dayEnabled.forEach((available, day) => {
        if (available) {
          availability.days.set(day, dayRanges.get(day));
        }
      });

      this.storeService
        .setAvailability(availability, this.timezone, meetingTypeId, this.hostid)
        .then(() => {
          this.scheduleSettingsViewService.resetStateStorage();
          this.alerts.openSuccessSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED');
        })
        .catch(() => {
          this.alerts.openErrorSnack('MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED_ERROR');
        });
    }
  }

  private scrollToAvailabilityPanel(): void {
    const element = document.querySelector('[data-testid="availability-selection-panel"]');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  protected readonly TeamEventMeetingType = TeamEventMeetingType;

  private updateDateRangeValidators(): void {
    const controls = this.form.controls;
    const dateRangeType = controls.dateRangeType.value;
    const relativeValue = controls.relativeValue;
    const relativeUnit = controls.relativeUnit;
    const startDate = controls.startDate;
    const endDate = controls.endDate;

    // Clear all validators first
    relativeValue.clearValidators();
    relativeUnit.clearValidators();
    startDate.clearValidators();
    endDate.clearValidators();

    if (dateRangeType === DateRangeType.RELATIVE) {
      // Relative mode: enable relative controls, disable date controls
      relativeValue.setValidators([Validators.required, Validators.min(1)]);
      relativeUnit.setValidators([Validators.required]);
      relativeValue.enable();
      relativeUnit.enable();
      startDate.disable();
      endDate.disable();
      // Remove form-level validator
      this.form.clearValidators();
    } else {
      // Custom mode: enable date controls, disable relative controls
      startDate.setValidators([Validators.required]);
      endDate.setValidators([Validators.required]);
      startDate.enable();
      endDate.enable();
      relativeValue.disable();
      relativeUnit.disable();
      // Add custom validator to ensure end date is after start date
      this.form.setValidators(this.dateRangeValidator);
    }

    // Update validity
    relativeValue.updateValueAndValidity();
    relativeUnit.updateValueAndValidity();
    startDate.updateValueAndValidity();
    endDate.updateValueAndValidity();
  }

  // Initialize date range form
  private initializeDateRangeForm(): void {
    const controls = this.form.controls;
    controls.dateRangeType.setValue(this.dateRangeConfig.dateRangeType);
    controls.dateRangeType.updateValueAndValidity();

    if (this.dateRangeConfig.dateRangeType === DateRangeType.RELATIVE && this.dateRangeConfig.relativeDateRange) {
      if (!this.dateRangeConfig.relativeDateRange.unit) {
        this.dateRangeConfig.relativeDateRange.unit = RelativeTimeUnit.DAYS;
      }

      controls.relativeValue.setValue(this.dateRangeConfig.relativeDateRange.value);
      controls.relativeValue.updateValueAndValidity();
      controls.relativeUnit.setValue(this.dateRangeConfig.relativeDateRange.unit);
      controls.relativeUnit.updateValueAndValidity();
    } else if (this.dateRangeConfig.dateRangeType === DateRangeType.CUSTOM && this.dateRangeConfig.customDateRange) {
      controls.startDate.setValue(this.dateRangeConfig.customDateRange.start);
      controls.startDate.updateValueAndValidity();
      controls.endDate.setValue(this.dateRangeConfig.customDateRange.end);
      controls.endDate.updateValueAndValidity();
    }

    // Update validators based on the type
    this.updateDateRangeValidators();

    // Mark form as pristine after initialization
    this.form.markAsPristine();

    // Trigger change detection to ensure UI updates
    this.cdr.detectChanges();
  }

  private setToEndOfDay(date: Date | null): Date | null {
    if (!date) return null;

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return endOfDay;
  }

  private buildDateRangeConfig(): EventTypeDateRangeInterface {
    const controls = this.form.controls;
    const dateRangeType = controls.dateRangeType.value;

    if (dateRangeType === DateRangeType.RELATIVE) {
      return {
        dateRangeType: DateRangeType.RELATIVE,
        relativeDateRange: {
          value: controls.relativeValue.value,
          unit: controls.relativeUnit.value,
        },
        customDateRange: null,
      };
    } else {
      return {
        dateRangeType: DateRangeType.CUSTOM,
        relativeDateRange: null,
        customDateRange: {
          start: controls.startDate.value,
          end: this.setToEndOfDay(controls.endDate.value),
        },
      };
    }
  }

  private dateRangeValidator(form: UntypedFormGroup): { [key: string]: boolean } | null {
    const startDate = form.get('startDate');
    const endDate = form.get('endDate');

    if (startDate && endDate && startDate.value && endDate.value) {
      const start = new Date(startDate.value);
      const end = new Date(endDate.value);

      // Reset time to compare only dates
      start.setHours(0, 0, 0, 0);
      end.setHours(0, 0, 0, 0);

      if (start >= end) {
        return { dateRangeInvalid: true };
      }
    }
    return null;
  }
}
