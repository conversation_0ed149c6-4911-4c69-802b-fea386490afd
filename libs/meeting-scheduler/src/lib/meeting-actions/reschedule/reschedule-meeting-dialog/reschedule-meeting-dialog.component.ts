import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Observable, Subscription } from 'rxjs';
import { MeetingForAction } from '../../interface';
import { Preferences, TeamEventMeetingType, TimeSpan } from '@vendasta/meetings';
import { MeetingSchedulerStoreService } from '../../../data-providers/meeting-scheduler-store.service';

@Component({
  selector: 'meeting-scheduler-reschedule-meeting',
  templateUrl: './reschedule-meeting-dialog.component.html',
  styleUrls: ['./reschedule-meeting-dialog.component.scss'],
  standalone: false,
})
export class RescheduleMeetingDialogComponent implements OnInit, OnDestroy {
  rescheduleForm: UntypedFormGroup;
  private dateTimeMap: Map<string, Date[]> = new Map();
  hostPreferences$: Observable<Preferences>;
  subscription: Subscription[] = [];
  timezone = '';
  userId = '';
  calenderDateStart: Date;
  calenderDateEnd: Date;
  availableTimeSlots: Date[];

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly dialogRef: MatDialogRef<RescheduleMeetingDialogComponent>,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    @Inject(MAT_DIALOG_DATA) public meeting: MeetingForAction,
  ) {}

  ngOnInit(): void {
    this.userId = this.meetingSchedulerStoreService.getUserId();

    this.hostPreferences$ = this.meetingSchedulerStoreService.loadUserPreferences();
    this.subscription.push(
      this.hostPreferences$.subscribe((hostPreference) => {
        this.timezone = hostPreference?.timezone?.id;
      }),
    );

    this.fetchCalenderAvailability();

    this.rescheduleForm = this.fb.group({
      meetingId: [this.meeting.meetingId],
      date: [null, Validators.required],
      timeSlot: [null, Validators.required],
      messageToGuest: [null],
    });

    this.subscription.push(
      this.rescheduleForm.get('date').valueChanges.subscribe((meetingDate) => {
        this.availableTimeSlots = this.dateTimeMap.get(this.formatDateWithoutTimezone(meetingDate));
      }),
    );
  }

  ngOnDestroy() {
    this.subscription.forEach((sub) => sub.unsubscribe());
    this.subscription = [];
  }

  rescheduleMeeting(): void {
    const rescheduledDate = this.rescheduleForm.value.timeSlot;
    this.dialogRef.close({ meetingId: this.meeting.meetingId, start: rescheduledDate });
  }

  formatDateWithoutTimezone(date: Date | null): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };
    // Format date parts using Intl.DateTimeFormat
    const formatter = new Intl.DateTimeFormat('en-CA', options); // en-CA => YYYY-MM-DD
    return formatter.format(date);
  }

  formatDate(date: Date | null): string {
    const options: Intl.DateTimeFormatOptions = {
      timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };
    // Format date parts using Intl.DateTimeFormat
    const formatter = new Intl.DateTimeFormat('en-CA', options); // en-CA => YYYY-MM-DD
    return formatter.format(date);
  }

  validateDate = (d: Date | null): boolean => {
    if (!d) return false;
    const dateString = this.formatDateWithoutTimezone(d);
    return this.dateTimeMap.has(dateString);
  };

  fetchCalenderAvailability() {
    this.dateTimeMap = new Map<string, Date[]>();
    const start = new Date();
    const end = new Date();
    end.setMonth(end.getMonth() + 3);

    const timeRange: TimeSpan = {
      start: start,
      end: end,
    };

    let userID = this.userId;
    if (this.meeting.meetingType === TeamEventMeetingType.MULTI_HOST) {
      // For multi host meeting user id should not be passed
      userID = '';
    }
    const response = this.meetingSchedulerStoreService.loadAvailableTimeSlots({
      calendarId: this.meeting.calendarId,
      meetingTypeId: this.meeting.eventTypeId,
      timeZone: { id: this.timezone },
      userID: userID,
      timeSpan: timeRange,
    });

    this.subscription.push(
      response.subscribe((availableTimeSlots) => {
        availableTimeSlots.forEach((timeSlot) => {
          const date = this.formatDate(timeSlot.start); // Extracts YYYY-MM-DD

          if (this.dateTimeMap.has(date)) {
            this.dateTimeMap.get(date)!.push(timeSlot.start);
          } else {
            this.dateTimeMap.set(date, [timeSlot.start]);
          }
        });

        // Restricting user selection to available timeslot
        // ie, user will not be able to navigate to months outside calendar availability
        const meetingDates = Array.from(this.dateTimeMap.keys());
        this.calenderDateStart = new Date(meetingDates[0]);
        this.calenderDateEnd = new Date(meetingDates[meetingDates.length - 1]);
      }),
    );
  }

  close(): void {
    this.dialogRef.close();
  }
}
