import { Component, EventEmitter, Input, OnInit, Optional, Output } from '@angular/core';
import { CustomSalespersonActionInterface, Status } from '@vendasta/sales-orders';
import { Observable, of } from 'rxjs';
import { OrderPermissionsService } from '../../core/permissions';
import { OrderAction } from '../../core/permissions/permissions';

export interface OrderConfirmationActionConfig {
  allowSendDirectToAdmin?: boolean;
  allowSendToCustomer?: boolean;
  customSalespersonActions?: CustomSalespersonActionInterface[];
}

interface buttonInterface {
  label: string;
  id: string;
  dataAction: string;
  action: () => void;
}

@Component({
  selector: 'orders-actions',
  templateUrl: './order-actions.component.html',
  styleUrls: ['./order-actions.component.scss'],
  standalone: false,
})
export class OrderActionsComponent implements OnInit {
  @Input() config: OrderConfirmationActionConfig;
  @Input() orderStatus: Status = null; // null represents a new order
  @Input() submitDisabled = true;

  @Output() goBackEmitter: EventEmitter<null> = new EventEmitter<null>();
  @Output() createDraftEmitter: EventEmitter<null> = new EventEmitter<null>();
  @Output() submitOrderEmitter: EventEmitter<null> = new EventEmitter<null>();
  @Output() submitForCustomerApprovalEmitter: EventEmitter<null> = new EventEmitter<null>();
  @Output() submitForAutomationActionEmitter: EventEmitter<string> = new EventEmitter<string>();
  @Output() convertToDraftEmitter = new EventEmitter<null>();
  @Output() requestCancellationEmitter = new EventEmitter<null>();
  @Output() collectPaymentEmitter = new EventEmitter<null>();
  @Output() processOrderEmitter = new EventEmitter<null>();

  canSubmitToCustomerForApproval$: Observable<boolean>;
  canRequestToCancelOrder$: Observable<boolean>;
  canBypassAdminWorkflowAndActivate$: Observable<boolean>;
  retailPaymentsEnabled$: Observable<boolean>;

  customSalespersonActions: buttonInterface[];

  Status = Status;

  canManageOrder$: Observable<boolean>;

  constructor(@Optional() private readonly orderPermissionsService: OrderPermissionsService) {}

  ngOnInit(): void {
    this.canSubmitToCustomerForApproval$ = of(this.config.allowSendToCustomer);
    if (this.orderPermissionsService) {
      this.canSubmitToCustomerForApproval$ = this.orderPermissionsService.CanDoAction(
        OrderAction.SubmitForCustomerApproval,
      );
    }

    this.canManageOrder$ = this.orderPermissionsService?.canManageOrders$;

    this.canRequestToCancelOrder$ = of(
      [
        Status.SUBMITTED,
        Status.RESUBMITTED,
        Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
        Status.SCHEDULED_ACTIVATION,
        Status.APPROVED,
        Status.FULFILLED,
        Status.ACTIVATION_ERRORS,
      ].includes(this.orderStatus),
    );
    if (this.orderPermissionsService) {
      this.canRequestToCancelOrder$ = this.orderPermissionsService.CanDoAction(OrderAction.RequestToCancelOrder);
      this.canBypassAdminWorkflowAndActivate$ = this.orderPermissionsService.CanDoAction(
        OrderAction.BypassAdminWorkflowAndActivate,
      );
      this.retailPaymentsEnabled$ = this.orderPermissionsService.retailPaymentsEnabled$;
    }

    if (this.orderStatus !== null) {
      this.customSalespersonActions = this.config?.customSalespersonActions?.map((a) => ({
        action: this.submitForAutomationAction(a.automationId).bind(this),
        dataAction: 'order-confirm-submit-' + a.automationId,
        id: 'order-confirm-submit-' + a.automationId,
        label: a.label,
      }));
    }
  }

  goBack(): void {
    this.goBackEmitter.emit();
  }

  createDraftOrder(): void {
    this.createDraftEmitter.emit();
  }

  submitOrder(): void {
    this.submitOrderEmitter.emit();
  }

  submitForCustomerApproval(): void {
    this.submitForCustomerApprovalEmitter.emit();
  }

  submitForAutomationAction(automationId: string): () => void {
    return () => {
      this.submitForAutomationActionEmitter.emit(automationId);
    };
  }

  onConvertToDraft(): void {
    this.convertToDraftEmitter.emit();
  }

  onIntentToCancel(): void {
    this.requestCancellationEmitter.emit();
  }

  protected onCollectPayment(): void {
    this.collectPaymentEmitter.emit();
  }

  protected onProcessOrder(): void {
    this.processOrderEmitter.emit();
  }
}
