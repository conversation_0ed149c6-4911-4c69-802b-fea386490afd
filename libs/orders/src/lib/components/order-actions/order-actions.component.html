<div class="buttons-container" *ngIf="config" [ngClass]="{ 'space-between-back-button': orderStatus === null }">
  <button
    *ngIf="orderStatus === null"
    class="back-button"
    id="order-confirm-back"
    data-action="order-confirm-back"
    mat-stroked-button
    color="secondary"
    (click)="goBack()"
  >
    {{ 'LIB_ORDERS.COMMON.ACTION_LABELS.BACK' | translate }}
  </button>

  <div class="buttons-wrapper">
    <!-- Secondary actions -->
    @if ([null, Status.DRAFTED].includes(orderStatus)) {
      @if (orderStatus === null) {
        <button
          class="secondary-button"
          id="order-confirm-draft"
          data-action="order-confirm-draft"
          mat-stroked-button
          (click)="createDraftOrder()"
        >
          {{ 'LIB_ORDERS.COMMON.ORDERS.DRAFT_ORDER' | translate }}
        </button>
      }
      <ng-container *ngTemplateOutlet="draftButtons"></ng-container>
    }
    @if ((canRequestToCancelOrder$ | async) === true) {
      <button mat-flat-button color="primary" class="primary-button" (click)="onIntentToCancel()">
        {{ 'LIB_ORDERS.COMMON.ORDERS.CANCEL_ORDER' | translate }}
      </button>
    }
    @if (orderStatus !== null && orderStatus !== Status.DRAFTED && (canSubmitToCustomerForApproval$ | async) === true) {
      <button mat-stroked-button (click)="submitForCustomerApproval()">
        {{
          (orderStatus === Status.SUBMITTED_FOR_CUSTOMER_APPROVAL
            ? 'LIB_ORDERS.COMMON.ORDERS.RESUBMIT_FOR_CUSTOMER_APPROVAL'
            : 'LIB_ORDERS.COMMON.ORDERS.SUBMIT_FOR_CUSTOMER_APPROVAL'
          ) | translate
        }}
      </button>
    }
    @if ([Status.DECLINED].includes(orderStatus)) {
      <button mat-flat-button color="primary" (click)="onConvertToDraft()">
        {{ 'LIB_ORDERS.COMMON.ORDERS.CONVERT_TO_DRAFT' | translate }}
      </button>
    }
  </div>
</div>

<ng-template #draftButtons>
  @if ((canSubmitToCustomerForApproval$ | async) === true) {
    <button
      mat-stroked-button
      id="order-confirm-submit-customer-c1"
      [trackEvent]="{
        eventName: 'submit-for-customer-approval-toolbar',
        category: 'unified-orders',
        action: 'click',
        properties: { orderStatus: orderStatus },
      }"
      class="secondary-button"
      [disabled]="submitDisabled"
      (click)="submitForCustomerApproval()"
    >
      {{ 'LIB_ORDERS.COMMON.ORDERS.SUBMIT_FOR_CUSTOMER_APPROVAL' | translate }}
    </button>
  }
  @if (config.allowSendDirectToAdmin || (canManageOrder$ | async)) {
    @if (canBypassAdminWorkflowAndActivate$ | async) {
      @if (retailPaymentsEnabled$ | async) {
        <!-- collect paymet if retail payments are enabled -->
        <button
          mat-flat-button
          color="primary"
          class="primary-button"
          [trackEvent]="{
            eventName: 'open-process-order-dialog-toolbar',
            category: 'unified-orders',
            action: 'click',
            properties: { orderStatus: orderStatus },
          }"
          [disabled]="submitDisabled"
          (click)="onCollectPayment()"
        >
          {{ 'LIB_ORDERS.COMMON.ORDERS.PROCESS_ORDER' | translate }}
        </button>
      } @else {
        <!-- otherwise we want to process the order immediately -->
        <button
          mat-flat-button
          color="primary"
          class="primary-button"
          [trackEvent]="{
            eventName: 'process-order-toolbar',
            category: 'unified-orders',
            action: 'click',
            properties: { orderStatus: orderStatus },
          }"
          [disabled]="submitDisabled"
          (click)="onProcessOrder()"
        >
          {{ 'LIB_ORDERS.COMMON.ORDERS.PROCESS_ORDER' | translate }}
        </button>
      }
    } @else {
      <button
        mat-flat-button
        color="primary"
        class="primary-button"
        id="order-confirm-submit-c1"
        data-action="order-confirm-submit"
        [disabled]="submitDisabled"
        (click)="submitOrder()"
      >
        {{ 'LIB_ORDERS.COMMON.ORDERS.SUBMIT_ORDER' | translate }}
      </button>
    }
  }
  @if (customSalespersonActions) {
    <glxy-button-group class="navigation-button-group">
      <ng-container *ngIf="customSalespersonActions.length > 0">
        <button
          [id]="customSalespersonActions[0].id"
          [attr.data-action]="customSalespersonActions[0].dataAction"
          mat-flat-button
          color="primary"
          class="menu-button"
          [disabled]="submitDisabled"
          (click)="customSalespersonActions[0].action()"
        >
          {{ customSalespersonActions[0].label }}
        </button>
      </ng-container>
      <ng-container *ngIf="customSalespersonActions.length > 1">
        <button
          id="order-confirm-menu-c1"
          class="dropdown-icon"
          mat-flat-button
          color="primary"
          [disabled]="submitDisabled"
          [matMenuTriggerFor]="submitMenu1"
        >
          <mat-icon>arrow_drop_down</mat-icon>
        </button>
        <mat-menu #submitMenu1="matMenu">
          <ng-container *ngFor="let actionButton of customSalespersonActions; let first = first">
            <!-- we already rendered the first button above, don't do it again -->
            <ng-container *ngIf="!first">
              <button
                [id]="actionButton.id"
                [attr.data-action]="actionButton.dataAction"
                mat-menu-item
                (click)="actionButton.action()"
              >
                {{ actionButton.label }}
              </button>
            </ng-container>
          </ng-container>
        </mat-menu>
      </ng-container>
    </glxy-button-group>
  }
</ng-template>
