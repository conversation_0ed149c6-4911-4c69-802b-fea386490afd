@if (loading === false) {
  @if (error === false) {
    @if (order$ | async; as order) {
      <div class="content" id="top">
        <div class="non-printable">
          <div class="header">
            @if (orderFormOptions?.readOnly && !isInPreviewMode) {
              <glxy-alert>
                {{ 'LIB_ORDERS.COMMON.ORDERS.NO_LONGER_AVAILABLE' | translate }}
              </glxy-alert>
            }
          </div>
        </div>

        <div class="header">
          <div class="header-info">
            <div class="page-title-container">
              <h1 class="page-title">{{ title }}</h1>
            </div>
            <h2 class="page-subtitle">{{ subtitle }}</h2>
          </div>
        </div>

        <sales-ui-sales-order-details-card
          #orderDetailsCard
          [order]="order"
          [orderDetails]="[]"
          [isCustomerDetails]="true"
          [agreements]="orderDetailAgreements"
        ></sales-ui-sales-order-details-card>
        @if ((unifiedOrderPageEnabled$ | async) === true) {
          @if (itemPricingTableConfig$ | async; as tableConfig) {
            <inventory-ui-item-pricing-table
              id="inventory-pricing-table"
              [config]="tableConfig"
              [selectedItems]="orderItems$ | async | lineItemsToInventoryItems"
              [viewOnly]="true"
            />
          }

          @if (retailSummary$ | async; as summary) {
            <orders-retail-summary
              [summary]="summary"
              [lineItems]="order.lineItems"
              [contractStartDate]="order.requestedActivation"
            >
            </orders-retail-summary>
          }
        } @else {
          <orders-pricing
            [orderItems]="orderItems$ | async"
            [taxOptions]="taxOptions$ | async"
            [duration]="contractDuration$ | async"
            [partnerId]="order.partnerId"
            [marketId]="order.marketId"
          ></orders-pricing>
        }

        <orders-form
          #salesOrderForm
          [order]="order$ | async"
          [orderConfig]="orderConfig$ | async"
          [orderFormOptions]="orderFormOptions"
          [fileUploadUrl]="fileUploadUrl"
          [userOptions]="userOptions"
          [parentForm]="orderFormGroup"
          [partnerId]="order.partnerId"
          [businessId]="order.businessId"
        ></orders-form>

        @if (order.customerAttachments?.length > 0) {
          <div [class]="'customer-attachments'">
            <mat-card appearance="outlined">
              <mat-card-header>
                <mat-card-title>
                  <div>
                    {{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.CUSTOMER_ATTACHMENTS' | translate }}
                  </div>
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <orders-customer-attachments
                  [ngClass]="{ 'non-printable': !order.customerAttachments }"
                  [files]="order.customerAttachments"
                  [orderId]="order.orderId"
                  [businessId]="order.businessId"
                  [editingDisabled]="true"
                  [hideSubtitle]="true"
                />
              </mat-card-content>
            </mat-card>
          </div>
        }

        @if (orderFormOptions?.readOnly === false || isInPreviewMode) {
          <div class="order-section">
            @if (
              ((order.status === statusSubmittedForCustomerApproval && order?.orderIsSmbPayable) ||
                isSMBPayableOverride) &&
              !!retailProvider
            ) {
              <mat-card appearance="outlined" class="order-section">
                <mat-card-header>
                  <mat-card-title>{{ 'LIB_ORDERS.COMMON.ORDERS.PAYMENT' | translate }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <orders-payment-element-form
                    #creditCardForm
                    [stripeKey]="stripeKey"
                    [retailProvider]="retailProvider"
                    [accountGroupId]="order.businessId"
                    [partnerId]="order.partnerId"
                    [readOnly]="isInPreviewMode"
                  ></orders-payment-element-form>
                </mat-card-content>
              </mat-card>
            }

            @if (orderConfig$ | async; as orderConfig) {
              <mat-card appearance="outlined" class="confirmation-section">
                <mat-card-content>
                  @if (partnerName) {
                    <orders-terms
                      #terms
                      [orderConfig]="orderConfig"
                      [partnerName]="partnerName"
                      [parentForm]="termsFormGroup"
                    ></orders-terms>
                  }
                  <div>
                    <glxy-form-field [required]="true" class="signature-form-field">
                      <glxy-label>Add Signature</glxy-label>
                      <input type="text" placeholder="Add Signature" [formControl]="signature" matInput />
                      @if (signature.invalid && signature.touched) {
                        <glxy-error>
                          {{ signatureError$ | async }}
                        </glxy-error>
                      }
                    </glxy-form-field>
                  </div>
                  @if (!signature.invalid) {
                    <div class="signature-handwritten">
                      <glxy-label>{{ signature.value }}</glxy-label>
                    </div>
                  }
                </mat-card-content>
              </mat-card>
            }
          </div>
        }

        @if (order.status === statusSubmittedForCustomerApproval || isInPreviewMode) {
          <div>
            <mat-card appearance="outlined" class="sticky-card">
              <mat-card-content class="sticky-footer-actions">
                <ng-container>
                  <div>
                    <button
                      mat-raised-button
                      color="primary"
                      data-action="approve-button"
                      [disabled]="disableSubmitButton$ | async"
                      (click)="onApprove()"
                    >
                      @if (
                        !!retailProvider &&
                        ((order.status === statusSubmittedForCustomerApproval && order?.orderIsSmbPayable) ||
                          isSMBPayableOverride)
                      ) {
                        {{ 'LIB_ORDERS.COMMON.ORDERS.PURCHASE' | translate }}
                      } @else {
                        {{ 'LIB_ORDERS.COMMON.ORDERS.AGREE_TO_PURCHASE' | translate }}
                      }
                    </button>
                    <button
                      mat-button
                      color="primary"
                      data-action="decline-button"
                      [disabled]="disableSubmitButton$ | async"
                      (click)="onDecline()"
                    >
                      {{ 'LIB_ORDERS.COMMON.ORDERS.DECLINE' | translate }}
                    </button>
                  </div>
                </ng-container>
              </mat-card-content>
            </mat-card>
          </div>
        }
      </div>
    }
  } @else {
    <glxy-empty-state data-testid="error-component">
      <glxy-empty-state-hero>
        <mat-icon>info_outline</mat-icon>
      </glxy-empty-state-hero>
      <glxy-empty-state-title>
        {{ 'LIB_ORDERS.COMMON.ORDERS.ERROR' | translate }}
      </glxy-empty-state-title>
      <p>{{ 'LIB_ORDERS.COMMON.ORDERS.ERROR_LOADING_MESSAGE' | translate }}</p>
      <glxy-empty-state-actions>
        <a mat-stroked-button (click)="refreshPage()">
          {{ 'LIB_ORDERS.COMMON.ORDERS.TRY_AGAIN' | translate }}
        </a>
      </glxy-empty-state-actions>
    </glxy-empty-state>
  }
}
