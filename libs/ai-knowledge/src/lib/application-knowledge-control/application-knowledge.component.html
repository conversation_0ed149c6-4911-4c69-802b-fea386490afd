<ng-container *ngIf="!isLoading(); else loading">
  <ai-knowledge-knowledge-list
    [knowledgeSources]="selectedSources().sources"
    [allowRemoveBusinessProfile]="true"
    (titleClicked)="viewSource($event)"
    (removeClicked)="removeSource($event)"
  >
  </ai-knowledge-knowledge-list>
  <glxy-form-field class="knowledge-select">
    <input
      #knowledgeSourceInput
      matInput
      #trigger="matAutocompleteTrigger"
      placeholder="{{ 'ADD_KNOWLEDGE' | translate }}"
      [formControl]="selectedSourceControl"
      autocomplete="off"
      (keydown.enter)="chooseMatch($event)"
      [matAutocomplete]="auto"
    />
    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="addSource($event.option.value)">
      <mat-option class="source-option">
        <a matLine>
          {{ 'ADD_NEW_KNOWLEDGE' | translate }}
        </a>
      </mat-option>
      <mat-divider *ngIf="filteredSources().length > 0"></mat-divider>
      <mat-option *ngFor="let source of filteredSources()" [value]="source" class="source-option">
        <div matLine>
          {{ source.name }}
          <glxy-badge [size]="'small'" *ngIf="source.isSuggested">{{ 'SUGGESTED' | translate }}</glxy-badge>
        </div>
        <div matLine class="source-preview">{{ getKnowledgePreview(source) }}</div>
      </mat-option>
    </mat-autocomplete>
  </glxy-form-field>
</ng-container>

<ng-template #loading>
  <glxy-loading-spinner></glxy-loading-spinner>
</ng-template>
