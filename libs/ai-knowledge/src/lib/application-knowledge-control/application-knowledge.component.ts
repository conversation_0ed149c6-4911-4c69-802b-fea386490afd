import {
  Component,
  computed,
  DestroyRef,
  effect,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnInit,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocomplete, MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { KnowledgeSource, KnowledgeSourceConfigType } from '@vendasta/embeddings';
import { AiKnowledgeService } from '../ai-knowledge.service';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { KnowledgeListComponent } from '../knowledge-list/knowledge-list.component';
import { MatDialog, MatDialogConfig, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ACCOUNT_GROUP_ID_TOKEN, SHOW_BUSINESS_PROFILE_SOURCE_TOKEN } from '../tokens';
import { AiKnowledgeI18nModule } from '../assets/i18n/ai-knowledge-i18n.module';
import { MatDividerModule } from '@angular/material/divider';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { firstValueFrom, map, switchMap, take } from 'rxjs';
import { KnowledgeSourceWithSuggestedFlag } from '../ai-knowledge-util';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AddKnowledgeComponent } from '../knowledge-editor/add-knowledge/add-knowledge.component';
import { WebsiteScrapeComponent } from '../knowledge-editor/website-scrape/website-scrape.component';
import { CustomDataComponent } from '../knowledge-editor/custom-data/custom-data.component';
import { BusinessProfileComponent } from '../knowledge-editor/business-profile/business-profile.component';
import { StepChangeEvent } from '../knowledge-editor/common/interfaces';

@Component({
  selector: 'ai-knowledge-application-knowledge',
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    AiKnowledgeI18nModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatAutocompleteModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    GalaxyLoadingSpinnerModule,
    GalaxyFormFieldModule,
    KnowledgeListComponent,
    MatDialogModule,
    MatDividerModule,
    GalaxyBadgeModule,
    MatCheckboxModule,
  ],
  providers: [AiKnowledgeService],
  templateUrl: './application-knowledge.component.html',
  styleUrls: ['./application-knowledge.component.scss'],
})
export class ApplicationKnowledgeComponent implements OnInit {
  @Input() appId: string;
  @Input() disabled: boolean;

  readonly selectedSourcesChange = output<{ sources: KnowledgeSource[]; initialLoad?: boolean }>();

  DIALOG_WIDTH = '660px';
  private readonly dialog = inject(MatDialog);
  private readonly bpObserver = inject(BreakpointObserver);

  private readonly accountGroupID = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));

  private readonly knowledgeService = inject(AiKnowledgeService);
  private readonly showBusinessProfileSource = toSignal(inject(SHOW_BUSINESS_PROFILE_SOURCE_TOKEN));

  private readonly accountGroupService: AccountGroupService;

  private suggestedWebsiteURL = '';
  private hasWebsiteKS = false;

  sources = signal<KnowledgeSourceWithSuggestedFlag[]>([]);
  searchValue = signal<string>('');
  selectedSources = signal<{ sources: KnowledgeSourceWithSuggestedFlag[]; initialLoad: boolean }>(null);
  filteredSources = computed(() =>
    this.sources().filter(
      (source) =>
        source.name.toLowerCase().indexOf(this.searchValue()) >= 0 &&
        !this.selectedSources().sources.some((selected) => selected.id === source.id),
    ),
  );
  readonly isLoading = signal(true);

  selectedSourceControl = new FormControl();

  @ViewChild('knowledgeSourceInput') knowledgeSourceInput: ElementRef<HTMLInputElement>;
  @ViewChild('auto') matAutocomplete: MatAutocomplete;
  @ViewChild('trigger') matAutocompleteTrigger: MatAutocompleteTrigger;

  constructor(private readonly destroyRef: DestroyRef) {
    if (this.accountGroupID()) {
      this.accountGroupService = inject(AccountGroupService);
    }
    effect(() => {
      this.selectedSourcesChange.emit(this.selectedSources());
    });

    effect(async () => {
      const accountGroupID = this.knowledgeService.accountGroupId();
      const partnerID = this.knowledgeService.partnerId();
      if (accountGroupID || partnerID) {
        this.isLoading.set(true);
        await this.initialize();
      }
    });
  }

  private async refreshSelectedSources(initialLoad?: boolean): Promise<void> {
    this.selectedSources.set({ sources: [], initialLoad: !!initialLoad });
    let initialSources: KnowledgeSourceWithSuggestedFlag[] = [];
    if (this.appId) {
      initialSources = await this.knowledgeService.listAllKnowledgeSourcesForApp(this.appId);
    } else if (this.showBusinessProfileSource()) {
      const businessProfileSource = this.knowledgeService.buildBusinessProfileKnowledgeSource(true);
      if (businessProfileSource) {
        initialSources.push(businessProfileSource);
      }

      if (this.suggestedWebsiteURL && !this.hasWebsiteKS) {
        const businessWebsiteSource = this.knowledgeService.buildBusinessWebsiteKnowledgeSource(
          this.suggestedWebsiteURL,
          true,
        );
        if (businessWebsiteSource) {
          initialSources.push(businessWebsiteSource);
        }
      }
    }
    this.selectedSources.set({ sources: initialSources, initialLoad: !!initialLoad });
  }

  async ngOnInit(): Promise<void> {
    this.selectedSourceControl.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((searchValue) => {
      if (typeof searchValue === 'string') {
        this.searchValue.set(searchValue?.toLowerCase());
      }
    });
  }

  private async initialize() {
    await this.checkWebsiteKnowledgeSource();
    await this.refreshSources();
    await this.refreshSelectedSources(true);
    this.isLoading.set(false);
  }

  // Checks if a website source is available and if there is a website URL available
  // Register all results in local variables
  async checkWebsiteKnowledgeSource() {
    if (this.showBusinessProfileSource()) {
      // Gets the website URL from the account group
      const accountGroup = await firstValueFrom(
        this.accountGroupService.get(this.accountGroupID(), new ProjectionFilter({ napData: true })),
      );
      this.suggestedWebsiteURL = accountGroup?.napData?.website;

      // Checks if there are any website sources
      const existingWebsiteKnowledgeSources = await this.knowledgeService.listKnowledgeSources(
        [KnowledgeSourceConfigType.KNOWLEDGE_SOURCE_CONFIG_TYPE_WEBSITE_SCRAPE],
        { pageSize: 1 },
      );
      this.hasWebsiteKS = existingWebsiteKnowledgeSources?.knowledgeSources?.length > 0;
    }
  }

  async refreshSources(): Promise<void> {
    // TODO: actually page and search on value changes
    const sourcesResult = await this.knowledgeService.listKnowledgeSources(
      [
        KnowledgeSourceConfigType.KNOWLEDGE_SOURCE_CONFIG_TYPE_CUSTOM_DATA,
        KnowledgeSourceConfigType.KNOWLEDGE_SOURCE_CONFIG_TYPE_WEBSITE_SCRAPE,
      ],
      { pageSize: 100 },
    );
    const knowledgeSources = sourcesResult.knowledgeSources || [];
    if (this.showBusinessProfileSource()) {
      // manually insert business profile source because it may not exist yet
      const businessProfileSource = this.knowledgeService.buildBusinessProfileKnowledgeSource(true);
      if (businessProfileSource) {
        knowledgeSources.unshift(businessProfileSource);
      }

      // Adds a suggestion to add a new website source if there are none available and we have a website URL
      if (this.suggestedWebsiteURL && !this.hasWebsiteKS) {
        const businessWebsiteSource = this.knowledgeService.buildBusinessWebsiteKnowledgeSource(
          this.suggestedWebsiteURL,
          true,
        );
        if (businessWebsiteSource) {
          knowledgeSources.unshift(businessWebsiteSource);
        }
      }
    }
    this.sources.set(knowledgeSources);
    this.clearInputFields();
  }

  async forceRefreshSources(): Promise<void> {
    this.isLoading.set(true);
    await this.refreshSources();
    this.isLoading.set(false);
  }

  async viewSource(source: KnowledgeSourceWithSuggestedFlag): Promise<void> {
    const conf: MatDialogConfig = {
      width: this.DIALOG_WIDTH,
      data: {
        knowledgeSourceKey: { knowledgeSourceId: source?.id, namespace: source?.namespace },
        excludeAddToAIStep: true,
        config: source?.config,
        isSuggested: source?.isSuggested,
        currentAppId: this.appId,
      },
    };

    const isSmallScreen = this.bpObserver.isMatched(Breakpoints.XSmall);
    if (isSmallScreen) {
      conf.maxWidth = '100vw';
      conf.maxHeight = '100vh';
    }

    let editor: MatDialogRef<StepChangeEvent>;
    if (source?.config?.websiteScrapeConfig) {
      editor = this.dialog.open(WebsiteScrapeComponent, conf);
    } else if (source?.config?.customDataConfig) {
      editor = this.dialog.open(CustomDataComponent, conf);
    } else {
      editor = this.dialog.open(BusinessProfileComponent, conf);
    }
    const navigatingAway = await firstValueFrom(editor.afterClosed());
    if (!navigatingAway && !source?.isSuggested) {
      this.isLoading.set(true);
      const updatedKnowledge = await this.knowledgeService.getKnowledgeSource(source?.id, source?.namespace);
      this.selectedSources.update((current) => {
        const index = current.sources.findIndex((item) => item.id === source?.id);
        if (index !== -1) {
          current.sources[index] = updatedKnowledge;
        }
        return current;
      });
      this.isLoading.set(false);
    }
  }

  removeSource(source: KnowledgeSource): void {
    this.selectedSources.update((selectedSources) => {
      const index = selectedSources.sources.indexOf(source);
      if (index >= 0) {
        selectedSources.sources.splice(index, 1);
      }
      return { sources: [...selectedSources.sources], initialLoad: false };
    });
  }

  addSource(source: KnowledgeSource | undefined): void {
    if (!source) {
      this.addKnowledge();
      return;
    }

    this.selectedSources.update((selectedSources) => {
      const sourceIds = selectedSources.sources.map((g) => g.id);
      if (!sourceIds.includes(source.id)) {
        selectedSources.sources.push(source);
      }
      this.clearInputFields();
      this.matAutocompleteTrigger.closePanel();
      return { sources: [...selectedSources.sources], initialLoad: false };
    });
  }

  chooseMatch(event: any): void {
    event.preventDefault();
    if (this.filteredSources().length > 1) {
      // Do not select if there are multiple matches
      return;
    }
    if (this.filteredSources().length === 1) {
      const sourceToAdd = this.filteredSources()[0];
      this.addSource(sourceToAdd);
    }
  }

  clearInputFields(): void {
    if (this.knowledgeSourceInput?.nativeElement?.value) {
      this.knowledgeSourceInput.nativeElement.value = '';
    }
    this.selectedSourceControl.setValue('');
  }

  async addKnowledge(): Promise<void> {
    const conf: MatDialogConfig = {
      width: this.DIALOG_WIDTH,
      data: { excludeAddToAIStep: true },
    };

    const isSmallScreen = this.bpObserver.isMatched(Breakpoints.XSmall);
    if (isSmallScreen) {
      conf.maxWidth = '100vw';
      conf.maxHeight = '100vh';
    }
    const editor = this.dialog.open(AddKnowledgeComponent, conf);
    editor.componentInstance.componentSelected
      .pipe(takeUntilDestroyed(editor.componentInstance.destroyRef))
      .subscribe((event) => {
        this.subscribeToKnowledgeCreated(event.knowledgeCreated, event.destroyRef);
      });
    await firstValueFrom(editor.afterClosed());
  }

  private subscribeToKnowledgeCreated(event: EventEmitter<string>, destroyRef: DestroyRef): void {
    event
      .pipe(
        takeUntilDestroyed(destroyRef),
        take(1),
        switchMap((sourceId) =>
          this.knowledgeService.getKnowledgeSource(sourceId, this.knowledgeService.buildNamespaceInterface(), false),
        ),
        map((ks) => this.selectedSources().sources.push(ks)),
        map(() => this.selectedSourcesChange.emit(this.selectedSources())),
        switchMap(() => this.forceRefreshSources()),
      )
      .subscribe();
  }

  getKnowledgePreview(source: KnowledgeSource): string {
    return this.knowledgeService.getKnowledgePreview(source);
  }
}
