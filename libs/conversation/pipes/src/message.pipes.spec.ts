import { ChatProfileSvgIconPipe, MessageCodeBacktickPipe, MessageStatusType } from './message.pipes';
import { createPipeFactory, SpectatorPipe } from '@ngneat/spectator';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN } from '../../core/src/lib/tokens';
import { ConversationMessage, ConversationEvent, EventInfo } from '../../core/src/lib/interface/conversation.interface';
import { HostAppInterface } from '../../core/src/lib/interface/host-app.interface';
import { EventType } from '@vendasta/conversation';

describe('MessageCodeBacktickPipe', () => {
  let spectator: SpectatorPipe<MessageCodeBacktickPipe>;
  const createPipe = createPipeFactory(MessageCodeBacktickPipe);

  it('should return the formatted code block', () => {
    const text = `
    # Fake heading that should not be parsed
    Here is some text with a code block:
    \`\`\`typescript
    const message = "Hello, world!";
    console.log(message);
    \`\`\`
    That was the code block.
    There is another code block below:
    \`\`\`typescript
    const message = "Hello, world!";
    console.log(message);
    \`\`\`
    `;

    // Watch out for the indentation in the expected string
    const expected = `
    # Fake heading that should not be parsed
    Here is some text with a code block:
    <pre><code>    const message = "Hello, world!";
    console.log(message);
    </code></pre>
    That was the code block.
    There is another code block below:
    <pre><code>    const message = "Hello, world!";
    console.log(message);
    </code></pre>
    `;

    spectator = createPipe(`{{ message | messageCodeBacktick }}`, {
      hostProps: {
        message: text,
      },
    });
    expect(spectator.element.textContent).toContain(expected);
  });

  it('should ignore unclosed single backtick', () => {
    spectator = createPipe(`{{ message | messageCodeBacktick }}`, {
      hostProps: {
        message: '`my code',
      },
    });
    expect(spectator.element.textContent).toContain('`my code');
  });

  it('should ignore unclosed triple backticks', () => {
    spectator = createPipe(`{{ message | messageCodeBacktick }}`, {
      hostProps: {
        message: `
        \`\`\`my code`,
      },
    });
    expect(spectator.element.textContent).toContain('```my code');
  });
});
describe('ChatProfileSvgIconPipe', () => {
  let spectator: SpectatorPipe<ChatProfileSvgIconPipe>;
  const createPipe = createPipeFactory(ChatProfileSvgIconPipe);

  it('should return the svg icon from the message group if it exists', () => {
    const wrappedInboxItem = {
      lastItem: {
        id: '123',
        item: {
          event: {
            id: '123',
          },
          eventInfo: {
            svgIcon: 'icon-svg-string',
          },
        },
      },
    };

    spectator = createPipe(`{{ wrappedInboxItem | chatProfileSvgIcon }}`, {
      hostProps: {
        wrappedInboxItem,
      },
    });

    expect(spectator.element.textContent).toContain('icon-svg-string');
  });

  it('should return an empty string if no svgIcon is found', () => {
    const wrappedInboxItem = {
      lastItem: {
        id: '123',
        item: {
          event: {
            id: '123',
          },
          eventInfo: {
            svgIcon: '',
          },
        },
      },
    };

    spectator = createPipe(`{{ wrappedInboxItem | chatProfileSvgIcon }}`, {
      hostProps: {
        wrappedInboxItem,
      },
    });

    expect(spectator.element.textContent).toBe('');
  });

  it('should return an empty string if there is no eventInfo object', () => {
    const wrappedInboxItem = {
      lastItem: {
        id: '123',
        item: {
          event: {
            id: '123',
          },
        },
      },
    };

    spectator = createPipe(`{{ wrappedInboxItem | chatProfileSvgIcon }}`, {
      hostProps: {
        wrappedInboxItem,
      },
    });

    expect(spectator.element.textContent).toBe('');
  });
});

describe('MessageStatusType', () => {
  let spectator: SpectatorPipe<MessageStatusType>;
  let mockHostAppInterface: Partial<HostAppInterface>;

  const mockMessage: Partial<ConversationMessage> = {
    messageId: 'test-message-id',
    body: 'test message',
  };

  const createMockEvent = (isOutBound: boolean): Partial<ConversationEvent> => {
    const mockEventInfo: Partial<EventInfo> = {
      id: 'test-id',
      title: 'test-title',
      content: 'test-content',
      sentBy: 'test-sender',
      sentVia: 'test-via',
      EventType: EventType.EVENT_TYPE_UNDEFINED,
      IsOutBound: isOutBound,
      icon: 'test-icon',
    };

    return {
      eventInfo: mockEventInfo as EventInfo,
    };
  };

  const createPipe = createPipeFactory({
    pipe: MessageStatusType,
    providers: [
      {
        provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
        useFactory: () => mockHostAppInterface,
      },
    ],
  });

  beforeEach(() => {
    mockHostAppInterface = {
      isSenderFromOrganization: jest.fn(),
    };
  });

  describe('when hostAppInterface is available', () => {
    it('should return Sent when hostAppInterface.isSenderFromOrganization returns true for ConversationMessage', () => {
      (mockHostAppInterface.isSenderFromOrganization as jest.Mock).mockReturnValue(true);

      spectator = createPipe(`{{ message | messageStatusType }}`, {
        hostProps: {
          message: mockMessage,
        },
      });

      expect(mockHostAppInterface.isSenderFromOrganization).toHaveBeenCalledWith(mockMessage);
      expect(spectator.element.textContent?.trim()).toBe('sent');
    });

    it('should return Received when hostAppInterface.isSenderFromOrganization returns false for ConversationMessage', () => {
      (mockHostAppInterface.isSenderFromOrganization as jest.Mock).mockReturnValue(false);

      spectator = createPipe(`{{ message | messageStatusType }}`, {
        hostProps: {
          message: mockMessage,
        },
      });

      expect(mockHostAppInterface.isSenderFromOrganization).toHaveBeenCalledWith(mockMessage);
      expect(spectator.element.textContent?.trim()).toBe('received');
    });

    it('should return Sent when ConversationEvent has IsOutBound true', () => {
      const mockEvent = createMockEvent(true);

      spectator = createPipe(`{{ event | messageStatusType }}`, {
        hostProps: {
          event: mockEvent,
        },
      });

      expect(mockHostAppInterface.isSenderFromOrganization).not.toHaveBeenCalled();
      expect(spectator.element.textContent?.trim()).toBe('sent');
    });

    it('should return Received when ConversationEvent has IsOutBound false', () => {
      const mockEvent = createMockEvent(false);

      spectator = createPipe(`{{ event | messageStatusType }}`, {
        hostProps: {
          event: mockEvent,
        },
      });

      expect(mockHostAppInterface.isSenderFromOrganization).not.toHaveBeenCalled();
      expect(spectator.element.textContent?.trim()).toBe('received');
    });
  });

  describe('when hostAppInterface is not available (optional injection)', () => {
    const createPipeWithoutInterface = createPipeFactory({
      pipe: MessageStatusType,
      providers: [
        // No CONVERSATION_HOST_APP_INTERFACE_TOKEN provider
      ],
    });

    it('should return Received for ConversationMessage when hostAppInterface is not available', () => {
      spectator = createPipeWithoutInterface(`{{ message | messageStatusType }}`, {
        hostProps: {
          message: mockMessage,
        },
      });

      expect(spectator.element.textContent?.trim()).toBe('received');
    });

    it('should still handle ConversationEvent correctly when hostAppInterface is not available', () => {
      const mockEvent = createMockEvent(true);

      spectator = createPipeWithoutInterface(`{{ event | messageStatusType }}`, {
        hostProps: {
          event: mockEvent,
        },
      });

      expect(spectator.element.textContent?.trim()).toBe('sent');
    });
  });
});
