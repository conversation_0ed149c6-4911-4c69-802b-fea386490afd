import { inject, Pipe, PipeTransform } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { EnvironmentService } from '@galaxy/core';
import { TranslateService } from '@ngx-translate/core';
import {
  ConversationChannel,
  MessageType,
  Participant,
  ParticipantType,
  UIComponentInterface,
} from '@vendasta/conversation';
import { ChatSourceId } from '@vendasta/galaxy/chat-composer';
import { DEFAULT_OPENAI_BOT_NAME, EMAIL_MESSAGE_BODY_MAX_LENGTH } from '../../core/src/lib/inbox.constants';
import { ConversationEvent, ConversationMessage, Media } from '../../core/src/lib/interface/conversation.interface';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN, USER_ID_TOKEN } from '../../core/src/lib/tokens';
import { MessageGroup } from '../../core/src/lib/types';
import { isBookingAvailabilityMessage, isInboundEmailMessage } from '../../core/src/lib/message-utils';
import { map, Observable, of } from 'rxjs';
import { Event } from '@vendasta/conversation/lib/_internal/objects/event';
import { ESCALATE_TO_SUPPORT_ACTION_KEY, TOOL_CALL_ACTION_KEY } from '../../core/src/lib/conversation.constants';

// returns true if message is a System Message type
@Pipe({
  name: 'isSystemMessage',
  standalone: false,
})
export class IsSystemMessagePipe implements PipeTransform {
  transform(message: ConversationMessage): boolean {
    return message?.type === MessageType.MESSAGE_TYPE_SYSTEM;
  }
}

// Determine whether we need to specify message type as 'sent' or 'received'
enum MessageStatus {
  Sent = 'sent',
  Received = 'received',
}

@Pipe({
  name: 'messageStatusType',
  standalone: false,
})
export class MessageStatusType implements PipeTransform {
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN, { optional: true });

  transform(item: ConversationMessage | ConversationEvent): MessageStatus {
    if (isConversationEvent(item)) {
      return item.eventInfo.IsOutBound ? MessageStatus.Sent : MessageStatus.Received;
    }
    return this.hostAppInterface?.isSenderFromOrganization(item) ? MessageStatus.Sent : MessageStatus.Received;
  }
}

@Pipe({
  name: 'chatMessageFrom',
  standalone: false,
})
export class ChatItemFromPipe implements PipeTransform {
  constructor(public translateService: TranslateService) {}

  transform(item: ConversationMessage): string {
    return chatMessageFrom(item) || '';
  }
}

@Pipe({
  name: 'messageCodeBacktick',
  standalone: false,
})
export class MessageCodeBacktickPipe implements PipeTransform {
  transform(message: string): string {
    if (!message) {
      return '';
    }

    const blockPattern = /```(.+?)?\n([\s\S]*?)```/g;
    const inlinePattern = /`([^`]+)`/g;
    const blockFormatted = message.replace(blockPattern, '<pre><code>$2</code></pre>');
    const finalFormat = blockFormatted.replace(inlinePattern, '<code>$1</code>');
    return finalFormat;
  }
}

@Pipe({
  name: 'truncateMessage',
  standalone: false,
})
export class TruncateMessagePipePipe implements PipeTransform {
  transform(body: string, msg: ConversationMessage): string {
    if (!body) {
      return '';
    }

    if (isInboundEmailMessage(msg) && body.length > EMAIL_MESSAGE_BODY_MAX_LENGTH) {
      return body.substring(0, EMAIL_MESSAGE_BODY_MAX_LENGTH) + '...';
    }

    return body;
  }
}

@Pipe({
  name: 'replaceNewLine',
  standalone: false,
})
export class ReplaceNewLinePipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';
    return value.replace(/\n/g, '  \n');
  }
}

@Pipe({
  name: 'handleEmptyMessage',
  standalone: false,
})
export class EmptyMessagesPipe implements PipeTransform {
  constructor(public translateService: TranslateService) {}

  transform(messages: ConversationMessage[] | null): ConversationMessage[] {
    if (!messages) return [];
    return messages.map((message) => {
      if (!message.body && !message.media?.length) {
        message.body = this.translateService.instant('INBOX.CHAT.NO_TEXT_CONTENT');
      }
      return message;
    });
  }
}

@Pipe({
  name: 'getMessageMediaUrl',
  standalone: false,
})
export class GetMessageMediaUrlPipe implements PipeTransform {
  constructor(public environmentService: EnvironmentService) {}

  transform(media: Media): string {
    const env = this.environmentService.getEnvironmentString();
    return (
      media?.mediaUrl || `https://storage.googleapis.com/conversations-${env}-shared-files/${media.mediaLocationPath}`
    );
  }
}

@Pipe({
  name: 'isFromYou',
  standalone: false,
})
export class IsFromYouPipe implements PipeTransform {
  private readonly userId = toSignal(inject(USER_ID_TOKEN));

  transform(messageGroup: MessageGroup): boolean {
    return isFromYou(messageGroup, this.userId());
  }
}

@Pipe({
  name: 'chatSourceIconName',
  standalone: false,
})
export class ChatSourceIconName implements PipeTransform {
  transform(channel: ConversationChannel): ChatSourceId | undefined {
    if (!channel) {
      return;
    }

    switch (channel) {
      case ConversationChannel.CONVERSATION_CHANNEL_INTERNAL:
        return undefined;
      case ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS:
        return 'google';
      case ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK:
        return 'facebook';
      case ConversationChannel.CONVERSATION_CHANNEL_SMS:
        return 'sms';
      case ConversationChannel.CONVERSATION_CHANNEL_EMAIL:
        return 'email';
      case ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT:
        return 'webchat';
      case ConversationChannel.CONVERSATION_CHANNEL_OPENAI:
        return undefined;
      case ConversationChannel.CONVERSATION_CHANNEL_INSTAGRAM:
        return 'instagram';
      case ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP:
        return 'whatsapp';
      case ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT:
        return undefined;
      default:
        throw new Error('unsupported channel ' + channel);
    }
  }
}

@Pipe({
  name: 'chatProfileIcon',
  standalone: false,
})
export class ChatProfileIconPipe implements PipeTransform {
  private readonly userId = toSignal(inject(USER_ID_TOKEN));

  transform(item: MessageGroup): string {
    if (isConversationEvent(item.lastItem.item)) {
      return item.lastItem.item.eventInfo.icon ?? '';
    }

    const unknownContact = !chatMessageFrom(item.lastItem);
    const notYou = !isFromYou(item, this.userId());

    return unknownContact && notYou ? 'person' : '';
  }
}

@Pipe({
  name: 'showBookingAvailability',
  standalone: false,
})
export class ShowBookingAvailabilityPipe implements PipeTransform {
  transform(message: any, messages$: Observable<ConversationMessage[]>): Observable<boolean> {
    if (!isBookingAvailabilityMessage(message)) {
      return of(false);
    }

    return messages$.pipe(
      map((messages) => messages?.[0]),
      map((latestMessage) => {
        if (!latestMessage) {
          return false;
        }

        const bookingAvailabilityMessage = message as ConversationMessage;
        if (latestMessage?.messageId === bookingAvailabilityMessage.messageId) {
          return true;
        }
        return false;
      }),
    );
  }
}

@Pipe({
  name: 'showMessageButton',
  standalone: false,
})
export class ShowMessageButtonPipe implements PipeTransform {
  transform(uiComponent: UIComponentInterface, message: ConversationMessage): boolean {
    if (!uiComponent.button) {
      return false;
    }

    if (uiComponent.button?.action === ESCALATE_TO_SUPPORT_ACTION_KEY) {
      const currentTime = new Date().getTime();
      const messageTime = message.created?.getTime() ?? 0;
      const timeDifference = currentTime - messageTime;
      // Showing the button for 12 hours after the message is sent, user can't open the zendesk from other places.
      return timeDifference < 1000 * 60 * 60 * 12;
    }
    if (uiComponent.button?.action === TOOL_CALL_ACTION_KEY) {
      return true;
    }

    return false;
  }
}

@Pipe({
  name: 'chatProfileSvgIcon',
  standalone: false,
})
export class ChatProfileSvgIconPipe implements PipeTransform {
  transform(messageGroup: MessageGroup): string {
    const lastItem = messageGroup?.lastItem;

    if (isConversationEvent(lastItem.item) && lastItem.item.eventInfo?.svgIcon) {
      return lastItem.item.eventInfo.svgIcon;
    }

    return '';
  }
}

function isConversationEvent(item: ConversationMessage | Event | ConversationEvent): item is ConversationEvent {
  return 'eventInfo' in item;
}

export function chatMessageFrom(item: { sender?: Participant | undefined }): string | undefined {
  if (item.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT) {
    return item?.sender?.name || DEFAULT_OPENAI_BOT_NAME;
  }
  return item?.sender?.name;
}

function isFromYou(messageGroup: MessageGroup, userId: string | undefined): boolean {
  const senderID: string | undefined = messageGroup?.sender?.internalParticipantId;
  if (!senderID) return false;
  return senderID === userId;
}
