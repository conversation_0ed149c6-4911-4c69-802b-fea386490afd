{"INBOX": {"ALL": "All", "SAVE": "Save", "SUBMIT": "Submit", "LOADING_CONVERSATION": "We are loading your conversations...", "YOUR_EXPERT": "Your Expert", "CANCEL": "Cancel", "CLOSE": "Close", "CONFIGURE": "Configure", "CUSTOMERS": "Customers", "SETTINGS_LINK": "Inbox Settings", "MORE": "More", "DONE": "Done", "EDIT": "Edit", "NEXT": "Next", "DELETE": "Delete", "CREATE_NEW": "Create new", "DATE_CREATED": "Date created", "MORE_ACTIONS": "More actions", "CONVERSATION_SUMMARY": "Conversation summary", "ERROR_GETTING_CONVERSATION_SUMMARY": "Error getting conversation summary, try again later.", "SOMETHING_WENT_WRONG": "Something went wrong.", "CHAT_HISTORY": "Chat history", "CONVERSATION_DATE_GROUPS": {"TODAY": "Today", "YESTERDAY": "Yesterday", "LAST_7_DAYS": "Previous 7 days", "LAST_30_DAYS": "Previous 30 days", "OLDER": "Older"}, "EMPTY": {"TITLE": "Message your customers", "SMS_SUBTITLE": "Start an SMS conversation with your customers", "MESSAGE_SUBTITLE": "Start a conversation with your customers", "ACTION": "Send a message", "LEARN": "Learn about Inbox", "ADD_CONTACT": "Add contact", "ADD_A_NEW_CONTACT": "Add a new contact", "ADD_CONTACT_SECONDARY_MESSAGE": "Start a new conversation by adding a contact", "ADD_ACCOUNT": "Add account", "ADD_A_NEW_ACCOUNT": "Add a new account", "ADD_ACCOUNT_SECONDARY_MESSAGE": "Start a new conversation by adding an account", "CONVERSATION_PREVIEW": "New conversation"}, "UNSAVED_CHANGES": {"TITLE": "Unsaved changes", "MESSAGE": "Are you sure you want to leave? You will lose any unsaved changes.", "CONFIRM": "Discard changes"}, "WELCOME": {"TITLE": "Welcome to Inbox", "SUBTITLE": "Where your team collaborates on great customer service.", "SMS_REGISTRATION_LINK_TEXT": "Register to send SMS messages", "FB_LINK_TEXT": "Get messages from Facebook", "WEBCHAT_LINK_TEXT": "Capture leads from your website"}, "WELCOME_AI_CHAT": {"TITLE": "Welcome to AI Assistant <PERSON><PERSON>", "SUBTITLE": "Where you can chat with AI Assistant to help you get stuff done!", "CREATE_CONVERSATION_TEXT": "Create conversation"}, "AI_CHAT": {"NO_MESSAGES": {"TITLE": "Start a conversation with {{<PERSON><PERSON><PERSON>}}", "DESCRIPTION": "Get help with your business by asking {{assistant<PERSON><PERSON>}} anything. Try one of these suggestions:", "DESCRIPTION_WITH_SUGGESTIONS": "Get help with your business by asking {{assistant<PERSON><PERSON>}} anything. Try one of these suggestions:", "DESCRIPTION_WITHOUT_SUGGESTIONS": "Get help with your business by asking {{assistant<PERSON><PERSON>}} anything."}, "SUGGESTIONS": {"HELP_GETTING_STARTED": "Help me get started", "WALK_THROUGH_FEATURE": "Walk me through a feature", "HELP_FIXING_SOMETHING": "Help me fix something"}, "FOOTER": {"LABEL": "This conversation is private", "DESCRIPTION": "This chat is visible only to you, though it may be reviewed to improve support quality. Avoid sharing sensitive or personal information."}}, "WELCOME_CHAT_WITH_PARTNER": {"SUBTITLE_1": "Questions? We're available to help.", "SUBTITLE_2": "Chat directly with the team at {{ partnerBrand<PERSON>ame }}."}, "SENDER": {"MARK_UNREAD": "Mark conversation as unread", "MARK_READ": "Mark conversation as read", "EDIT": "Edit", "VIEW_CONTACT": "View contact", "GO_TO_ACCOUNT": "Go to account", "OPEN_BUSINESS_APP": "Open Business App", "BLOCK": "Block", "UNSUBSCRIBE": "Unsubscribe", "OPEN_CONVERSATION": "Open conversation", "CLOSE_CONVERSATION": "Close conversation", "MARK_AS_CLOSED": "Close conversation", "SUMMARIZE_CONVERSATION": "Summarize conversation", "TURN_ON_AI_REPLIES": "Turn on AI replies for conversation", "TURN_OFF_AI_REPLIES": "Turn off AI replies for conversation"}, "CHAT": {"ENTER_MESSAGE": "Enter message", "SEND": "Send", "SENDING": "Sending ...", "TYPING": "Typing ...", "FAILED_SENDING": "Failed to send message.", "HAS_PHONE_NUMBER": "Messages sent via {{ phone_number }}", "NO_PHONE_NUMBER": "You will be assigned an SMS number when you send your first message", "MEDIA_NOT_SUPPORTED": "This message contains a media file which cannot be displayed in Inbox currently. You may request they try sending it to you by another method.", "INSERT_REVIEW_LINK": "Ask for review", "NO_REVIEW_LINKS_FOUND": "Sorry there aren't any review links yet. Add them in {{ cvWhiteLabelName }}", "SEND_MESSAGE": "Send message", "GET_ACCESS_TO_REVIEW_LINKS": "Get quick access to your review links from many sites and more...", "GET_ACCESS_TO_REVIEW_LINKS_CONTACT_US": "Get quick access to your review links from many sites and more. Contact us for more info.", "GET_ACCESS_TO_REVIEW_LINKS_ERROR": "Error getting data, please try again later.", "CONTACT_US": "Contact us", "SETUP_REVIEW_LINKS": "Setup review links", "VIA_GOOGLE": "via Google", "VIA_FACEBOOK": "via Facebook", "VIA_INSTAGRAM": "via Instagram", "VIA_WHATSAPP": "via WhatsApp", "VIA_SMS": "via SMS", "VIA_EMAIL": "via Email", "VIA_WEBCHAT": "via Web Chat", "VIA_WEB_FORM": "via Form", "VIA_PHONE": "via Phone", "NEW_FORM_SUBMISSION": "New Submission", "UNKNOWN_CONTACT": "Unknown Contact", "RESPOND_TO_LEAD_AT_PHONE_NUMBER": "Respond to this lead at {{phone}}.", "RESPOND_TO_LEAD_AT_EMAIL": "Respond to this lead at {{email}}.", "RESPOND_TO_LEAD_AT_PHONE_NUMBER_OR_EMAIL": "Respond to this lead at {{phone}} or {{email}}.", "VIEW_ORIGINAL_MESSAGE": "View original message", "AI_WAITING_RESPONSE": "Will reply in", "AI_RESPONSE_CANCELLED": "AI response cancelled", "AI_RESPONSE_ENABLED_FOR_CONVERSATION": "Active", "AI_RESPONSE_WAITING_POP_OVER": "{{assistant<PERSON><PERSON>}} is waiting before automatically responding to an unanswered SMS message from a customer to give your team time to respond. When the timer reaches zero, {{assistant<PERSON><PERSON>}} will send a reply.", "AI_RESPONSE_ACTIVE_POP_OVER": "{{assistant<PERSON><PERSON>}} will automatically respond to inbound SMS messages immediately. {{assistant<PERSON><PERSON>}} will stop automatically responding when someone from your business engages in the conversation.", "NOTIFICATIONS_DISABLED": "Your push notifications are disabled.", "ALLOW_PUSH_NOTIFICATIONS": "Allow push notifications", "WHATS_APP_TEMPLATE_LANGUAGE_LABEL": "Language", "NO_TEXT_CONTENT": "[<b>No text content</b>]", "OPEN_SIDE_PANEL_TOOLTIP": "Open in side panel", "LIVE_CHAT_NOT_AVAILABLE": "Live chat is currently not available, please try again later"}, "NEW_MESSAGE": {"NEW_MESSAGE": "New message", "NEW_MESSAGE_FROM": "New message from", "SEND_TO": "Send to:", "NAME": "Name", "PHONE_NUMBER_OR_CONTACT_NAME": "Phone number or contact name", "PHONE_NUMBER_EMAIL_OR_CONTACT_NAME": "Phone number, email or contact name", "EMAIL": "E-mail", "PHONE": "Phone", "FIRST_NAME": "First name", "LAST_NAME": "Last name", "NAME_OR_PHONE": "Name or phone", "CREATE_A_NEW_CONTACT": "Create a new contact", "ADD_CONTACT": "Add contact", "ADDING_CONTACT": "Adding contact ...", "SUGGESTED_CONTACTS": "Suggested contacts", "START_CONVERSATION": "Start conversation", "CUSTOMER": "Customer", "INTERNAL": "Internal", "SEND_MESSAGE_TO_TEAM": "Send message to your team members: ", "CHOOSE_AN_ACCOUNT": "Choose an account to send a message", "CHOOSE_A_CONTACT": "Choose a contact to send a message", "ACCOUNT_NAME": "Account name"}, "ADD_CONTACT": {"ADD_CONTACT_FORM_HEADER": "Add contact: {{ businessName }}"}, "CHANNEL_STATUS": {"CONVERSATION_EXPIRED": "This conversation has expired, and can no longer be replied to.", "NO_CUSTOMER_ACCOUNT": "No active customer account is associated with this conversation.", "PROVIDER_NOT_ENABLED": "Message provider is not enabled.", "CUSTOMER_EMAIL_REQUIRED": "Customer email address is required.", "UNAVAILABLE_IN_LOCATION": "Message provider is not available in the current location", "CUSTOMER_PHONE_REQUIRED": "Customer phone number is required.", "WHATSAPP_MESSAGE_TEMPLATES_ONLY": "Initial message limited to approved templates", "WHATSAPP_MESSAGE_TEMPLATES_ONLY_BUT_PENDING_APPROVAL": "Sending via WhatsApp not yet available; awaiting WhatsApp approval. Monitor at ", "CUSTOMER_UNSUBSCRIBED_SMS": "Customer has unsubscribed from SMS messages.", "CUSTOMER_UNSUBSCRIBED_EMAIL": "Customer has unsubscribed from email messages."}, "ERROR": {"PERMISSION": "Sorry, you don't have permission to send a message in this chat", "PERMISSION_DENY": "You are currently unable to access partial conversations. Please reach out to your system administrator for the necessary permissions.", "CHAT_DELETED": "Sorry, this channel is inactivated to receive new messages", "CREATE_CONTACT": "Sorry, could not create contact", "CORRECT_INVALID_REQUIRED_FIELDS": "Please correct the required or invalid fields", "THE_MESSAGE_IS_EMPTY": "The message is empty", "NO_CONTACT_SELECTED": "Select a contact or enter a valid phone number to send a message", "INVALID_CONTACT_SELECTED": "Select an existing contact or provide a valid email address or phone number to send a message", "INVALID_ACCOUNT_SELECTED": "Select an existing account", "CONTACT_EXISTS": "A contact already exists with that phone number and/or email", "UNSUPPORTED_PHONE_NUMBER": "Unsupported number. Please try a different number, or select a contact from search results.", "INVALID_PHONE_NUMBER": "The phone number is invalid", "INVALID_INPUT": "The provided phone number or email is invalid", "DELETED_CUSTOMER": "Deleted Customer", "REQUIRED_FIELD": "This field is required", "INVALID_LENGTH_TOO_FEW_CHARACTERS": "Must be {{ min }} characters or more", "INVALID_EMAIL_ADDRESS": "Invalid email address", "PHONE_OR_EMAIL_REQUIRED": "A phone number or email address is required", "NO_PHONE_NUMBER": "Sorry, you are unable to send messages to this contact, as no phone number has been provided.", "NO_PHONE_NUMBER_OR_EMAIL": "Sorry, you are unable to send messages to this contact, as no phone number or e-mail has been provided.", "BROKEN_MEDIA_URL": "Image is expired or unavailable", "ORIGINAL_MSG_CONTENT_NOT_AVAILABLE": "Original message content is not available", "CONVERSATION_DETAILS": "Error getting conversation details", "SEND_REVIEW_REQUEST": "Error sending review request", "MESSAGE_ERROR": {"UNABLE_TO_DELIVER": "Unable to deliver message. The recipient's phone number may not be a WhatsApp number.", "PAYMENT_ISSUE": "There was an error related to your payment method. Please ensure a payment method is set on your WhatsApp Business Account and that your Facebook Business has a valid address.", "PHONE_NUMBER_REGISTRATION": "There was an issue with your phone number registration. Please register before trying again.", "MORE_THAN_24_HOURS": "More than 24 hours have passed since the last message or the recipient has never sent a message. Please use a message template instead."}, "UNABLE_TO_CREATE_CONVERSATION": "There was an issue creating a conversation. Please contact support or try again later", "WHATSAPP_TEMPLATE": {"CONTACT_MUST_HAVE_NAME": "The contact must have a first name to send this template. Add a first name to the contact and try again."}, "LOAD_CONVERSATIONS": "We couldn't load your conversations. Please try again.", "LOAD_MESSAGES": "We couldn't load your messages. Please try again."}, "POPOVER": {"BUSINESS_INITIATED_CONVERSATIONS": "Inbox SMS Number", "BUSINESS_INITIATED_CONVERSATIONS_DETAIL": "Your business is assigned an SMS number. You can share this number with customers. Please note, this number is for SMS text messages only. Calls cannot be received at this number.", "IN_PLATFORM_CHANNEL_MESSAGE_INFO": "Messages will be sent directly through the platform, and recipients will be notified.", "GOOGLE_BUSINESS_COMMUNICATIONS_CHANNEL_MESSAGE_INFO": "<p>This customer is reaching out via your business listing on Google Maps or Google Search. Your response will be delivered to them via Google Business Messages, and the customer can reply directly from your listing.</p><p>This conversation will automatically expire 30 days after the customer's last response. We recommend asking for their phone number or email address if you want to retain this customer.</p>", "NO_PHONE_NUMBER_PROVISIONED": "You will automatically be assigned a local SMS number when you send your first message.", "IMPERSONATION_TITLE": "You're impersonating another user", "IMPERSONATION_TEXT": "Before sending messages from Inbox, make sure you're not impersonating another user."}, "INFO": {"DELETED": "Deleted", "PARTNER_PLATFORM_LABEL": "Messages sent via platform", "GOOGLE_BUSINESS_COMMUNICATIONS_LABEL": "Google Business Messages", "CONVERSATION_DETAILS": "Conversation details", "CONVERSATION_STATUS_OPENED": "Conversation opened", "CONVERSATION_STATUS_CLOSED": "Conversation closed", "AI_RESPONSES_ENABLED": "AI responses are enabled for this conversation", "AI_RESPONSES_DISABLED": "AI responses are disabled for this conversation", "SHARED_MEDIA": "Shared media"}, "LABELS": {"VENDOR": "<PERSON><PERSON><PERSON>", "PARTNER": "Partner", "ACCOUNT": "Account", "CONTACT": "Contact", "CUSTOMER": "Customer", "APP": "App", "INACTIVE": "Inactive", "ACCOUNT_DELETED": "Account Deleted", "PRIVATE": "Private"}, "TERMS_OF_SERVICE": {"TITLE": "Terms of Service", "FULL_TITLE": "Inbox Terms of Service", "INTRO_FIRST_LINE": "To continue, you need to agree to the ", "ACCEPTANCE_MESSAGE": "I have read and agree to the Terms of Service"}, "COMMON": {"ACTION_LABELS": {"DECLINE": "Decline", "ACCEPT": "Accept", "AGREE": "Agree", "GO_BACK": "Go back", "COPY": "Copy"}, "SNACK_BAR_MESSAGE": {"COPY_SUCCESS": "Copied to clipboard", "CLICK_TO_COPY": "Click to copy"}}, "REQUEST_PAYMENT": {"TITLE": "Send invoice", "ITEM_DESCRIPTION": "Item description", "DUE_DATE": "Due date", "ADD_LINE": "Add line", "DESCRIPTION": "Description", "PRICE": "Price", "SUBTOTAL": "Subtotal", "TAX": "Tax", "TAX_TOOLTIP": "Tax is automatically calculated using the best matching tax rate.", "CONFIGURE_TAX_RATES": "Configure tax rates", "AMOUNT_DUE": "Amount due", "CANCEL": "Cancel", "CREATE": "Create invoice", "VIEW_INVOICES": "View invoices", "INVOICE_CREATE_SUCCESS": "Invoice created", "INVOICE_CREATE_ERROR": "Failed to create invoice. Please try again."}, "REQUEST_REVIEW": {"TITLE": "Send review request?", "CONTENT": "You're about to send a review request to {{ customerName }}.", "SEND_REQUEST": "Send request"}, "UPLOAD_FILE": {"TITLE": "Add file", "SUCCESSFUL_UPLOAD": "Successfully uploaded {{fileName}} file", "MAX_FILES_EXCEEDED": "Only one attachment per message. Send message or remove attachment to add another.", "MAX_FILE_SIZE_EXCEEDED_ARG": "Max file size of {{maxFileSize}}MB exceeded ({{channel}} messaging limit). Please use a different file", "VOICE_RECORDING": {"TITLE": "Record audio clip", "STOP_RECORDING": "Stop recording"}}, "MESSAGE_TEMPLATES": {"TITLE": "Insert templated message", "MODAL": {"EDIT_TEMPLATES": "Edit", "CREATE_TEMPLATE": "Create template", "EMPTY_STATE_TITLE": "Messaging Templates", "EMPTY_STATE_CONTENT": "There are no message templates yet. Customize your templates or create a new one and save time when responding to messages in your Inbox.", "ERROR": "Error inserting template, please try again"}, "FIELDS_NOT_ALL_HYDRATED": "Some dynamic fields could not be populated. Please update them manually."}, "PREVIEW_PANE": {"TITLE": "Inbox messages", "GO_TO_INBOX": "Go to Inbox", "EMPTY_STATE": {"INFO": "Start a conversation with this account.", "SEND_MESSAGE": "Send a message"}, "LOAD_MORE": "Load more", "CURRENT_USER": "You", "DELETED_USER": "Deleted User", "SHARED_A_FILE": "Shared a file"}, "HEADER": {"TITLE": "Inbox", "BETA": "BETA", "INBOX_AI_TITLE": "{{<PERSON><PERSON><PERSON>}}", "TOOLTIP": "{{assistant<PERSON><PERSON>}} - Your AI Assistant", "AI_ASSISTANT_INBOX_TITLE": "Cha<PERSON>"}, "TOPBAR": {"ASSIGNEE": "Assignee", "NONE": "None", "ERROR_LOADING_RECIPIENT": "Error loading recipients information"}, "COMPOSER_ALERTS": {"PARTICIPANT_INTERNAL_INFO_DELETED": {"TITLE": "Deleted contact", "DESCRIPTION": "The contact you are trying to reach has been deleted."}}, "SETTINGS": {"PAGE_TITLE": "Inbox settings", "CHAT_AVAILABILITY": "Chat availability", "DISPLAY_AVAILABILITY_MESSAGE": "Display availability message", "AVAILABILITY_DESCRIPTION": "All recipients in conversations with you will see your organization's availability message at the top of their message window", "AVAILABILITY_MESSAGE": "Availability message", "PREVIEW_TITLE": "Preview", "SAVE_SUCCESSFUL": "Setting<PERSON> saved successfully", "ERROR_SAVING_SETTINGS": "Error saving settings", "ERROR_LOADING_SETTINGS": "Error loading settings", "PREVIEW": {"TITLE": "Organization Name"}, "AUTOMATIONS": "Automations", "AUTOMATIONS_DESCRIPTION": "You can further enhance Inbox features by creating automated workflows.", "GO_TO_AUTOMATIONS": "Go to Automations", "LEARN_MORE": "Learn more", "EMPTY_STATE": {"TITLE": "Inbox settings coming soon", "DESCRIPTION": "We're working on ways to let you customize your inbox. Stay tuned."}, "ACTIVE": "Active", "INACTIVE": "Setup required", "DISCONNECT": "Disconnect", "AI_ASSISTANT": {"TITLE": "AI Assistant"}, "FACEBOOK_MESSENGER": {"TITLE": "Facebook Messenger", "DESCRIPTION": "Receive and respond to Facebook Messenger inquiries in your Inbox.", "DISCONNECTED_DESCRIPTION": "Connect a verified account to gain access to Facebook Messenger inquiries directly in your Inbox.", "FACEBOOK_PAGE_NAME": "Facebook Page", "CONNECT": "Connect Facebook", "SIGN_IN_TO_FACEBOOK": "Sign in with Facebook", "RECONNECT": "Reconnect", "TURN_ON_MESSAGING": "Turn on messaging", "DISCONNECT": "Disconnect", "DISCONNECT_TITLE": "Are you sure you want to disconnect Facebook Messenger?", "DISCONNECT_DESCRIPTION": "You will no longer be able to receive or respond to messages from Facebook Messenger in your Inbox.", "SUCCESS": {"SUCCESSFULLY_ENABLED": "Successfully enabled Facebook Messenger", "SUCCESSFULLY_DISABLED": "Successfully disabled Facebook Messenger"}, "WARNINGS": {"MULTIPLE_PAGES_LINKED": "Multiple Facebook Pages linked", "MULTIPLE_PAGE_FIRST_SELECTED": "The page you will setup for Facebook Messenger is <strong>{{ pageName }}</strong>.", "MULTIPLE_PAGES_LINKED_DESCRIPTION": "You have linked multiple Facebook pages to your account but may only use one with Inbox Pro."}, "ERRORS": {"MESSENGER_ALREADY_ENABLED_ON_OTHER_BUSINESS": "The business '{{ organizationName }}' is already receiving Messenger messages for this Facebook page. Either disable messaging on '{{ organizationName }}' or connect a different Facebook page here.", "MESSENGER_ALREADY_ENABLED_ON_OTHER_UNKNOWN_BUSINESS": "Another business is already receiving Messenger messages for this Facebook page. Either disable messaging on the other business or connect a different Facebook page here.", "FAILED_TO_ENABLE": "Failed to enable Facebook Messenger. Please contact support if the issue persists.", "FAILED_TO_DISABLE": "Failed to disable Facebook Messenger. Please contact support if the issue persists.", "UNKNOWN_ERROR": "An unknown error occurred.", "PERMISSIONS_ERROR": "A permissions error occurred.", "PERMISSIONS_ERROR_DESCRIPTION": "You do not have the necessary permissions to make changes to this connection. Please contact support for assistance.", "PLEASE_TRY_RECONNECTING": "Please try reconnecting.", "RECONNECT_TO_GRANT_PERMISSIONS": "Please reconnect to Facebook to grant additional messaging permissions for your Facebook page.", "CANNOT_SEND_MESSAGE": "Unable to reply with Facebook Messenger", "CANNOT_SEND_MESSAGE_DESCRIPTION": "An error occurred getting the status of this Facebook message."}}, "GOOGLE_BUSINESS_MESSAGES": {"ACTIONS": {"GO_TO_SETTINGS": "Go to settings"}, "STATUS": {"NOT_LAUNCHED": {"TITLE": "Business Messages is not enabled", "DESCRIPTION": "Google Business Messages has been disabled for this account. You can re-enable it by visiting the Inbox Settings page."}, "NOT_ENABLED": {"TITLE": "Business Messages is disabled", "DESCRIPTION": "Google Business Messages is disabled for this account. To enable it, please contact your account administrator."}, "EXPIRED_CONVERSATION": {"TITLE": "Conversation has expired", "DESCRIPTION": "Google Business Messages conversations expire after 30 days from the customer's last message, and responses can no longer be provided beyond that point."}, "UNKNOWN_ERROR": {"TITLE": "Unknown error", "DESCRIPTION": "An unknown error occurred. Please contact support, or try again later."}, "INVALID_TOKEN": {"TITLE": "Google Business Profile disconnected", "DESCRIPTION": "This conversation was initiated from your Google Business Profile, but your profile is no longer connected. To continue the conversation, please reconnect your profile."}}}, "INSTAGRAM_MESSAGES": {"TITLE": "Instagram Messages", "DESCRIPTION": "Receive and respond to Instagram Messages in your Inbox.", "DISCONNECTED_DESCRIPTION": "Connect through Facebook with a verified account to gain access.", "SIGN_IN_TO_INSTAGRAM": "Sign in with Instagram", "INSTAGRAM_PAGE_NAME": "Connected account", "CONNECT": "Connect Instagram", "RECONNECT": "Reconnect", "TURN_ON_MESSAGING": "Turn on messaging", "DISCONNECT": "Disconnect", "DISCONNECT_TITLE": "Are you sure you want to disconnect Instagram Messages?", "DISCONNECT_DESCRIPTION": "You will no longer be able to receive or respond to Instagram Messages in your Inbox.", "SUCCESS": {"SUCCESSFULLY_ENABLED": "Successfully enabled Instagram Messages", "SUCCESSFULLY_DISABLED": "Successfully disabled Instagram Messages"}, "WARNINGS": {"MULTIPLE_PAGES_LINKED": "Multiple Instagram Pages linked", "MULTIPLE_PAGE_FIRST_SELECTED": "The page you will setup for Instagram Messages is <strong>{{ pageName }}</strong>.", "MULTIPLE_PAGES_LINKED_DESCRIPTION": "You have linked multiple Instagram pages to your account but may only use one with Inbox Pro."}, "ERRORS": {"FAILED_TO_ENABLE": "Failed to enable Instagram Messages. Please contact support if the issue persists.", "FAILED_TO_DISABLE": "Failed to disable Instagram Messages. Please contact support if the issue persists.", "UNKNOWN_ERROR": "An unknown error occurred.", "PERMISSIONS_ERROR": "A permissions error occurred.", "PERMISSIONS_ERROR_DESCRIPTION": "You do not have the necessary permissions to make changes to this connection. Please contact support for assistance.", "PLEASE_TRY_RECONNECTING": "Please try reconnecting.", "RECONNECT_TO_GRANT_PERMISSIONS": "Please reconnect to Instagram to grant additional messaging permissions for your Instagram page.", "CANNOT_SEND_MESSAGE": "Unable to reply with Instagram Messages", "CANNOT_SEND_MESSAGE_DESCRIPTION": "This conversation has expired, or there is an issue with the Instagram connection."}}, "WHATSAPP_MESSAGES": {"TITLE": "WhatsApp Business Messages", "DESCRIPTION": "Receive and respond to WhatsApp Business messages in your Inbox.", "NOT_CONNECTED_DESCRIPTION": "Connect through Facebook with a verified account to gain access.", "CONNECTED_PHONE_NUMBER": "Connected phone number", "LOGIN_WITH_FACEBOOK": "Sign in with Facebook", "RECONNECT": "Reconnect", "DISCONNECT": "Disconnect", "DISCONNECT_TITLE": "Are you sure you want to disconnect your WhatsApp account?", "DISCONNECT_DESCRIPTION": "You will no longer be able to receive or respond to WhatsApp messages in your Inbox.", "SUCCESS": {"SIGNUP_SUCCESSFUL": "Successfully setup WhatsApp connection", "DISCONNECT_SUCCESSFUL": "Successfully removed WhatsApp connection"}, "ERRORS": {"SIGNUP_NOT_SUCCESSFUL": "Failed to setup WhatsApp connection. Please contact support if the issue persists.", "DISCONNECT_NOT_SUCCESSFUL": "Failed to remove WhatsApp connection. Please contact support if the issue persists.", "UNKNOWN_ERROR": "An unknown error occurred.", "PERMISSIONS_ERROR": "A permissions error occurred.", "PLEASE_TRY_RECONNECTING": "Please try reconnecting.", "CANNOT_SEND_MESSAGE_DESCRIPTION": "This conversation has expired, or there is an issue with the WhatsApp connection."}, "BADGE": {"CONNECTED": "Connected", "NOT_CONNECTED": "Setup required", "SETUP_STEPS_INCOMPLETE": "Setup required", "TOKEN_INVALID": "Invalid token", "UNKNOWN": "Unknown", "UNKNOWN_ERROR": "Unknown error"}}, "EMAIL": {"UNSUPPORTED_FOR_PARTNER_CENTER": {"TITLE": "Email is not available", "DESCRIPTION": "Sorry, you are unable to send messages to this contact, as email is not available."}}, "SMS_MESSAGES": {"COMPOSER_A2P_REGISTRATION_REQUIRED": {"TITLE": "SMS Registration Required", "DESCRIPTION": "SMS Registration Required – US telecom companies require businesses to register to send SMS messages. Register your business to continue this conversation via SMS.", "ACTION_TITLE": "View registration"}, "NOT_ENABLED": {"TITLE": "SMS messaging is not available", "DESCRIPTION": "Sorry, you are unable to send messages to this contact, as SMS messaging is not enabled. Please contact support for assistance."}, "NO_PHONE_NUMBER": {"TITLE": "SMS messaging is not available", "DESCRIPTION": "SMS phone number not available. Please contact support if the issue persists."}, "CARD": {"PHONE_AND_SMS": "Phone and SMS", "ASSIGNED_NUMBER_TITLE": "Assigned Number", "ASSIGNED_NUMBER_DESCRIPTION": "Your number is automatically assigned based on available options in your area.", "ENABLED_AI_RESPONSES": "Enable AI responses", "ENABLED_AI_RESPONSES_DESCRIPTION": "AI can respond to incoming SMS messages.", "AI_RESPONSES_UPDATED": "AI SMS responses setting updated successfully.", "AI_RESPONSES_UPDATE_ERROR": "Error updating AI SMS responses setting.", "SETTINGS": "Settings", "LEARN_MORE": "Learn more", "CONFIGURE": "Configure", "PHONE_NUMBER_CLAIM_SUCCESS": "Phone number claimed with success", "PHONE_NUMBER_CLAIM_ERROR": "Error claiming phone number", "CLAIM_PHONE_NUMBER": "Claim phone number"}}, "PHONE_NUMBER_SETTINGS": {"BUTTON_LABEL": "Settings", "TITLE": "Phone number settings", "INCOMING_CALL_HANDLE_TITLE": "When a call is received...", "INCOMING_SMS_HANDLE_TITLE": "When you receive an SMS...", "YOUR_NUMBER": "Your number:", "PHONE_CONFIG_SAVED": "Phone number config saved", "PHONE_CONFIG_ERROR": "Failed to save phone number config", "PARTNER_SMS_BETA_MESSAGE": "Unlimited SMS sending and receiving is currently included with your subscription during our open beta! Later, some messaging will be included as part of your subscription with the option to pay for more as needed.", "CALL_OPTIONS": {"NOTHING": "End call", "VOICE_AI": "Answer with AI", "FORWARD_CALL": "Forward the call", "USE_BROADLY": "Handle with Broadly 1.0", "SEND_FOLLOWUP_MESSAGE": "Send SMS message"}, "SMS_OPTIONS": {"NOTHING": "Do nothing", "AI_REPLY": "Reply with AI"}, "VOICE_AI": {"ENABLED_LABEL": "Answer with voice AI", "ENABLED_INFO": "Call forwarding and missed call messaging are not available while your voice receptionist is answering calls.", "DISABLED_INFO": "Your voice receptionist can be configured to answer calls. Note that using this feature will disable call forwarding and missed call messaging."}, "CALL_FORWARDING": {"ENABLED_LABEL": "Forward the call", "FORWARDING_NUMBER_LABEL": "Forward to", "FORWARDING_NUMBER_HINT": "Incoming calls to your platform number will be forwarded to this number", "FORWARDING_NUMBER_INVALID": "Phone number must contain international dialing code and area code", "FORWARD_TO_TOOLTIP": "Enter the number for your answering service, personal device, or phone tree. This is not your regular business number."}, "END_CALL_MESSAGE": {"PLAY_VOICE_MESSAGE": "Play a voice message before ending the call (text-to-speech audio)\n", "LABEL": "Voice message", "PLACEHOLDER": "Sorry we missed you call. We'll send you a text shortly"}, "MISSED_CALL_MESSAGING": {"ENABLED_LABEL": "Follow up with an SMS message", "ENABLED_LABEL_MISSED_CALL": "Follow up with an SMS message when the call is missed", "TRIGGER_LABELS": {"IMMEDIATE": "Immediately", "FORWARDED_CALL_MISSED": "If the forwarded call is missed"}, "IMMEDIATE_MESSAGE_TOOLTIP": "Recommended when forwarding to an answering service or voicemail", "FORWARDED_CALL_MISSED_TOOLTIP": "Recommended when forwarding to a personal device or phone tree", "FORWARDED_CALL_MISSED_HINT": "A call is considered missed if it disconnects or receives a busy tone. Calls answered by voicemail are considered 'answered' and won't trigger a text back.", "MESSAGE_CONTENT_LABEL": "Message to send", "MESSAGE_CONTENT_LABEL_WHEN_CALL_ENDS": "Message to send when call ends", "VOICE_AI_EXAMPLE_FOLLOW_UP_MESSAGE": "e.g, <PERSON>! Thanks for calling [business name]. You can text us here if you have any more questions", "CALL_FORWARDING_EXAMPLE_FOLLOW_UP_MESSAGE": "e.g, <PERSON>, this is [Your Business Name]. We missed your call. How can we help? Reply to this message and we'll get back to you soon. Reply STOP to opt out.", "SEND_SMS_FOLLOW_UP_MESSAGE": "e.g, <PERSON>, this is [Your Business Name]. We missed your call. How can we help? Reply to this message and we'll get back to you soon. Reply STOP to opt out."}}, "MANAGE_ON_WEB": "Manage on web", "MANAGE_ON_WEB_ERROR": "Failed to open the web page.", "SMS_REGISTRATION": "SMS Registration", "INBOX_SETTINGS": "Inbox Settings", "EMAIL_SETTINGS": {"TITLE": "Email", "DESCRIPTION": "Sender info preview", "SENDER_PREVIEW_INFO_DESCRIPTION": "The sender name is automatically set as your logged in name and business profile name.", "FORWARDING_LEARN_MORE_TITLE": "Assigned email", "FORWARDING_LEARN_MORE_DESCRIPTION": "Your email send address for email messages sent out of Inbox, can be configured in email settings.", "USER_AT_COMPANY": "{{firstName}} at {{companyName}}", "COMPANY_ONLY": "{{companyName}}", "CONFIGURE_EMAIL": "Configure email"}, "MESSAGE_TEMPLATES": {"TITLE": "Messaging templates", "DESCRIPTION": "Create text messaging templates to save time when responding to messages in Inbox.", "GO_TO_MESSAGE_TEMPLATES": "Customize templates", "EDIT_TEMPLATE": "Edit template", "DELETE_TEMPLATE": "Delete template", "TEMPLATE_NAME": "Name", "TEMPLATE_CONTENT": "Content", "CREATE_TEMPLATE": "Create template", "CREATE_TEMPLATE_SUCCESSFUL": "Template is created successfully", "CREATE_TEMPLATE_FAILED": "An error occurred while creating the template", "TEMPLATE_MISSING_FIELDS": "Some fields are required", "UPDATE_TEMPLATE_SUCCESSFUL": "Template is successfully updated", "UPDATE_TEMPLATE_FAILED": "An error occurred while updating the template", "LOAD_TEMPLATE_FAILED": "An error occurred while loading the template", "DELETE_TEMPLATE_MODAL": {"TITLE": "Delete template", "DESCRIPTION": "Are you sure you want to delete this template?"}}, "PLATFORM_ASSISTANT": {"SETTINGS_CARD": {"TITLE": "Platform Chat", "DESCRIPTION": "AI will respond to incoming Platform Chat messages using the knowledge you give it.", "ENABLE": "Enable AI assistant for Platform Chat", "GO_TO_CONFIGURATION": "Configure Platform Chat", "PLATFORM_CHAT_STATUS_UPDATED": "Platform Chat status updated successfully.", "PLATFORM_CHAT_STATUS_UPDATE_ERROR": "Error updating Platform Chat status."}, "SETTINGS_PAGE_TITLE": "Platform Chat configuration", "KNOWLEDGE_SOURCES": {"TITLE": "AI Assistant for Platform Chat", "SUBTITLE": "Custom Knowledge"}, "SUSCRIPTION_SELECTOR": {"TITLE": "Constrain to subscription level", "DESCRIPTION": "AI will respond only to messages from the subscription levels.", "PLACEHOLDER": "Select..."}}, "WEBCHAT_CONFIGURATION": "Web Chat configuration", "INBOX_PRO_CTA": {"TITLE": "Installation", "HEADING": "Upgrade to get AI Web Chat on your website", "VIEW_PRODUCT": "View product"}}, "VIEWS": {"ALL_CONVERSATIONS": "All conversations", "NOTIFICATIONS": "Notifications", "SETTINGS": "Settings", "TOGGLE_LABEL": "<PERSON><PERSON>", "FOLLOW": "Follow", "FOLLOWING": "Following", "FAILED_FOLLOW": "Failed change follow status.", "SUCCESS_FOLLOW": "You are now following this conversation.", "BANNER_INFO_HEADING": "Messaging views are here!", "BANNER_INFO_BODY": "Focus on the conversations that matter most to you by adding them to your ★ Following view."}, "DETAILS": {"TITLE": "Conversation details", "LOADING_ERROR": "Error loading conversation details"}, "TICKETS": {"TITLE": "Tickets", "VIEW_MORE": "View more", "VIEW_LESS": "View less", "CREATE": "Create ticket", "VIEW": "View ticket", "OPEN_TITLE": "Open tickets", "CLOSED_TITLE": "Closed tickets", "NO_TICKETS": "No {{status}} tickets", "NO_TICKETS_DESCRIPTION": "Congratulations you are all caught up", "LOAD_MORE": "Load more", "ERROR_TICKETS": "Error loading {{status}} tickets. Please refresh."}, "DATE": {"TODAY": "Today", "YESTERDAY": "Yesterday", "AT": "at", "MINS_AGO": "mins ago", "JUST_NOW": "Just now"}, "EVENTS": {"UNSUBSCRIBED": "A recipient unsubscribed from your messages", "SUBSCRIBED": "A recipient subscribed to your messages", "SENT_BY_YOU": "You", "LEAD_CAPTURED": "Form captured", "VIEW_ORIGINAL_EMAIL": "View original email", "VIEW_CAMPAIGN": "View campaign", "CAMPAIGN": {"CARD_TITLE": "Campaign {{channel}} delivered"}, "REVIEW_REQUEST": {"CARD_TITLE": "Ask for feedback"}, "PHONE": {"CARD_TITLE": "Phone Call", "CARD_SUBTITLE": "{{ from }} to {{ to }}", "MISSED_CALL": "Missed call", "TRANSCRIPT": "Call transcript", "VIEW_TRANSCRIPT": "View call transcript", "AUDIO_PLAYBACK_ERROR": "Audio playback is not supported", "VOICE_AI_SETTINGS_LABEL": "Voice AI settings for this call", "VOICE_LABEL": "Voice", "TURN_DETECTION_THRESHOLD_LABEL": "Turn detection threshold", "TURN_DETECTION_PREFIX_PADDING_LABEL": "Turn detection prefix padding", "TURN_DETECTION_SILENCE_DURATION_LABEL": "Turn detection silence duration"}, "SMS": {"UNSUBSCRIBED_SUBTITLE": "You can no longer send messages to this SMS number because the contact has replied with an unsubscribe word: STOP, STOPALL, UNSUBSCRIBE, CANCEL, END, or QUIT.", "SUBSCRIBED_SUBTITLE": "You can now send messages to this SMS number because the the contact has sent a resubscribe word: START, YES or UNSTOP."}}, "SELECTOR_DIALOG": {"SEARCH_FOR_COMPANY": "Search for a company", "ACCOUNTS": "Accounts", "SEARCH_FOR_CONTACT": "Search for a contact", "CONTACTS": "Contacts", "CHOOSE_ACCOUNT": "Choose account", "CHOOSE_CONTACT": "Choose contact", "CREATE_ACCOUNT": "Create account", "CREATE_CONTACT": "Create contact", "ACCOUNTS_NOT_FOUND": "No accounts found", "CONTACTS_NOT_FOUND": "No contacts found"}, "WARNING": {"TURN_ON_NOTIFICATIONS": "Turn on notifications ", "TURN_ON_NOTIFICATIONS_DETAILS": "— To be notified of new messages in this conversation, turn on \"Only my events\" in your ", "NOTIFICATION_SETTINGS": "Notification settings.", "NOTIFICATION_UPDATE_SUCCESS": "Notification settings updated successfully", "NOTIFICATION_UPDATE_ERROR": "Error updating notification settings", "A2P_BANNER_TITLE": "Action required immediately", "A2P_BANNER_DESCRIPTION": "SMS messages are being blocked by telecoms for unregistered businesses. Fill out your business details now to minimize downtime of message deliverability.", "A2P_BANNER_ACTION": "Update business info", "A2P_REJECTED_BANNER_DESCRIPTION": "- Your SMS registration was rejected. Please correct your application and resubmit.", "A2P_REJECTED_BANNER_ACTION": "View submission"}, "WEBCHAT": {"SETTINGS": {"INSTALLATION": "Installation", "WEBSITE_INSTALLATION": "Website", "WORD_PRESS_INSTALLATION": "WordPress", "INSTALL_INSTRUCTIONS": "Copy and paste this code into the head element of your website, typically just before the closing </head> tag.", "INSTALL_INSTRUCTIONS_READ_MORE": "Read detailed instructions", "GTM_TITLE": "Google Tag Manager", "GTM_DESCRIPTION": "You can use this code with Google Tag Manager to embed Web Chat on your site.", "WORD_PRESS_INSTALL_INSTRUCTIONS": "Integrate chat into your WordPress site with our easy to use plugin.", "WORD_PRESS_INSTALL_INSTRUCTIONS_DETAILS": "Upload and activate this plugin on your website at <strong>yoursite.com/wp-admin/ > Plugins > Add New Plugin</strong>", "WORD_PRESS_DOWNLOAD": "Download WordPress plugin", "SUCCESS_WORD_PRESS_DOWNLOAD": "Downloaded file saved to {{directory}}", "GO_DADDY_TITLE": "GoDaddy Website Builder", "GO_DADDY_DESCRIPTION": "When using the GoDaddy website builder, you may need to use a different installation script. This script will make sure the widget appears correctly on your GoDaddy website.", "TITLE": "Web Chat", "LOCAL_SEO_TITLE": "My Listing Chat Widget", "DESCRIPTION": "Capture new leads from your website with an AI-assisted chat widget.", "LEARN_MORE": "Learn more", "ENABLED_DESCRIPTION": "Once installed on your website, new leads will appear in your Inbox.", "GO_TO_MY_LISTING": "Try it", "SETUP_WEBCHAT": "Setup Web Chat", "SETUP_REQUIRED": "Setup required", "NOT_ACTIVE": "Not active", "SOMETHING_WENT_WRONG": "Something went wrong.", "WIDGET_NAME": "Widget name", "WIDGET_NAME_DESCRIPTION": "A unique name to identify this widget internally.", "WEBCHAT_WELCOME_MESSAGE": "Welcome greeting", "WEBCHAT_WELCOME_MESSAGE_DESCRIPTION": "This greeting appears on header of the chat widget, to encourage website visitors to engage.", "WEBCHAT_WELCOME_MESSAGE_PLACEHOLDER": "Hi, how can we help you today?", "WEBCHAT_CUSTOM_GREETING": "Initial AI message", "WEBCHAT_CUSTOM_GREETING_DESCRIPTION": "Your AI receptionist will send this initial message when someone interacts with the web chat for the first time.", "WEBCHAT_CUSTOM_GREETING_PLACEHOLDER": "Hi there! 👋 I'm here to assist with anything you need. How can I help you today?", "APPEARANCE": "Appearance", "PRIMARY_COLOR": "Primary color", "WEBCHAT_PREVIEW": "Web Chat preview", "TEXT_COLOR": "Primary text color", "ACCENT_COLOR": "Accent color (send button and chat bubbles)", "ACCENT_TEXT_COLOR": "Accent text color", "LIGHT": "Light", "DARK": "Dark", "POSITION": "Web Chat bottom corner position", "RIGHT": "Right", "LEFT": "Left", "INVALID_COLOR": "Invalid format of \"#rrggbb\", where rr, gg, bb are two-digit hexadecimal characters.", "DISABLED": "Disabled", "ENABLED": "Enabled", "FORM_INVALID": "Failed to update widget, please make sure all fields are correct.", "WEBCHAT_UPDATED": "Web Chat updated successfully.", "WEBCHAT_UPDATE_ERROR": "Error updating Web Chat.", "WEBCHAT_CREATED": "Web Chat created successfully.", "WEBCHAT_CREATE_ERROR": "Error creating Web Chat.", "WEBCHAT_VALIDATION_ERROR": "Please correct the invalid fields before saving.", "WEBCHAT_DISABLED": "Web Chat replies not available.", "WEBCHAT_ENABLED_SET": "Web Chat enabled.", "WEBCHAT_DISABLED_SET": "<PERSON> Chat disabled.", "WEBCHAT_ENABLED_FAIL": "Failed to enable Web Chat, please try again.", "WEBCHAT_DISABLED_FAIL": "Failed to disable <PERSON> Chat, please try again.", "WEBCHAT_DISABLED_MISSING_CONTACT_INFO": "You will be able to respond once the web visitor provides their contact information.", "WEBCHAT_DISABLED_NO_SMS_PHONE": "Respond to this lead at {{phone}}.", "WEBCHAT_DISABLED_NO_SMS_EMAIL": "Respond to this lead at {{email}}.", "WEBCHAT_DISABLED_NO_SMS_EMAIL_PHONE": "Respond to this lead at {{phone}} or {{email}}.", "WEBCHAT_ASSISTANT_TITLE": "AI assistant", "WEBCHAT_ASSISTANT_DETAILS": "Assistant details", "WEBCHAT_ASSISTANT_NAME_HINT": "The name that will appear for the AI assistant in the chat widget.", "WEBCHAT_ASSISTANT_NAME": "Name", "WEBCHAT_ASSISTANT_NAME_TOOLTIP": "Recommended: include a title after your AI assistant's name to clearly indicate to users that they are interacting with an AI and not a human employee. If not set, the default designation will be \"Chat Receptionist.\"", "WEBCHAT_ASSISTANT_PROFILE_PICTURE": "Profile picture", "WEBCHAT_PRIMARY_GOAL": "Primary goal", "WEBCHAT_ADDITIONAL_INSTRUCTIONS": "Additional instructions", "WEBCHAT_ADDITIONAL_INSTRUCTIONS_DESCRIPTION": "Add any additional instructions for your AI assistant to follow.", "WEBCHAT_ADDITIONAL_INSTRUCTIONS_LIMIT_EXCEEDED": "Too many characters", "WEBCHAT_ASSISTANT": {"DESCRIPTION": "The AI will reply to messages sent to this web chat widget. Customize it separately.", "CONFIGURE": "Configure"}, "ENABLE_LEAD_CAPTURE": {"DESCRIPTION": "Capture lead information", "INFORMATION": "The AI will prioritize getting a name and contact information, create a new contact, and let the user know someone on your team will follow up."}, "IMAGE_UPLOAD": {"REMOVE_IMAGE": "Remove image", "HINT": "Pick an assistant profile picture."}, "ENABLE_LEAD_CAPTURE_DIALOG": {"INFORMATION": "The AI has the mission of capturing a lead for your team to follow up and get a customer. It will ask for their name and phone number, or email address as a fallback.", "INFORMATION_2": "The moment a lead provides single piece of contact info – phone number or email, a new contact will be created in the CRM, along with their name, what URL the conversation was captured on, and any UTM fields present – which is great for attributing leads to certain ad campaigns.", "INFORMATION_3": "If you disable this option, the AI assistant will act more like a help agent and answer questions, but will not ask for contact information, nor tell the user anyone will follow up, and leads will not be captured in the CRM."}, "ADDITIONAL_INSTRUCTIONS_DIALOG": {"TITLE": "Additional instructions information", "INFORMATION_WARNING": "Warning:", "INFORMATION": "These instructions will affect the main instructions and conversational behavior of your AI assistant. Make sure to test your assistant thoroughly after making any changes, because this has the potential for unintended consequences for your AI assistant's behavior. Use plain text or markdown format.", "EXAMPLE_HEADING": "Example instructions that you can use:", "EXAMPLE_1": "Link sharing: Describe when the AI assistant should direct someone to a link, like if they want to book a meeting or create a support ticket.", "EXAMPLE_2": "Ask additional qualifying questions, like if the user is within service area, or more details about their project.", "EXAMPLE_3": "Adjust the assistant's tone and personality, to match your brand voice.", "EXAMPLE_4": "Localizations – for example, you could ask it to use British English instead of American English.", "EXAMPLE_5": "Adjust the conversation flow, like ask for both the phone number and email address, or ask for the email address first.", "EXAMPLE_6": "Ask for additional contact fields; these fields will be added to the contact if found within the conversation:", "FIELD_1": "First name", "FIELD_2": "Last name", "FIELD_3": "Phone number", "FIELD_4": "Email address", "FIELD_5": "Address", "FIELD_6": "City", "FIELD_7": "State/Province", "FIELD_8": "Zip/Postal code", "FIELD_9": "Country", "DO_NOT_WORK_HEADING": "Instructions that do not work yet:", "DO_NOT_WORK_DESCRIPTION": "You cannot make the AI assistant perform actions outside of the conversation. For example, you cannot say \"If a customer asks for [employee], then notify that user.\""}, "BUSINESS_PROFILE": {"BUSINESS_PROFILE": "Business Profile", "ALLOW_ACCESS": "Allow access to your business profile", "DESCRIPTION": "Answer questions about your business contact information, hours, products and services you offer.", "MANAGE_BUSINESS_PROFILE": "Manage business profile", "LEARN_MORE": {"TITLE": "Learn more", "WHAT_INFORMATION_QUESTION": "What information is made available to your AI assistant?", "WHAT_INFORMATION": "These fields are shared from your business profile:", "BUSINESS_NAME": "Business name", "ADDRESS_SERVICE_AREAS": "Address and/or service areas", "WEBSITE": "Website", "PHONE_NUMBER": "Phone number", "CATEGORIES": "Categories", "HOURS": "Hours", "SERVICES_OFFERED": "Services offered", "SHORT_DESCRIPTION": "Short description", "LONG_DESCRIPTION": "Long description", "SOCIAL_MEDIA_URLS": "Social media URLs", "BOOKING_URL": "Booking URL"}}, "CUSTOM_KNOWLEDGE": {"AI_KNOWLEDGE_BASE": "AI knowledge base", "MANAGE_KNOWLEDGE": "Manage knowledge", "MANAGE_KNOWLEDGE_DESCRIPTION": "Create and manage facts and information that can be added to AI assistants", "REFRESH_OPTIONS": "Refresh options", "CHOOSE_KNOWLEDGE_SOURCES": "Answer questions using provided knowledge:", "KNOWLEDGE_SOURCES": "Knowledge sources", "ADD_KNOWLEDGE": "Add knowledge", "SELECT_KNOWLEDGE": "Select knowledge", "CUSTOM_KNOWLEDGE": "Q&A", "DESCRIPTION": "Provide additional facts and answers to frequently asked questions your Chat Receptionist can use when responding to inquiries.", "MAX_CHARACTER_EXCEEDED": "Maximum character limit exceeded", "APPLICATION_SETTINGS": "Settings", "APPS_WARNING": "This knowledge is currently being used by the following:", "NO_APPS": "This knowledge is not currently being used.", "EDIT": {"TITLE": "Custom knowledge", "NAME": "Name", "CONTENT": "Content"}, "DELETE": {"TITLE": "Delete custom knowledge?"}}, "MOBILE_CTA_TITLE": "Mobile Call-to-Action Popup", "MOBILE_CTA_DESCRIPTION": "Display the message popup on mobile screens of new website visitors. When unchecked, only the circular chat button will appear."}, "WIDGET": {"POWERED_BY": "Chat powered by", "WE_RESPOND_IMMEDIATELY": "We respond immediately", "SEND": "Send", "SENDING_DISABLED_FOR_PREVIEW": "Sending disabled for preview", "DEFAULT_ASSISTANT_NAME": "<PERSON>t Receptionist", "TOGGLE_WIDGET": "Toggle web chat widget", "CLOSE_WIDGET": "Close web chat widget"}, "LEARN_MORE": {"TITLE": "Learn more", "WHAT_CAN_DO_QUESTION": "What can an AI-assisted web chat widget do for my business?", "WHAT_CAN_DO_ANSWER": "Installing an AI-assisted web chat widget on your business website can help you capture more leads at any time of day, even when you are not available to respond immediately. Your AI assistant will instantly respond to customers on your behalf, with the goals of answering questions about your business and obtaining their name and contact information so you can follow up at a time that suits you.", "HOW_IT_WORKS_QUESTION": "How does my AI Agent work?", "HOW_IT_WORKS_ANSWER": "Your AI assistant will act as a first responder to any website leads, answering questions and providing a professional and courteous first impression for your business. The AI will attempt to get the lead's contact info, requesting the website visitor's name, mobile number, or email. Your AI assistant can answer questions about your business using knowledge it's been granted access to from your business profile, custom text snippets, and your website. The AI has been given strict instructions to only answer about things it's confident about and will not step outside of these bounds. You can monitor all conversations in real-time via your Inbox, providing complete transparency of the historical context prior to outreach. When you respond, you can do so via email or SMS (where available) directly from your Inbox, allowing you to continue the conversation off the website and onto their phone.", "WHAT_HAPPENS_QUESTION": "What happens when a new lead is captured?", "WHAT_HAPPENS_ANSWER": "When a new lead's contact information is captured by the AI assistant, a new contact will be created in your CRM, and you'll be notified – all users on your account who are subscribed to the \"new lead\" notification can get an app or email notification. You can also take advantage of automations and integrations to further streamline your lead nurturing flows.", "HOW_TO_GET_MOST_QUESTION": "How do I get the most from my AI-assisted Chat Widget?", "HOW_TO_GET_MOST_ANSWER": "The more website visitors you have, the more opportunity you have to capture a new lead. By improving your website's search engine optimization (SEO) and driving traffic to your website through paid advertising, you'll increase the effectiveness of your chat widget, which can result in more leads! Make sure someone from your team is reaching out to new leads as quickly as possible – every moment that passes without a response from your team is a moment that the lead could choose to go to another business. Don't lose the lead – respond quickly! Consider using an automation to immediately send the lead a message via email or SMS.", "HOW_TEST_IT_QUESTION": "How can I test it?", "HOW_TEST_IT_ANSWER": "You may have access to a demo \"My Listing\" chat widget, which is automatically created and trained on your business information – try adding additional knowledge and seeing how your AI assistant answers your questions. You can also install the chat widget on a private or low-traffic web page and try chatting with it there before going live on your homepage. You'll see it's an exciting way to drive new business for your company, 24/7."}}, "FILTERS": {"INBOX": "Inbox", "ANONYMOUS": "Anonymous web visitors", "CLOSED": "Closed", "PRIVATE": "Private"}, "RESPONSE_EVALUATION": {"HELPFUL": "Helpful", "HELPFUL_QUESTION": "What about this response was helpful?", "NOT_HELPFUL": "Not helpful", "NOT_HELPFUL_QUESTION": "What about this response was not helpful?"}, "INBOX_ZERO": {"TITLE": "Great work!", "DESCRIPTION": "You have closed all your conversations.", "START_A_NEW_CONVERSATION": "Start a new conversation"}, "MULTI_LOCATION": {"MULTI_LOCATION_SUBTITLE": "Chat with customers across all locations in this group.", "MULTI_LOCATION_BUTTON": "Start using multi-location inbox", "SUCCESS_TO_OPT_IN": "You are now able to use Inbox for this group. Please wait while conversations are loaded.", "FAIL_TO_OPT_IN": "Opting in failed", "INBOX_INFO": "Interact with all conversations from all accounts in this group. Note, contacts and notifications are managed at single-location."}, "CITATIONS": {"INTERNAL_ONLY": "Explanations and sources are only visible to users at your organization.", "SOURCE_MATERIAL": "Explanation", "FUNCTION": {"REQUEST_PARAMETERS": "Request parameters", "REQUEST_URL": "Request URL", "RESPONSE": "Response", "RESPONSE_TOO_LARGE": "Response too large to display. Large responses (over {{ maxKilobytes }}KB) may cause issues with AI. Consider using filters or pagination on the tool call to reduce the size.", "HEADERS": "Headers", "COPY_AS_CURL": "<PERSON><PERSON> as c<PERSON><PERSON>"}}, "WEBSITE_PREVIEW": {"PREVIEW_TITLE": "Website Preview", "NO_FILE_ERROR": "No file URL provided for preview", "SCRAPED_CONTENT_WARNING": "This is a preview of text content that was previously captured from the website. The layout and styling may look very different from the actual site, and images or links may not work. We only process the text content for our AI, not the visual design or images.", "VIEW_LIVE_SITE": "View live site ↗", "UNSUPPORTED_TYPE_ERROR": "This citation type is not supported for preview. Only website citations can be previewed."}, "CONTACT_ASSISTANT": {"AI_HELP_MESSAGE": "Need immediate assistance? I'm here to help.", "QUESTION_PLACEHOLDER": "Ask a question..."}}}