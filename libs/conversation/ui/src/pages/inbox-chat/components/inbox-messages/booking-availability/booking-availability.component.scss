@use 'design-tokens' as *;

$chat-bubble-border-radius: 16px;

.availability-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: $spacing-1;
  button {
    text-align: start;
  }

  &.align-self-end {
    align-items: flex-end;
    button {
      text-align: end;
    }
  }
}

.availability-wrapper > button {
  border-radius: $chat-bubble-border-radius;
  padding: $spacing-2 $spacing-3;
  color: $primary-text-color;
  font-size: $font-preset-4-size;
  background-color: $info-background-color;
  margin: $spacing-1 0;
  border: 1px solid rgba(0, 0, 0, 0.04);
  font-weight: 500;
  width: fit-content;
}
