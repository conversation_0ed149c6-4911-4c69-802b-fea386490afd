import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BookingAvailabilityComponent } from './booking-availability.component';
import {
  ConversationMessage,
  BookingAvailabilityOption,
} from '../../../../../../../core/src/lib/interface/conversation.interface';
import { MessageType, ConversationChannel } from '@vendasta/conversation';
import { BOOKING_AVAILABILITY_KEY } from '../../../../../../../core/src/lib/conversation.constants';
import { SimpleChange, InputSignal, signal } from '@angular/core';

function createMockInputSignal<T>(value: T): InputSignal<T> {
  const mockSignal = signal(value);
  return mockSignal as unknown as InputSignal<T>;
}

describe('BookingAvailabilityComponent', () => {
  let component: BookingAvailabilityComponent;
  let fixture: ComponentFixture<BookingAvailabilityComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BookingAvailabilityComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(BookingAvailabilityComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    const mockMessage: ConversationMessage = {
      messageId: 'test-message',
      body: 'Test message',
      type: MessageType.MESSAGE_TYPE_MESSAGE,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [],
    };

    component.inputMessage = createMockInputSignal(mockMessage);
    expect(component).toBeTruthy();
  });

  it('should parse booking availability options from message metadata', async () => {
    const mockBookingOptions: BookingAvailabilityOption[] = [
      {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      },
      {
        displayText: 'Book for 3:00 PM',
        scheduleId: 'schedule-2',
      },
    ];

    const mockMessage: ConversationMessage = {
      messageId: 'booking-message',
      body: 'Available booking times:',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: JSON.stringify(mockBookingOptions),
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    expect(component.bookingAvailabilities).toEqual(mockBookingOptions);
  });

  it('should render booking availability buttons when options are available', async () => {
    const mockBookingOptions: BookingAvailabilityOption[] = [
      {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      },
      {
        displayText: 'Book for 3:00 PM',
        scheduleId: 'schedule-2',
      },
    ];

    const mockMessage: ConversationMessage = {
      messageId: 'booking-message',
      body: 'Available booking times:',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: JSON.stringify(mockBookingOptions),
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    fixture.detectChanges();

    const availabilityWrapper = fixture.nativeElement.querySelector('.availability-wrapper');
    expect(availabilityWrapper).toBeTruthy();

    const buttons = fixture.nativeElement.querySelectorAll('button[mat-flat-button]');
    expect(buttons.length).toBe(2);
    expect(buttons[0].textContent.trim()).toBe('Book for 2:00 PM');
    expect(buttons[1].textContent.trim()).toBe('Book for 3:00 PM');
  });

  it('should emit preferredBooking event when a booking option is clicked', async () => {
    const mockBookingOptions: BookingAvailabilityOption[] = [
      {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      },
    ];

    const mockMessage: ConversationMessage = {
      messageId: 'booking-message',
      body: 'Available booking times:',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: JSON.stringify(mockBookingOptions),
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    fixture.detectChanges();

    jest.spyOn(component.preferredBooking, 'emit');

    const button = fixture.nativeElement.querySelector('button[mat-flat-button]');
    button.click();

    expect(component.preferredBooking.emit).toHaveBeenCalledWith(mockBookingOptions[0]);
  });

  it('should not render anything when no booking options are available', async () => {
    const mockMessage: ConversationMessage = {
      messageId: 'regular-message',
      body: 'Regular message without booking data',
      type: MessageType.MESSAGE_TYPE_MESSAGE,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    fixture.detectChanges();

    const availabilityWrapper = fixture.nativeElement.querySelector('.availability-wrapper');
    expect(availabilityWrapper).toBeFalsy();
  });

  it('should handle empty booking availability metadata', async () => {
    const mockMessage: ConversationMessage = {
      messageId: 'empty-booking-message',
      body: 'Message with empty booking data',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: '',
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    expect(component.bookingAvailabilities).toEqual([]);

    fixture.detectChanges();

    const availabilityWrapper = fixture.nativeElement.querySelector('.availability-wrapper');
    expect(availabilityWrapper).toBeFalsy();
  });

  it('should apply correct CSS class based on message status type', async () => {
    const mockBookingOptions: BookingAvailabilityOption[] = [
      {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      },
    ];

    const mockMessage: ConversationMessage = {
      messageId: 'booking-message',
      body: 'Available booking times:',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: JSON.stringify(mockBookingOptions),
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    fixture.detectChanges();

    const availabilityWrapper = fixture.nativeElement.querySelector('.availability-wrapper');
    expect(availabilityWrapper).toBeTruthy();

    expect(availabilityWrapper.classList.contains('align-self-end')).toBeFalsy();
  });

  it('should work correctly when hostAppInterface is missing (optional injection)', async () => {
    const mockBookingOptions: BookingAvailabilityOption[] = [
      {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      },
    ];

    const mockMessage: ConversationMessage = {
      messageId: 'booking-message',
      body: 'Available booking times:',
      type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created: new Date(),
      metadata: [
        {
          key: BOOKING_AVAILABILITY_KEY,
          value: JSON.stringify(mockBookingOptions),
        },
      ],
    };

    component.inputMessage = createMockInputSignal(mockMessage);

    await component.ngOnChanges({
      inputMessage: new SimpleChange(null, mockMessage, true),
    });

    expect(() => fixture.detectChanges()).not.toThrow();

    const availabilityWrapper = fixture.nativeElement.querySelector('.availability-wrapper');
    expect(availabilityWrapper).toBeTruthy();

    const buttons = fixture.nativeElement.querySelectorAll('button[mat-flat-button]');
    expect(buttons.length).toBe(1);
    expect(buttons[0].textContent.trim()).toBe('Book for 2:00 PM');
  });
});
