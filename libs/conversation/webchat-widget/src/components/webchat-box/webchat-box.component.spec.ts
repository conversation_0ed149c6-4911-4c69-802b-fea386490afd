import { InputSignal, InputSignalWithTransform } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { MarkdownService } from 'ngx-markdown';
import { WebchatBoxComponent } from './webchat-box.component';
import {
  ConversationMessage,
  BookingAvailabilityOption,
} from '../../../../core/src/lib/interface/conversation.interface';
import { MessageGroup } from '../../models/message-group';
import { MessageType, ConversationChannel } from '@vendasta/conversation';
import { BOOKING_AVAILABILITY_KEY } from '../../../../core/src/lib/conversation.constants';

describe('WebchatBoxComponent', () => {
  let component: WebchatBoxComponent;
  let fixture: ComponentFixture<WebchatBoxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        WebchatBoxComponent,
        HttpClientTestingModule,
        TranslateTestingModule.withTranslations({
          en: {
            INBOX: {
              WEBCHAT: {
                SETTINGS: {
                  WEBCHAT_GREETING_PLACEHOLDER: 'Hi, how can we help you today?',
                },
              },
            },
          },
        }),
      ],
      providers: [
        {
          provide: MarkdownService,
          useValue: {
            parse: jest.fn().mockReturnValue(''),
            compile: jest.fn().mockReturnValue(''),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(WebchatBoxComponent);
    component = fixture.componentInstance;
    component.currentParticipantId = ((): string | null => 'participantId') as InputSignal<string | null>;
    component.messageGroups = ((): MessageGroup[] => []) as InputSignal<MessageGroup[]>;
    component.messages = ((): ConversationMessage[] => []) as InputSignal<ConversationMessage[]>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('replying should start as false', () => {
    expect(component.isReplying()).toBe(false);
  });

  it('should have the default welcome message if none is provided', () => {
    const message = fixture.nativeElement.querySelector('.chat-box-title');
    expect(message.textContent.trim()).toBe('INBOX.WEBCHAT.SETTINGS.WEBCHAT_WELCOME_MESSAGE_PLACEHOLDER');
  });

  it('should use custom welcome message if provided', () => {
    component.welcomeMessage = ((): string => 'Custom welcome message') as InputSignal<string>;
    fixture.detectChanges();
    const message = fixture.nativeElement.querySelector('.chat-box-title');
    expect(message.textContent.trim()).toBe('Custom welcome message');
  });

  it('should have the default assistant name if none is provided', () => {
    fixture.detectChanges();
    expect(component.assistantName()).toBe('Chat Receptionist');
  });

  it('should use custom assistant name if provided', () => {
    component.assistantName = ((): string | undefined => 'Custom assistant name') as InputSignalWithTransform<
      string,
      string | undefined
    >;
    fixture.detectChanges();
    expect(component.assistantName()).toBe('Custom assistant name');
  });

  describe('Booking Availability', () => {
    it('should render component handling booking availability messages', () => {
      const mockBookingOptions: BookingAvailabilityOption[] = [
        {
          displayText: 'Book for 2:00 PM',
          scheduleId: 'schedule-1',
        },
        {
          displayText: 'Book for 3:00 PM',
          scheduleId: 'schedule-2',
        },
      ];

      const mockBookingAvailabilityMessage: ConversationMessage = {
        messageId: 'booking-msg-1',
        body: 'Available booking times:',
        type: MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY,
        channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
        created: new Date('2024-01-01T12:00:00Z'),
        metadata: [
          {
            key: BOOKING_AVAILABILITY_KEY,
            value: JSON.stringify(mockBookingOptions),
          },
        ],
      };

      component.currentParticipantId = ((): string | null => 'participant-1') as InputSignal<string | null>;
      const messageGroup = new MessageGroup('booking-msg-1', 'participant-1', new Date('2024-01-01T12:00:00Z'));
      messageGroup.addMessage(mockBookingAvailabilityMessage);
      component.messageGroups = ((): MessageGroup[] => [messageGroup]) as InputSignal<MessageGroup[]>;
      component.messages = ((): ConversationMessage[] => [mockBookingAvailabilityMessage]) as InputSignal<
        ConversationMessage[]
      >;

      fixture.detectChanges();

      expect(component).toBeTruthy();

      expect(component.messages()).toContain(mockBookingAvailabilityMessage);
      expect(component.messageGroups()).toHaveLength(1);
      expect(component.messageGroups()[0].messages).toContain(mockBookingAvailabilityMessage);
    });

    it('should emit setPreferredBooking event when booking is selected', async () => {
      const mockBookingOption: BookingAvailabilityOption = {
        displayText: 'Book for 2:00 PM',
        scheduleId: 'schedule-1',
      };

      jest.spyOn(component.setPreferredBooking, 'emit');

      await component.handleSetPreferredBooking(mockBookingOption);

      expect(component.setPreferredBooking.emit).toHaveBeenCalledWith(mockBookingOption);
    });
  });
});
