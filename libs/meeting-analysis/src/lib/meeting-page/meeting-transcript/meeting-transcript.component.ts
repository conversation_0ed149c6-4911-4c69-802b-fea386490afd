import { Component, computed, ElementRef, input, output, Signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Content, Transcript, TranscriptComponent } from '@vendasta/galaxy/transcript';
import { TranslateModule } from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { TranscriptEntry, TranscriptService } from '@vendasta/meeting-analysis';
import { HttpClient } from '@angular/common/http';
import { take } from 'rxjs/operators';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatIconButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'meeting-analysis-meeting-transcript',
  styleUrls: ['./meeting-transcript.component.scss'],
  templateUrl: './meeting-transcript.component.html',
  imports: [CommonModule, TranscriptComponent, TranslateModule, MatDividerModule, MatIconButton, MatIconModule],
  standalone: true,
})
export class MeetingTranscriptComponent {
  @ViewChild('downloadAnchor', { static: true })
  private downloadAnchor: ElementRef;

  transcriptEntries = input.required<TranscriptEntry[]>();
  conversationId = input.required<string>();
  transcript: Signal<Transcript> = computed(() => {
    const entries = this.transcriptEntries();
    return {
      entries: entries.map((entry) => ({
        user: {
          displayName: entry.displayName,
        },
        content: entry.words.map((words) => ({
          startTime: words.startTime || 0,
          text: words.text,
        })),
      })),
    };
  });

  readonly timeStampSelected = output<number>();

  constructor(
    private readonly transcriptService: TranscriptService,
    private http: HttpClient,
    private readonly snackbarService: SnackbarService,
  ) {}

  clickedContent(event: Content): void {
    // Jump slightly before what was selected so it's a more natural start
    if (event.startTime > 0.5) {
      event.startTime = event.startTime - 0.5;
    }
    this.timeStampSelected.emit(event.startTime);
  }

  downloadTranscript() {
    this.http
      .get(this.transcriptService.getTranscriptUrl(this.conversationId()), {
        responseType: 'blob',
        withCredentials: true,
      })
      .pipe(take(1))
      .subscribe({
        next: (result) => {
          this.snackbarService.openSuccessSnack('TRANSCRIPT.DOWNLOAD_WILL_START_SHORTLY');
          this.downloadFile(result, 'transcript.txt');
        },
        error: (error) => {
          console.error(error);
          this.snackbarService.openErrorSnack('TRANSCRIPT.TRY_AGAIN');
        },
      });
  }

  downloadFile(file: Blob, filename: string): void {
    // Based off this article: https://www.illucit.com/en/angular/angular-5-httpclient-file-download-with-authentication/
    const url = window.URL.createObjectURL(
      new Blob([file], {
        type: 'text/plain',
      }),
    );
    const link = this.downloadAnchor.nativeElement;
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
