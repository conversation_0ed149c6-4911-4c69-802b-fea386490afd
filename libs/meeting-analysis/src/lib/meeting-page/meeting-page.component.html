<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>{{ conversation()?.title || '' }}</glxy-page-title>
  </glxy-page-toolbar>
  <glxy-page-wrapper [widthPreset]="'full'">
    <glxy-three-panel
      [leftPanelConfig]="{
        title: 'COMMON.DETAILS' | translate,
        noDefaultPadding: true,
        noBorder: true,
        desktopStyleOverrides: { 'max-width': '410px' },
        template: leftPanel,
      }"
      [centerPanelConfig]="{
        title: 'COMMON.SUMMARY' | translate,
        noBorder: true,
        template: centerPanel,
      }"
      [rightPanelConfig]="{
        title: 'COMMON.TRANSCRIPT' | translate,
        noDefaultPadding: true,
        noBorder: true,
        desktopStyleOverrides: { 'max-width': '410px' },
        template: rightPanel,
      }"
      [showRightPanel]="true"
      [fullPanelConfig]="{ noMobilePadding: true }"
      [heightOffset]="'100px'"
    >
      <ng-template #leftPanel>
        <div class="meeting-details-container">
          <meeting-analysis-meeting-details [conversation]="conversation()"></meeting-analysis-meeting-details>
        </div>
      </ng-template>

      <ng-template #centerPanel>
        <ng-container *ngIf="recordingURL$ | async as recordingUrl; else loadingRecording">
          <video class="recording" controls #videoPlayer>
            <source [src]="recordingUrl" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </ng-container>
        <ng-template #loadingRecording>
          <div class="recording stencil-shimmer"></div>
        </ng-template>
        <mat-card>
          <mat-card-content>
            <meeting-analysis-meeting-summary
              [summary]="conversation()?.summary || ''"
              [keyTakeaways]="conversation()?.keyTakeaways || []"
              [actionItems]="conversation()?.actionItems || []"
            >
            </meeting-analysis-meeting-summary>
          </mat-card-content>
        </mat-card>
      </ng-template>

      <ng-template #rightPanel>
        <div class="transcript-container">
          @if (conversation()) {
            @if (conversation()?.transcriptEntries?.length > 0) {
              <meeting-analysis-meeting-transcript
                [transcriptEntries]="conversation()?.transcriptEntries || []"
                (timeStampSelected)="onTimeStampSelected($event)"
                [conversationId]="conversation()?.id || ''"
              ></meeting-analysis-meeting-transcript>
            } @else {
              <div class="no-transcript">
                <glxy-empty-state>
                  <glxy-empty-state-hero>
                    <mat-icon class="larger-icon">text_snippet</mat-icon>
                  </glxy-empty-state-hero>
                  <glxy-empty-state-title
                    >{{ 'TRANSCRIPT.TRANSCRIPT_NOT_AVAILABLE_TITLE' | translate }}
                  </glxy-empty-state-title>
                  <p>{{ 'TRANSCRIPT.TRANSCRIPT_NOT_AVAILABLE_DESCRIPTION' | translate }}</p>
                </glxy-empty-state>
              </div>
            }
          }
        </div>
      </ng-template>
    </glxy-three-panel>
  </glxy-page-wrapper>
</glxy-page>
