import { Component, ElementRef, Signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { LexiconModule } from '@galaxy/lexicon';
import { ActivatedRoute } from '@angular/router';
import { catchError, Observable, switchMap } from 'rxjs';
import { map } from 'rxjs/operators';
import { Conversation, MeetingAnalysisApiService } from '@vendasta/meeting-analysis';
import { toSignal } from '@angular/core/rxjs-interop';
import { GalaxyThreePanelModule } from '@vendasta/galaxy/galaxy-three-panel';
import { MatTabsModule } from '@angular/material/tabs';
import { MeetingSummaryComponent } from '@galaxy/meeting-analysis/static';
import { MatCardModule } from '@angular/material/card';
import { MeetingDetailsComponent } from './meeting-details/meeting-details.component';
import { MeetingTranscriptComponent } from './meeting-transcript/meeting-transcript.component';
import { MatChipsModule } from '@angular/material/chips';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIcon } from '@angular/material/icon';
import { MatButton } from '@angular/material/button';

@Component({
  imports: [
    CommonModule,
    GalaxyPageModule,
    LexiconModule,
    GalaxyThreePanelModule,
    MatTabsModule,
    MeetingSummaryComponent,
    MatCardModule,
    MeetingDetailsComponent,
    MeetingTranscriptComponent,
    MatChipsModule,
    GalaxyLoadingSpinnerModule,
    GalaxyEmptyStateModule,
    MatIcon,
    MatButton,
  ],
  templateUrl: './meeting-page.component.html',
  styleUrl: './meeting-page.component.scss',
  standalone: true,
})
export class MeetingPageComponent {
  @ViewChild('videoPlayer') videoPlayer: ElementRef<HTMLMediaElement | null> | undefined;
  private conversationId$: Observable<string | null> = this.activatedRoute.paramMap.pipe(
    map((params) => params.get('conversationId')),
  );

  private conversation$: Observable<Conversation | null> = this.conversationId$.pipe(
    switchMap((conversationId) => this.meetingAnalysisApiService.getConversation({ id: conversationId })),
    map((response) => response?.conversation),
  );

  conversation: Signal<Conversation | null | undefined> = toSignal<Conversation | null>(this.conversation$);

  recordingURL$ = this.conversationId$.pipe(
    switchMap((conversationId) => this.meetingAnalysisApiService.getRecordingUrl({ conversationId: conversationId })),
    map((response) => response?.recordingUrl),
    catchError(() => {
      this.snackbarService.openErrorSnack('MEETING_DETAILS.RECORDING.LOADING_ERROR');
      return '';
    }),
  );

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly meetingAnalysisApiService: MeetingAnalysisApiService,
    private readonly snackbarService: SnackbarService,
  ) {}

  onTimeStampSelected(seconds: number): void {
    if (this.videoPlayer?.nativeElement) {
      this.videoPlayer.nativeElement.currentTime = seconds;
      this.videoPlayer.nativeElement.play();
    }
  }
}
