<form [formGroup]="formGroup" (ngSubmit)="submit()" class="full-height">
  <ng-container *ngIf="subscription$ | async as subscription">
    <ng-container *ngIf="currencyCode$ | async as currencyCode">
      <div class="subscription-details-container full-height">
        <div class="title-section">
          <div>
            <div class="title">
              <span class="title-text">{{ subscription.productName }}</span>
            </div>
            <div class="subtitle">{{ 'BILLING_SUBSCRIPTIONS.RETAIL_SUBSCRIPTION' | translate }}</div>
          </div>
          <span class="content-spacer"></span>
          <ng-container *ngIf="subscription | subscriptionStatusBadgePipe as status">
            <span class="status-badge">
              <glxy-badge [color]="status?.color!">{{ status?.text! | translate: status.parameters }}</glxy-badge>
            </span>
          </ng-container>
          <button type="button" mat-icon-button [matMenuTriggerFor]="settingsMenu">
            <mat-icon>settings</mat-icon>
            <mat-menu #settingsMenu="matMenu">
              <button
                mat-menu-item
                (click)="updateRenewalDate()"
                *ngIf="!subscription.expiry && subscription.nextInvoiceDate > today"
              >
                <glxy-button-loading-indicator [isLoading]="isUpdatingRenewalDate$ | async">
                  {{ 'BILLING_SUBSCRIPTIONS.EDIT_SUBSCRIPTION_INVOICE_DATE' | translate }}
                </glxy-button-loading-indicator>
              </button>
              @if (canAccessBillingServicePeriods$ | async) {
                <button mat-menu-item (click)="updateServicePeriod()" *ngIf="!subscription.expiry">
                  <glxy-button-loading-indicator [isLoading]="isUpdatingServicePeriod$ | async">
                    {{ 'BILLING_SUBSCRIPTIONS.EDIT_SUBSCRIPTION_SERVICE_PERIOD' | translate }}
                  </glxy-button-loading-indicator>
                </button>
              }
              <button mat-menu-item (click)="cancel()" *ngIf="!subscription.expiry">
                <glxy-button-loading-indicator [isLoading]="isCanceling$ | async">
                  {{ 'BILLING_SUBSCRIPTIONS.CANCEL_SUBSCRIPTION' | translate }}
                </glxy-button-loading-indicator>
              </button>
              <button mat-menu-item (click)="uncancel()" *ngIf="subscription.expiry && subscription.expiry >= today">
                <glxy-button-loading-indicator [isLoading]="isCanceling$ | async">
                  {{ 'BILLING_SUBSCRIPTIONS.UNCANCEL_SUBSCRIPTION' | translate }}
                </glxy-button-loading-indicator>
              </button>
              <button mat-menu-item (click)="uncancel()" *ngIf="subscription.expiry && subscription.expiry < today">
                <glxy-button-loading-indicator [isLoading]="isCanceling$ | async">
                  {{ 'BILLING_SUBSCRIPTIONS.REACTIVATE_SUBSCRIPTION' | translate }}
                </glxy-button-loading-indicator>
              </button>
            </mat-menu>
          </button>
          <button type="button" mat-icon-button (click)="close()">
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <mat-divider></mat-divider>
        <div class="quick-info">
          <div>
            <span class="key-container">{{ 'BILLING_SUBSCRIPTIONS.START_DATE' | translate }}</span>
            <span class="key-value-container">
              @if (subscription.billingStart) {
                {{ subscription.billingStart | toLocalDatePipe | date: 'mediumDate' }}
              }
            </span>
          </div>
          <div>
            @if (canAccessBillingServicePeriods$ | async) {
              <span class="key-container">{{ 'BILLING_SUBSCRIPTIONS.NEXT_INVOICE' | translate }}</span>
              <span class="key-value-container">
                @if (subscription.nextInvoiceDate) {
                  {{ subscription.nextInvoiceDate | toLocalDatePipe | date: 'mediumDate' }}
                }
              </span>
            }
          </div>
          <div>
            <span class="key-container">{{
              'ACCOUNT_BILLING_SETTINGS.ACTIVE_PRODUCTS.TABLE.HEADER.BILLING_END' | translate
            }}</span>
            @if (subscription.expiry) {
              <span class="key-value-container">{{ subscription.expiry | toLocalDatePipe | date: 'mediumDate' }}</span>
            }
          </div>
        </div>
        <mat-divider></mat-divider>

        <div>
          <mat-expansion-panel [expanded]="true" class="header-section">
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{ 'BILLING_SUBSCRIPTIONS.DETAILS' | translate }}
              </mat-panel-title>
            </mat-expansion-panel-header>
            <billing-pc-subscription-header
              [subscription]="subscription"
              [billingRecipient]="(billingRecipient$ | async)!"
              [loadingBillingRecipient]="loadingBillingRecipient$ | async"
              [canAccessBillingServicePeriods]="(canAccessBillingServicePeriods$ | async) ?? false"
            ></billing-pc-subscription-header>
          </mat-expansion-panel>

          <mat-divider></mat-divider>

          <div>
            <mat-expansion-panel [expanded]="false" class="invoice-name-panel">
              <mat-expansion-panel-header>
                <mat-panel-title>{{
                  'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVOICE_NAME' | translate
                }}</mat-panel-title>
              </mat-expansion-panel-header>
              <mat-panel-description class="panel-hint-text">
                {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVOICE_NAME_HINT' | translate }}
              </mat-panel-description>
              <div class="row">
                <span class="col-12 col-xs-12">
                  <glxy-form-field [size]="'small'" [bottomSpacing]="'none'" [showLabel]="false">
                    <input
                      matInput
                      type="text"
                      formControlName="invoiceName"
                      onwheel="this.blur()"
                      placeholder="{{
                        'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVOICE_NAME_PLACEHOLDER' | translate
                      }}"
                    />
                  </glxy-form-field>
                </span>
              </div>
            </mat-expansion-panel>
          </div>

          <mat-divider></mat-divider>

          <div>
            <mat-expansion-panel [expanded]="true" class="sale-price-panel">
              <mat-expansion-panel-header>
                <mat-panel-title>{{ 'COMMON.PRICING.PRICING' | translate }}</mat-panel-title>
              </mat-expansion-panel-header>

              <div class="row toggle">
                <mat-slide-toggle formControlName="autoBillable">
                  {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.AUTO_BILLABLE_TEXT' | translate }}
                </mat-slide-toggle>
                <div class="tooltip">
                  <mat-icon
                    class="info-icon"
                    [tooltipTitle]="
                      'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.AUTO_BILLABLE_TOOLTIP.TITLE' | translate
                    "
                    [glxyTooltip]="
                      'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.AUTO_BILLABLE_TOOLTIP.DESCRIPTION' | translate
                    "
                  >
                    info_outline
                  </mat-icon>
                </div>
              </div>

              <div class="row details-item">
                <div class="col-6 col-xs-6">
                  <span class="details-label unit-price-font">
                    {{ 'COMMON.PRICING.UNIT_PRICE_SENTENCE_CASE' | translate }}
                  </span>
                </div>
                <span class="col-6 col-xs-6 amount">
                  <glxy-form-field
                    class="amount-input"
                    [size]="'small'"
                    [bottomSpacing]="'none'"
                    [showLabel]="false"
                    [prefixText]="currencyCode | formatCurrency: 'wide'"
                  >
                    <input
                      matInput
                      class="right-align-text"
                      type="text"
                      formControlName="amount"
                      onwheel="this.blur()"
                      (blur)="formatPrice(formGroup.get('amount'))"
                      required
                    />
                  </glxy-form-field>
                </span>
              </div>

              <div class="row details-item">
                <div class="col-6 col-xs-6">
                  <span class="details-label unit-price-font">
                    {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.SETUP_FEE' | translate }}
                    <mat-icon
                      class="info-icon icon-tooltip"
                      [glxyTooltip]="'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.SETUP_FEE_TOOLTIP' | translate"
                      [highContrast]="true"
                    >
                      info_outline
                    </mat-icon>
                  </span>
                </div>
                <span class="col-6 col-xs-6 amount">
                  <glxy-form-field
                    class="amount-input"
                    [size]="'small'"
                    [bottomSpacing]="'none'"
                    [showLabel]="false"
                    [prefixText]="currencyCode | formatCurrency: 'wide'"
                  >
                    <input
                      matInput
                      class="right-align-text"
                      type="text"
                      formControlName="setupFee"
                      onwheel="this.blur()"
                      (blur)="formatPrice(formGroup.get('setupFee'))"
                      required
                    />
                  </glxy-form-field>
                </span>
              </div>

              <ng-container *ngIf="lineItemTaxData$ | async as lineItemTaxData">
                <div class="details-section subtotal-section">
                  <div class="details-item">
                    <span class="details-label subtotal-font">{{ 'COMMON.PRICING.SUBTOTAL' | translate }}</span>
                    <ng-container *ngIf="unitPriceAmountInput$ | async as amount">
                      <ng-container
                        *ngIf="subtotalAmountWithDiscountsApplied$ | async as subtotalAmountWithDiscountsApplied"
                      >
                        <ng-container *ngIf="loadingTotalAmount$$ | async; else showAmount">
                          <div class="stencil-shimmer"></div>
                        </ng-container>
                        <ng-template #showAmount>
                          <span
                            [class.strike]="
                              subtotalAmountWithDiscountsApplied?.amountInCents > 0 &&
                              amount?.amountInCents !== subtotalAmountWithDiscountsApplied?.amountInCents
                            "
                          >
                            {{ amount.amountInCents || 0 | currencyWithoutUSD: currencyCode! : 'en-US' }}
                          </span>
                        </ng-template>
                      </ng-container>
                    </ng-container>
                  </div>

                  <mat-divider></mat-divider>

                  @if ((isInternalTaxProvider$ | async) === true) {
                    <ng-container *ngIf="lineItemTaxData?.taxRates?.length > 0; else addTaxRates">
                      <ng-container *ngFor="let tax of lineItemTaxData.appliedTaxes; let i = index">
                        <div class="details-item">
                          <div class="details-label">
                            <span>
                              {{ tax | taxDescription: false }}
                            </span>
                          </div>
                          <span class="right-align-text">
                            <ng-container *ngIf="loadingTaxAmount$$ | async; else showTaxAmount">
                              <div class="stencil-shimmer"></div>
                            </ng-container>
                            <ng-template #showTaxAmount>
                              {{ tax.amount || 0 | currencyWithoutUSD: currencyCode! : 'en-US' }}
                            </ng-template>
                          </span>
                        </div>
                        <mat-divider></mat-divider>
                      </ng-container>
                    </ng-container>
                    @if (lineItemTaxData?.taxRates?.length > 0) {
                      <span class="view-taxes-link">
                        <a [routerLink]="['/billing/taxes']">
                          {{ 'COMMON.PRICING.VIEW_TAX_RATES' | translate }}
                        </a>
                      </span>
                    }
                    <ng-template #addTaxRates>
                      <span class="add-taxes-link">
                        <a [routerLink]="['/billing/taxes']">
                          {{ 'COMMON.PRICING.ADD_TAX_RATES' | translate }}
                        </a>
                      </span>
                    </ng-template>
                  }
                </div>
              </ng-container>

              @if (hasActiveDiscount() === false) {
                @if (
                  subscription.status === SubscriptionStatus.Active && subscription.frequency !== Frequency.OneTime
                ) {
                  <div class="add-discounts-link">
                    <a (click)="openRetailDiscountDialog(subscription, null)">
                      {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.TITLE' | translate }}
                    </a>
                  </div>
                }
              }

              <ng-container *ngIf="discounts$ | async as discounts">
                <ng-container *ngIf="(loadingDiscounts$$ | async) || (isExpiringDiscount$ | async); else showDiscounts">
                  <div class="discounts-loading">
                    <div class="stencil-shimmer"></div>
                  </div>
                </ng-container>
                <ng-template #showDiscounts>
                  <mat-divider *ngIf="discounts.length > 0"></mat-divider>
                  <ng-container *ngFor="let discount of discounts">
                    <div class="details-item discounts-list">
                      <div class="details-label">
                        <div
                          class="discount-title"
                          [class.greyed-out]="discount.isExpired"
                          [glxyTooltip]="discount.displayTitle"
                        >
                          {{ discount.displayTitle }}
                        </div>
                        <div class="discount-subtitle" [class.greyed-out]="discount.isExpired">
                          {{ discount.displaySubtitle }}
                        </div>
                      </div>

                      <div class="details-information">
                        <div>
                          <ng-container *ngIf="!discount.isExpired && !discount.isScheduled">
                            <div class="discount-amount greyed-out">
                              -{{
                                discount.calculatedDiscountAmountInCents || 0
                                  | currencyWithoutUSD: currencyCode! : 'en-US'
                              }}
                            </div>
                            <div class="subtotal-font">
                              {{
                                discount.recalculatedSubtotalInCents || 0 | currencyWithoutUSD: currencyCode! : 'en-US'
                              }}
                            </div>
                          </ng-container>
                        </div>

                        <div class="discount-actions" *ngIf="!discount.isExpired">
                          <button
                            class="discount-actions-btn"
                            mat-icon-button
                            type="button"
                            color="secondary"
                            [matMenuTriggerFor]="discountMenu"
                          >
                            <mat-icon>more_vert</mat-icon>
                          </button>
                          <mat-menu #discountMenu="matMenu">
                            <button
                              mat-menu-item
                              (click)="openRetailDiscountDialog(subscription, discount.discountAPI)"
                            >
                              {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.EDIT_DISCOUNT' | translate }}
                            </button>
                            <button mat-menu-item (click)="expireDiscount(discount)">
                              {{ 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.CANCEL_DISCOUNT' | translate }}
                            </button>
                          </mat-menu>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </ng-template>
              </ng-container>
            </mat-expansion-panel>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="total-section">
          <div class="details-section">
            <div class="details-item unit-price-font">
              @if ((isInternalTaxProvider$ | async) === true) {
                <span class="details-label">{{ 'COMMON.PRICING.TOTAL' | translate }}</span>
              } @else {
                <span class="details-label">{{ 'COMMON.PRICING.TOTAL_BEFORE_TAX' | translate }}</span>
              }
              <span>
                <ng-container *ngIf="loadingTotalAmount$$ | async; else showTotal">
                  <div class="stencil-shimmer"></div>
                </ng-container>

                <ng-template #showTotal>
                  <ng-container *ngIf="lineItemTaxData$ | async as lineItemTaxData">
                    <ng-container *ngIf="subtotalAmountWithDiscountsApplied$ | async as amount">
                      {{
                        (amount?.amountInCents || 0) + (lineItemTaxData?.totalTaxableAmount || 0)
                          | currencyWithoutUSD: currencyCode! : 'en-US'
                      }}
                    </ng-container>
                  </ng-container>
                </ng-template>
              </span>
            </div>
          </div>
        </div>

        <span class="content-spacer"></span>

        <mat-toolbar class="sticky-footer">
          <button
            type="submit"
            mat-flat-button
            color="primary"
            [disabled]="formGroup.invalid || (isSubmitting$ | async) === true"
          >
            <glxy-button-loading-indicator [isLoading]="isSubmitting$ | async">
              {{ 'BILLING_SUBSCRIPTIONS.SAVE' | translate }}
            </glxy-button-loading-indicator>
          </button>
        </mat-toolbar>
      </div>
    </ng-container>
  </ng-container>
</form>
