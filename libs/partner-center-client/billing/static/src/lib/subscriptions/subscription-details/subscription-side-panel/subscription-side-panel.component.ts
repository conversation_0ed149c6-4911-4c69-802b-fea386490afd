import { Component, DestroyRef, Inject, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  BillableItem,
  CalculateLineItemsTaxResponse,
  Consumer,
  Discount,
  DiscountConsumer,
  DiscountService,
  DiscountType,
  Frequency,
  LineItem,
  ListDiscountsFilter,
  MerchantService,
  SubscriptionStatus,
  TaxRateService,
} from '@galaxy/billing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  DYNAMIC_OPEN_CLOSE_TEMPLATE_DATA,
  DynamicOpenCloseTemplateRefService,
} from '@vendasta/galaxy/side-drawer/src/dynamic-open-close-template-ref.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { User } from '@vendasta/iamv2';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  filter,
  firstValueFrom,
  lastValueFrom,
  map,
  Observable,
  of,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { AsyncPipe, DatePipe, formatDate, NgForOf, NgIf } from '@angular/common';
import {
  BillingUiModule,
  DiscountDialogInfo,
  DiscountFromRetailDialog,
  OnSaveResponse,
  RetailDiscountDialogComponent,
} from '@vendasta/billing-ui';
import { SubscriptionPageService } from '../subscription-page.service';
import {
  convertCentsToDollars,
  convertDollarsToCents,
  FormatCurrencyPipe,
  SubscriptionStatusBadgePipe,
  ToLocalDatePipe,
} from '../../utils';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { SubscriptionHeaderComponent } from '../subscription-header/subscription-header.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';
import { RouterLink } from '@angular/router';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { formatUTCDate, getUTCStartOfDay } from '@vendasta/galaxy/utility/date-utils';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { SubscriptionsService } from '../../subscriptions.service';
import { isAfter } from 'date-fns';

import { FeatureFlagService } from '@galaxy/partner';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN, PartnerCenterBillingConfig } from '../../../core/config';
import { TaxProviderType } from '@vendasta/billing';

export const SubscriptionSidePanelTemplateID = 'subscriptionSidePanel';

interface Subtotal {
  amountInCents: number;
}

export interface DiscountDisplay {
  displayTitle: string;
  displaySubtitle: string;
  isExpired: boolean;
  isScheduled: boolean;
  calculatedDiscountAmountInCents: number;
  recalculatedSubtotalInCents: number;
  discountAPI: Discount;
}

@Component({
  selector: 'billing-subscription-side-panel',
  templateUrl: './subscription-side-panel.component.html',
  styleUrls: ['./subscription-side-panel.component.scss'],
  imports: [
    ReactiveFormsModule,
    SubscriptionStatusBadgePipe,
    NgIf,
    AsyncPipe,
    GalaxyBadgeModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatDividerModule,
    SubscriptionHeaderComponent,
    MatExpansionModule,
    TranslateModule,
    MatSlideToggleModule,
    GalaxyButtonLoadingIndicatorModule,
    MatMenuModule,
    MatToolbarModule,
    BillingUiModule,
    RouterLink,
    GalaxyTooltipModule,
    NgForOf,
    MatInputModule,
    GalaxyFormFieldModule,
    DatePipe,
    ToLocalDatePipe,
    FormatCurrencyPipe,
  ],
})
export class SubscriptionSidePanelComponent implements OnInit {
  public readonly Frequency = Frequency;
  public readonly SubscriptionStatus = SubscriptionStatus;
  public readonly today = new Date();

  public unitPriceAmountInput$!: Observable<Subtotal>;
  public subtotalAmountWithDiscountsApplied$$ = new BehaviorSubject<Subtotal>({ amountInCents: 0 });
  public subtotalAmountWithDiscountsApplied$ = this.subtotalAmountWithDiscountsApplied$$.asObservable();
  public lineItemTaxData$!: Observable<CalculateLineItemsTaxResponse>;
  public discountsResponse$!: Observable<Discount[]>;
  public discounts$!: Observable<DiscountDisplay[]>;

  public isSubmitting$!: Observable<boolean>;
  public billingRecipient$!: Observable<User | null>;
  public loadingBillingRecipient$!: Observable<boolean>;
  public isExpiringDiscount$!: Observable<boolean>;
  public isCanceling$!: Observable<boolean>;
  public isUpdatingRenewalDate$!: Observable<boolean>;
  public isUpdatingServicePeriod$!: Observable<boolean>;
  public subscription$!: Observable<BillableItem>;
  public canAccessBillingServicePeriods$!: Observable<boolean>;
  public currencyCode$!: Observable<string>;
  public isInternalTaxProvider$!: Observable<boolean>;

  public loadingTaxAmount$$ = new BehaviorSubject<boolean>(true);
  public loadingDiscounts$$ = new BehaviorSubject<boolean>(true);
  public loadingTotalAmount$$ = new BehaviorSubject<boolean>(true);

  public formGroup!: FormGroup;
  private readonly destroyRef = inject(DestroyRef);

  private editedActiveDiscountValue: WritableSignal<Discount | undefined> = signal(undefined);
  private patchDiscount$$ = new BehaviorSubject<boolean>(false);
  // only 1 discount can be active/scheduled per subscription
  public hasActiveDiscount: WritableSignal<boolean> = signal(false);

  get amountControl(): FormControl {
    return this.formGroup.get('amount') as FormControl;
  }

  get setupFeeControl(): FormControl {
    return this.formGroup.get('setupFee') as FormControl;
  }

  get autoBillableControl(): FormControl {
    return this.formGroup.get('autoBillable') as FormControl;
  }

  get invoiceNameControl(): FormControl {
    return this.formGroup.get('invoiceName') as FormControl;
  }

  constructor(
    @Inject(DYNAMIC_OPEN_CLOSE_TEMPLATE_DATA) public templateSubscription$: Observable<BillableItem>,
    private taxRateService: TaxRateService,
    private drawerService: DynamicOpenCloseTemplateRefService,
    private subscriptionPageService: SubscriptionPageService,
    private readonly subscriptionService: SubscriptionsService,
    private formBuilder: FormBuilder,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
    private discountService: DiscountService,
    public dialog: MatDialog,
    private featureFlagService: FeatureFlagService,
    private merchantService: MerchantService,
    @Inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN) private readonly config: PartnerCenterBillingConfig,
  ) {}

  ngOnInit(): void {
    this.formGroup = this.formBuilder.group({
      amount: new FormControl<string>((0).toFixed(2), [Validators.pattern('^[0-9.,-]*$')]),
      setupFee: new FormControl<string>((0).toFixed(2), [Validators.pattern('^[0-9.,-]*$')]),
      autoBillable: new FormControl<boolean>(true),
      invoiceName: new FormControl<string>(''),
    });

    this.templateSubscription$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((subscription) => {
      this.subscriptionPageService.init(subscription);
      this.setAmountControl(subscription);
      this.setSetupFeeControl(subscription);
      this.setAutoBillableControl(subscription);
      this.setInvoiceNameControl(subscription);
    });

    this.unitPriceAmountInput$ = combineLatest([
      this.setupFeeControl.valueChanges.pipe(startWith(this.setupFeeControl.value)),
      this.amountControl.valueChanges.pipe(startWith(this.amountControl.value)),
    ]).pipe(
      map(([setupFee, amount]) => {
        let unitPriceAmount = +setupFee + +amount;
        if (this.setupFeeControl.disabled) {
          unitPriceAmount = +amount;
        }
        this.loadingTaxAmount$$.next(true);
        this.loadingTotalAmount$$.next(true);
        this.loadingDiscounts$$.next(true);
        return { amountInCents: convertDollarsToCents(unitPriceAmount) };
      }),
      // include set up fee in unit price amount if it applies to the next/upcoming invoice
      startWith({
        amountInCents: !this.setupFeeControl.disabled
          ? convertDollarsToCents((+this.setupFeeControl?.value || 0) + +this.amountControl?.value || 0)
          : convertDollarsToCents(+this.amountControl?.value || 0),
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.subscription$ = this.subscriptionPageService.subscription$;
    this.billingRecipient$ = this.subscriptionPageService.billingRecipient$;
    this.loadingBillingRecipient$ = this.subscriptionPageService.loadingBillingRecipient$;
    this.isExpiringDiscount$ = this.subscriptionPageService.isExpiringDiscount$;
    this.isSubmitting$ = this.subscriptionPageService.isUpdatingPrice$;
    this.isCanceling$ = this.subscriptionPageService.isCanceling$;
    this.isUpdatingRenewalDate$ = this.subscriptionPageService.isUpdatingRenewalDate$;
    this.isUpdatingServicePeriod$ = this.subscriptionPageService.isUpdatingServicePeriod$;

    this.currencyCode$ = this.subscription$.pipe(
      filter((subscription) => !!subscription),
      switchMap((subscription) => {
        if (subscription.price?.currency) {
          return of(subscription.price?.currency);
        }
        return this.getRetailCurrencyForMarket(subscription.merchantId, subscription.customerId);
      }),
    );

    this.isInternalTaxProvider$ = this.subscription$.pipe(
      filter((subscription) => !!subscription),
      switchMap((subscription) => {
        return this.taxRateService.getTaxProvider(subscription.merchantId).pipe(
          map((p) => p?.taxProviderType === TaxProviderType.TAX_PROVIDER_TYPE_INTERNAL),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      }),
    );

    this.lineItemTaxData$ = combineLatest([
      this.subtotalAmountWithDiscountsApplied$,
      this.subscription$,
      this.isInternalTaxProvider$,
    ]).pipe(
      debounceTime(300),
      switchMap(([amount, subscription, isInternalTaxProvider]) => {
        if (isInternalTaxProvider === false) {
          return of({
            appliedTaxes: [],
            lineItemTaxes: [],
            totalTaxableAmount: 0,
            taxRates: [],
          } as CalculateLineItemsTaxResponse);
        }

        const lineItem: LineItem = {
          amount: amount.amountInCents,
          sku: subscription.sku,
        };
        // Round to the nearest integer before computing taxes
        lineItem.amount = Math.round(lineItem.amount);
        return this.taxRateService.calculateLineItems(subscription.merchantId, subscription.customerId, [lineItem]);
      }),
      tap(() => {
        this.loadingTotalAmount$$.next(false);
        this.loadingTaxAmount$$.next(false);
      }),
      shareReplay(1),
    );

    this.discountsResponse$ = this.subscription$.pipe(
      debounceTime(300),
      switchMap((subscription) => {
        const filters: ListDiscountsFilter = {
          merchantId: subscription.merchantId,
          customerIds: [subscription.customerId],
          consumers: [Consumer.CONSUMER_RETAIL],
          subscriptionIds: [subscription.subscriptionId!],
        };
        return this.discountService.list(filters, '', 100);
      }),
      map((discountResp) => {
        return discountResp?.results || [];
      }),
    );

    this.discounts$ = combineLatest([this.unitPriceAmountInput$, this.discountsResponse$, this.patchDiscount$$]).pipe(
      map(([subtotalInDollars, discounts, _]) => {
        const editedActiveDiscountValue = this.editedActiveDiscountValue();
        let newSubtotalAmount = subtotalInDollars.amountInCents;
        if (editedActiveDiscountValue) {
          const patchDiscountIndex = discounts.findIndex((d) => d.id === editedActiveDiscountValue?.id);
          if (patchDiscountIndex !== -1) {
            discounts[patchDiscountIndex] = editedActiveDiscountValue;
          } else {
            discounts.unshift(editedActiveDiscountValue);
          }
          this.editedActiveDiscountValue.set(undefined);
        }
        const resultsWithEndSet = discounts.map((d) => {
          const discount = this.buildDisplayDiscountFromDiscountAPI(d, newSubtotalAmount);
          // decrement the subtotal as the discounts are applied
          newSubtotalAmount = discount.recalculatedSubtotalInCents;
          return discount;
        });
        this.subtotalAmountWithDiscountsApplied$$.next({
          amountInCents: newSubtotalAmount > 0 ? newSubtotalAmount : 0,
        });
        return resultsWithEndSet;
      }),
      map((discounts: DiscountDisplay[]) => sortDiscountsByStartAndEndDates(discounts)),
      tap((discounts: DiscountDisplay[]) => {
        discounts.some((d) => !d.isExpired) ? this.hasActiveDiscount.set(true) : this.hasActiveDiscount.set(false);
      }),
      tap(() => {
        this.loadingDiscounts$$.next(false);
      }),
    );

    this.canAccessBillingServicePeriods$ = this.subscription$.pipe(map((subscription) => subscription.merchantId)).pipe(
      switchMap((merchantId) => {
        return this.featureFlagService.batchGetStatus(merchantId, '', ['billing_service_periods']);
      }),
      map((flags) => flags['billing_service_periods']),
      shareReplay(1),
    );
  }

  private isNullDate(date?: Date): boolean {
    if (!date) {
      return true;
    }
    return date.getUTCFullYear() === 1 && date.getUTCMonth() === 0 && date.getUTCDate() === 1;
  }

  private buildDisplayDiscountFromDiscountAPI(d: Discount, subtotalInCents: number): DiscountDisplay {
    // Discount display title
    let displayTitle = d.description || '';

    // Discount display subtitle
    const foreverDisplay = this.translateService.instant(
      'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.TIME_FRAMES.FOREVER',
    );
    let displaySubtitle = foreverDisplay;

    const startsDisplay = this.translateService.instant(
      'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.TIME_FRAMES.STARTS',
    );
    const scheduledDisplay = this.translateService.instant(
      'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.TIME_FRAMES.SCHEDULED',
    );

    let isScheduled = false;
    if (d.end && !this.isNullDate(d.end)) {
      // if there's an expiry date, show the timeline of the discount
      displaySubtitle =
        formatDate(d.start!, 'mediumDate', 'en-US', 'UTC') + ' - ' + formatDate(d.end, 'mediumDate', 'en-US', 'UTC');
    }

    // Discount marked as expired should not be applied. Expiry for retail is inclusive, meaning it will still be applied on the `end` date
    const isExpired = (d.end && !this.isNullDate(d.end) && d.end < getUTCStartOfDay(this.today)) ?? false;
    if (isExpired) {
      const expiredDisplay = this.translateService.instant(
        'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.TIME_FRAMES.EXPIRED',
      );
      displayTitle = expiredDisplay + ' - ' + displayTitle;
    } else {
      // handles displaying scheduled discounts
      if (d.start && d.start! > this.today) {
        // if it's scheduled to start in the future
        isScheduled = true;
        displaySubtitle = formatDate(d.start!, 'mediumDate', 'en-US', 'UTC');
        if (d.end && !this.isNullDate(d.end)) {
          displaySubtitle = displaySubtitle + ' - ' + formatDate(d.end, 'mediumDate', 'en-US', 'UTC');
        } else {
          displaySubtitle = displaySubtitle + ' - ' + foreverDisplay;
        }
        displaySubtitle = startsDisplay + ' ' + displaySubtitle;
        displayTitle = scheduledDisplay + ' - ' + displayTitle;
      }
    }

    let discountAmount = 0;
    let newSubtotalAmount = subtotalInCents;
    // Discount application to subtotal only if the discount is currently active
    if (!isExpired && !isScheduled) {
      // Discount calculated discount amount based on the current subtotal
      if (d.discountType === DiscountType.PERCENT_AMOUNT) {
        // calculate the discount amount using the subtotal
        discountAmount = (d.amount / 100) * subtotalInCents;
      } else if (d.discountType === DiscountType.FIXED_AMOUNT_PER_UNIT) {
        // calculate the discount amount using the subtotal
        discountAmount = Math.min(d.amount, subtotalInCents);
      }
      newSubtotalAmount = subtotalInCents - discountAmount;
    }

    return {
      ...d,
      displayTitle: displayTitle,
      displaySubtitle: displaySubtitle,
      isExpired: isExpired,
      isScheduled: isScheduled,
      calculatedDiscountAmountInCents: discountAmount <= 0 ? 0 : discountAmount,
      recalculatedSubtotalInCents: newSubtotalAmount <= 0 ? 0 : newSubtotalAmount,
      discountAPI: d,
    };
  }

  private setAutoBillableControl(subscription: BillableItem): void {
    if (subscription.frequency === Frequency.OneTime) {
      if (subscription?.billingStart < this.today) {
        this.autoBillableControl.setValue(false);
        this.autoBillableControl.disable();
      }
    } else if (subscription.expiry && subscription.expiry <= this.today) {
      this.autoBillableControl.setValue(false);
      this.autoBillableControl.disable();
    } else {
      this.autoBillableControl.setValue(subscription.autoBillable);
      this.autoBillableControl.enable();
    }
  }

  private setInvoiceNameControl(subscription: BillableItem): void {
    this.invoiceNameControl.setValue(subscription.description);
  }

  private setAmountControl(subscription: BillableItem): void {
    this.amountControl.setValue(convertCentsToDollars(subscription?.price?.pricingRules[0]?.price || 0)?.toFixed(2));
    if (!!subscription.expiry && subscription.expiry <= this.today) {
      this.amountControl.disable();
      return;
    }

    if (this.amountControl.disabled) {
      this.amountControl.enable();
      return;
    }
  }

  // disable the setup fee control if billing start or expiry date is in the past
  private setSetupFeeControl(subscription: BillableItem): void {
    this.setupFeeControl.setValue(convertCentsToDollars(subscription?.price?.setupFee)?.toFixed(2));
    if (
      (!!subscription.billingStart && subscription.billingStart <= this.today) ||
      (!!subscription.expiry && subscription.expiry <= this.today)
    ) {
      this.setupFeeControl.disable();
      return;
    }

    if (this.setupFeeControl.disabled) {
      this.setupFeeControl.enable();
      return;
    }
  }

  // submit will update the retail price and auto billable status of the subscription.
  // If the user exits out from updating the retail price, neither action will be taken.
  async submit(): Promise<void> {
    try {
      const amountInCents = convertDollarsToCents(this.amountControl.value);
      const setupFeeInCents = convertDollarsToCents(this.setupFeeControl.value);
      const currency = await firstValueFrom(this.currencyCode$);
      const upsertResult = await this.subscriptionPageService.upsertRetailPrice(
        amountInCents,
        setupFeeInCents,
        currency,
      );
      if (!upsertResult) {
        return;
      }

      const currentSubscription = await firstValueFrom(this.subscription$);
      if (currentSubscription.autoBillable !== this.formGroup.value.autoBillable) {
        // only call to update if the value is different from what's currently saved
        await this.subscriptionPageService.setAutoBillable(
          currentSubscription.subscriptionId!,
          this.formGroup.value.autoBillable || false,
        );
      }

      if (currentSubscription.description !== this.formGroup.value.invoiceName) {
        await this.subscriptionPageService.setDescription(this.invoiceNameControl.value);
      }

      this.snackbarService.openSuccessSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.SAVE_UNIT_PRICING_SUCCESS'),
      );
    } catch (err: any) {
      this.snackbarService.openErrorSnack(
        this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.SAVE_UNIT_PRICING_ERROR', {
          err: err.error?.message || err.message || err,
        }),
      );
    }
  }

  // Returns whether the call was successful or not
  async onSaveDiscount(discount: DiscountFromRetailDialog, mode: 'edit' | 'create'): OnSaveResponse {
    const successMessage =
      mode === 'edit'
        ? 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.UPDATE_SUCCESS'
        : 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.CREATE_SUCCESS';
    // format start date
    const formatter = 'yyyy-MM-dd';
    // the startDate form control isn't initialized when the "forever" option is selected; use now()
    const formattedStart = formatUTCDate(discount.start || new Date(), formatter) + 'T00:00:00Z';

    // format end date if one should be set
    let formattedEnd = '';
    if (discount.end) {
      formattedEnd = formatUTCDate(discount.end, formatter) + 'T23:59:59Z'; // setting to end of day essentially makes the discount inclusive of the end date
    }

    let call$: Observable<DiscountFromRetailDialog>;
    if (mode === 'edit') {
      call$ = this.subscriptionService
        .updateDiscount(
          discount.merchantId,
          discount.id,
          discount.discountType,
          discount.amount,
          formattedStart,
          formattedEnd,
          true,
          discount.description,
          discount.customerId,
          discount.subscriptionId,
        )
        .pipe(
          map((_) => {
            return {
              ...discount,
            };
          }),
        );
    } else {
      call$ = this.subscriptionService
        .createDisount(
          discount.merchantId,
          discount.SKU,
          discount.discountType,
          discount.amount,
          formattedStart,
          formattedEnd,
          true,
          discount.description,
          discount.customerId,
          DiscountConsumer.RETAIL,
          discount.subscriptionId,
        )
        .pipe(
          map((resp) => {
            return {
              ...discount,
              id: resp,
            };
          }),
        );
    }

    try {
      const discountResponse = await lastValueFrom(call$);
      this.snackbarService.openSuccessSnack(this.translateService.instant(successMessage));
      return {
        success: true,
        discount: discountResponse,
      };
      // Close dialog
    } catch (e) {
      if (e.status === 409) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.ERROR_ALREADY_EXISTS'),
        );
      } else {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.DISCOUNTS.ERROR_UNKNOWN'),
        );
      }
    }
    return {
      success: false,
      discount: discount,
    };
  }

  close(): void {
    this.drawerService.close();
  }

  async expireDiscount(discount: DiscountDisplay): Promise<void> {
    const expiredDiscount = await this.subscriptionPageService.expireDiscount(discount.discountAPI);
    this.editedActiveDiscountValue.set(expiredDiscount);
    this.patchDiscount$$.next(true);
  }

  async cancel(): Promise<void> {
    await this.subscriptionPageService.cancel();
  }

  async uncancel(): Promise<void> {
    await this.subscriptionPageService.uncancel();
  }

  async updateRenewalDate(): Promise<void> {
    const canAccessBillingServicePeriods = await firstValueFrom(this.canAccessBillingServicePeriods$);
    await this.subscriptionPageService.updateRenewalDate(canAccessBillingServicePeriods);
  }

  async updateServicePeriod(): Promise<void> {
    await this.subscriptionPageService.updateServicePeriod();
  }

  async openRetailDiscountDialog(subscription: BillableItem, discount: Discount | null): Promise<void> {
    const dialogData: DiscountDialogInfo = {
      discount: {
        merchantId: subscription.merchantId,
        customerId: subscription.customerId,
        SKU: subscription.sku,
        subscriptionId: subscription.subscriptionId,
        id: discount?.id ?? '',
        ...(discount ?? {}),
      },
      currencyCode: subscription.price?.currency ?? 'USD',
      mode: discount ? 'edit' : 'create',
      onSave: (discount, mode) => this.onSaveDiscount(discount, mode),
    };
    const result = await firstValueFrom(
      this.dialog
        .open(RetailDiscountDialogComponent, {
          width: '450px',
          data: dialogData,
        })
        .afterClosed(),
    );
    if (result) {
      this.editedActiveDiscountValue.set(result);
      this.patchDiscount$$.next(true);
    }
  }

  private getRetailCurrencyForMarket(partnerId: string, customerId: string): Observable<string> {
    const marketId$ = this.config.customerService.getCustomer(customerId).pipe(
      map((a) => a.marketId),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    const retailConfigs$ = marketId$.pipe(
      switchMap((marketId) => this.merchantService.getMultiRetailConfigurations(partnerId, [marketId])),
    );

    return combineLatest([marketId$, retailConfigs$]).pipe(
      take(1),
      map(([marketId, retailConfigs]) => {
        const marketRetailConfig = retailConfigs.get(marketId);
        return marketRetailConfig ? marketRetailConfig.currencyCode : '';
      }),
    );
  }

  formatPrice(control: AbstractControl | null) {
    const value = parseFloat(control?.value);
    if (!isNaN(value)) {
      control?.setValue(value.toFixed(2), { emitEvent: false });
    }
  }
}

// sortDiscountsByStatusAndExpiry sorts the discounts based on the end dates
export function sortDiscountsByStartAndEndDates(discounts: DiscountDisplay[]): DiscountDisplay[] {
  return discounts.sort((d1, d2) => {
    if (!d1.isExpired) {
      // if d1 is not expired, d1 should be sorted first
      return -1;
    }
    if (!d2.isExpired) {
      // if d2 is not expired, d2 should be sorted first
      return 1;
    }
    if (!!d1.discountAPI.end && !!d2.discountAPI.end) {
      // sort by most recent end date
      return isAfter(d1.discountAPI.end, d2.discountAPI.end) ? -1 : 1;
    }
    return 0;
  });
}
