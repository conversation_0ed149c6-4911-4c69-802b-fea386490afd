import { Component, effect, inject, Inject, input, Signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BillingUiModule } from '@vendasta/billing-ui';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyColumnDef, GalaxyDataSource, GalaxyTableModule } from '@vendasta/galaxy/table';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatIconModule } from '@angular/material/icon';

import { SMBInvoicingModule } from '@vendasta/smb-invoicing';
import { SubscriptionCreateDataSource } from './subscription-create-table-form.datasource';
import { SubscriptionItem } from '../../../billing-line-item/item-selector-dialog/item-selector-dialog.component';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatDatepicker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { MatInput } from '@angular/material/input';
import { MatOption, MatSelectModule } from '@angular/material/select';
import { Frequency } from '@galaxy/billing';
import { FeatureFlagService } from '@galaxy/partner';
import { map, Observable, shareReplay, switchMap } from 'rxjs';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN, PartnerCenterBillingConfig } from '../../../core/config';
import { toSignal } from '@angular/core/rxjs-interop';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { addDays, isBefore } from 'date-fns';
import { MatDialog } from '@angular/material/dialog';
import { ViewSubscriptionDialogComponent } from '../view-subscription-dialog/view-subscription-dialog.component';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { FormatCurrencyPipe } from './../../utils';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';

enum TableColumn {
  ITEM = 'item',
  QUANTITY = 'quantity',
  PRICE = 'price',
  FREQUENCY = 'frequency',
  BILLING_START = 'billingStart',
  RENEWAL_START = 'renewalStart',
  SERVICE_PERIOD = 'servicePeriod',
  EXPIRY = 'expiry',
  REMOVE = 'remove',
  VIEW = 'view',
  SETUP_FEE = 'setup-fee',
}

const CUSTOM_DATE_FORMATS = {
  display: {
    dateInput: {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    },
    monthYearLabel: { month: 'short', year: 'numeric' },
  },
};

interface FrequencyOption {
  value: Frequency;
  labelTranslationKey: string;
}

@Component({
  selector: 'billing-subscription-create-table-form',
  providers: [SubscriptionCreateDataSource, { provide: MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS }],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatInput,
    MatSelectModule,
    MatOption,
    MatDatepickerToggle,
    MatDatepickerInput,
    MatDatepicker,
    BillingUiModule,
    GalaxyEmptyStateModule,
    GalaxyTableModule,
    GalaxyTooltipModule,
    TranslateModule,
    MatIconModule,
    GalaxyPopoverModule,
    MatButtonModule,
    MatTableModule,
    MatMenuModule,
    SMBInvoicingModule,
    FormatCurrencyPipe,
  ],
  templateUrl: './subscription-create-table-form.component.html',
  styleUrl: './subscription-create-table-form.component.scss',
})
export class SubscriptionCreateTableFormComponent {
  PopoverPositions = PopoverPositions;
  Frequency = Frequency;

  public form: FormGroup;
  frequencyOptions: FrequencyOption[] = [];

  hasAccessBillingServicePeriods$: Observable<boolean>;
  hasAccessBillingServicePeriods: Signal<boolean | undefined>;

  readonly selectedSubscriptionItems = input<SubscriptionItem[]>();
  readonly invoiceDay = input<number>(0);
  readonly currency = input<string>('');
  private today = new Date();
  tomorrow = addDays(this.today, 1);

  columns: GalaxyColumnDef[];

  public tableDataSource: GalaxyDataSource<FormGroup>;
  protected readonly TableColumn = TableColumn;

  confirmationModal = inject(OpenConfirmationModalService);

  constructor(
    @Inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN) private readonly config: PartnerCenterBillingConfig,
    private subscriptionCreateDataSource: SubscriptionCreateDataSource,
    private translateService: TranslateService,
    private formBuilder: FormBuilder,
    private featureFlagService: FeatureFlagService,
    public dialog: MatDialog,
  ) {
    const featureFlags$ = this.config.merchantId$.pipe(
      switchMap((merchantId) => this.featureFlagService.batchGetStatus(merchantId, '', ['billing_service_periods'])),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.hasAccessBillingServicePeriods$ = featureFlags$.pipe(map((resp) => resp['billing_service_periods']));
    this.hasAccessBillingServicePeriods = toSignal(this.hasAccessBillingServicePeriods$);

    this.form = this.formBuilder.group({
      retailSubscriptions: this.subscriptionCreateDataSource.subscriptionsFormArray(),
    });

    this.frequencyOptions = [
      {
        value: Frequency.Monthly,
        labelTranslationKey: this.translateService.instant('BILLING_COMMON.FREQUENCY.MONTHLY'),
      },
      {
        value: Frequency.Yearly,
        labelTranslationKey: this.translateService.instant('BILLING_COMMON.FREQUENCY.YEARLY'),
      },
      {
        value: Frequency.OneTime,
        labelTranslationKey: this.translateService.instant('BILLING_COMMON.FREQUENCY.ONE_TIME'),
      },
    ];

    this.columns = [
      { id: TableColumn.ITEM },
      { id: TableColumn.FREQUENCY },
      { id: TableColumn.BILLING_START },
      { id: TableColumn.EXPIRY },
      { id: TableColumn.QUANTITY },
      { id: TableColumn.PRICE },
      { id: TableColumn.SETUP_FEE },
      { id: TableColumn.VIEW, stickyEnd: true },
      { id: TableColumn.REMOVE, stickyEnd: true },
    ];

    effect(() => {
      const subscriptionItems = this.selectedSubscriptionItems() || [];

      if (subscriptionItems.length === 0) {
        return;
      }

      const invoiceDay = this.invoiceDay() || 0;
      const hasAccessBillingServicePeriods = this.hasAccessBillingServicePeriods();

      this.subscriptionCreateDataSource.addSubscriptionRows(
        subscriptionItems,
        invoiceDay,
        hasAccessBillingServicePeriods || false,
        this.today,
      );
    });

    effect(() => {
      const hasAccessBillingServicePeriods = this.hasAccessBillingServicePeriods();

      if (hasAccessBillingServicePeriods) {
        // add service period column after start/renewal column
        this.columns.splice(3, 0, { id: TableColumn.SERVICE_PERIOD });
      }
    });

    this.tableDataSource = new GalaxyDataSource<FormGroup>(this.subscriptionCreateDataSource);
  }

  public removeSubscriptionRow(index: number): void {
    this.subscriptionCreateDataSource.removeSubscriptionRow(index);
  }

  public clearExpiry(index: number): void {
    this.subscriptionCreateDataSource.clearExpiry(index);
  }

  public viewSubscriptionSummary(subscription: FormGroup): void {
    const billingStartControl = subscription.get('billingStart');

    this.retailSubscriptions.controls.forEach((control) => {
      control.get('billingStart')?.markAsUntouched();
    });

    if (!billingStartControl?.value) {
      billingStartControl?.markAsTouched();
      return;
    }

    this.dialog.open(ViewSubscriptionDialogComponent, {
      width: '380px',
      data: {
        subscriptionName: subscription.get('productName')?.value,
        startBilling: subscription.get('billingStart')?.value,
        servicePeriodAnchor: this.hasAccessBillingServicePeriods() ? subscription.get('servicePeriod')?.value : null,
        frequency: subscription.get('frequency')?.value,
        endDate: subscription.get('expiry')?.value,
      },
    });
  }

  get retailSubscriptions(): FormArray {
    return this.form.get('retailSubscriptions') as FormArray;
  }

  formatPrice(control: AbstractControl | null) {
    const value = parseFloat(control?.value);
    if (!isNaN(value)) {
      control?.setValue(value.toFixed(2), { emitEvent: false });
    }
  }

  onBillingStartChange(billingStart: Date): void {
    const normalizedStartDate = new Date(billingStart).setHours(0, 0, 0, 0);
    const normalizedTomorrow = new Date(this.tomorrow).setHours(0, 0, 0, 0);
    if (isBefore(normalizedStartDate, normalizedTomorrow)) {
      this.confirmationModal.openModal({
        title: 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.BACKDATE_SUBSCRIPTION_WARNING.TITLE',
        message: 'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.BACKDATE_SUBSCRIPTION_WARNING.DESCRIPTION',
        hideCancel: true,
      });
    }
  }
}
