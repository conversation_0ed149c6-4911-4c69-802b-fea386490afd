import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AppliedBundle, AppliedDiscount, Currency, InvoiceItem, TaxRate, TaxRateService } from '@galaxy/billing';
import { PackageService } from '@vendasta/marketplace-packages';
import { of } from 'rxjs';
import { AddTaxRateDialogComponent } from './add-tax-rate-dialog/add-tax-rate-dialog.component';
import { LineItemFormComponent } from './line-item-form.component';
import { CommonLineItemStoreService } from './common-line-item-store.service';
import { MarketplaceApp } from '@galaxy/marketplace-apps/v1';
import { TranslateService } from '@ngx-translate/core';
import { FieldMask } from '@vendasta/billing';
import { EditServicePeriodDialogComponent } from './edit-service-period-dialog/edit-service-period-dialog.component';
import { addDays } from '@vendasta/galaxy/utility/date-utils';

function stubInvoiceItem(): InvoiceItem {
  return {
    amount: 1200,
    appliedTaxes: [],
    appliedDiscounts: [],
    description: 'description',
    id: 'item-id',
    quantity: 1,
    sku: 'sku',
    taxRates: [{ id: 'tax-id' }],
    total: 1200,
    subtotal: 1200,
    bundleId: '',
  };
}

describe('LineItemFormComponent', () => {
  const formBuilder = new FormBuilder();
  const invoiceItemStoreServiceMock: CommonLineItemStoreService = {
    items$: of([]),
    total$: of(0),
    subtotal$: of(0),
    discountTotal$: of(0),
    appliedTaxes$: of([]),
    currency$: of(Currency.USD),
    taxRates$: of([]),
    defaultTaxRates$: of([]),
    bundles$: of([]),
    init: jest.fn(),
    addLineItem: jest.fn(
      (
        _merchantId: string,
        _invoiceId: string,
        _sku: string,
        _amount: number,
        _quantity: number,
        _description: string,
        _taxRates: string[],
      ) => of({}),
    ),
    deleteLineItem: jest.fn((_merchantId: string, _invoiceId: string, _index: number) => of({})),
    updateLineItem: jest.fn(
      (
        _merchantId: string,
        _invoiceId: string,
        _id: string | number,
        _sku: string,
        _amount: number,
        _quantity: number,
        _description: string,
        _taxRates: TaxRate[],
        _appliedDiscounts: AppliedDiscount[],
        _servicePeriodStart?: Date,
        _servicePeriodEnd?: Date,
        _fieldMask?: FieldMask,
      ) => of({}),
    ),
    addBundle: jest.fn((_merchantId: string, _invoiceId: string, _bundleId: string) => of({})),
    updateBundle: jest.fn(
      (
        _merchantId: string,
        _invoiceId: string,
        _bundleId: string,
        _bundleInstanceId: string,
        _hideBundleItems: boolean,
      ) => of({}),
    ),
    updateDefaultTaxes: jest.fn((_merchantId: string, _invoiceId: string, _taxRates: TaxRate[]) => of({})),
    refresh: jest.fn(),
    createNewApp: jest.fn((_appName: string) => of({} as MarketplaceApp)),
  };
  const taxRates: TaxRate[] = [{ id: 'new-tax-id' }];
  const taxRateDialogReferenceMock = {
    afterClosed: jest.fn(() => of(taxRates)),
    close: jest.fn(() => null),
  };
  const servicePeriodStartDateMock = new Date();
  const servicePeriods = {
    start: servicePeriodStartDateMock,
    end: addDays(servicePeriodStartDateMock, 1),
  };
  const servicePeriodDialogReferenceMock = {
    afterClosed: jest.fn(() => of(servicePeriods)),
    close: jest.fn(() => null),
  };
  const emptyResponseDialogReferenceMock = {
    afterClosed: jest.fn(() => of(null)),
    close: jest.fn(() => null),
  };
  let component: LineItemFormComponent;
  let matDialogService: any;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        MatDialogModule,
        NoopAnimationsModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        AddTaxRateDialogComponent,
        EditServicePeriodDialogComponent,
      ],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: MatDialogRef, useValue: {} },
        { provide: TaxRateService, useValue: {} },
        { provide: CommonLineItemStoreService, useValue: invoiceItemStoreServiceMock },
      ],
    });

    matDialogService = TestBed.get(MatDialog);
    jest.spyOn(matDialogService, 'open').mockReturnValue(taxRateDialogReferenceMock);
    component = new LineItemFormComponent(
      formBuilder,
      invoiceItemStoreServiceMock,
      {} as PackageService,
      {} as TaxRateService,
      matDialogService,
      {} as TranslateService,
    );
  });

  afterEach(() => {
    // prevents unintended mock pollution between tests
    jest.clearAllMocks();
  });

  it('when getItemsFormArray is called it return the invoice line items array', () => {
    const formArray = component.getItemsFormArray();
    expect(formArray).toBeInstanceOf(FormArray);
  });

  it('when addCustomItem is called it should call addLineItem for the current merchant and invoice', () => {
    component.merchantId = 'partner-id';
    component.invoiceId = 'invoice-id';
    component.addCustomItem();
    expect(invoiceItemStoreServiceMock.addLineItem).toHaveBeenCalledWith('partner-id', 'invoice-id', '', 0, 1, '', []);
  });

  it('when deleteItem is called it should call deleteLineItem for the current merchant and invoice and line item', () => {
    component.merchantId = 'partner-id';
    component.invoiceId = 'invoice-id';

    const itemsArray = component.getItemsFormArray();
    const item = component.buildItemFormGroup({
      amount: 0,
      appliedTaxes: [],
      appliedDiscounts: [],
      description: '',
      id: 'item-id',
      quantity: 0,
      sku: '',
      taxRates: [],
      subtotal: 0,
      total: 0,
      bundleId: '',
    });
    itemsArray.push(item);

    component.deleteItem(1);
    expect(invoiceItemStoreServiceMock.deleteLineItem).toHaveBeenCalledWith('partner-id', 'invoice-id', 1);
  });

  it('when updateItem is called it should call updateLineItem for the current merchant and invoice and line item information', () => {
    component.merchantId = 'partner-id';
    component.invoiceId = 'invoice-id';
    component.canEdit = true;

    const itemsArray = component.getItemsFormArray();
    const item = component.buildItemFormGroup(stubInvoiceItem());
    itemsArray.push(item);

    const expectedTaxRate: TaxRate[] = [{ id: 'tax-id' }];

    component.updateItem(0);
    expect(invoiceItemStoreServiceMock.updateLineItem).toHaveBeenCalledWith(
      'partner-id',
      'invoice-id',
      'item-id',
      'sku',
      1200,
      1,
      'description',
      expectedTaxRate,
      [],
      null,
      null,
    );
  });

  it('setItemTax when called should open the add tax rate dialog for the current item and merchant and update the line item for updated tax rates', () => {
    component.merchantId = 'partner-id';
    component.invoiceId = 'invoice-id';
    component.canEdit = true;

    const itemsArray = component.getItemsFormArray();
    const item = component.buildItemFormGroup(stubInvoiceItem());
    itemsArray.push(item);

    component.setItemTax(0);
    expect(taxRateDialogReferenceMock.afterClosed).toHaveBeenCalledTimes(1);
    expect(invoiceItemStoreServiceMock.updateLineItem).toHaveBeenCalledWith(
      'partner-id',
      'invoice-id',
      'item-id',
      'sku',
      1200,
      1,
      'description',
      taxRates,
      [],
      null,
      null,
    );
  });

  describe('getTaxPercentageDisplay', () => {
    it('should return formatted percentages for the applied taxes of the current line item', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = false;
      component.appliedTaxes$ = of([
        {
          taxRateId: 'tax-id',
          percentage: 8,
        },
      ]);

      const itemsArray = component.getItemsFormArray();
      const item = component.buildItemFormGroup(stubInvoiceItem());
      itemsArray.push(item);

      const result$ = component.getTaxPercentageDisplay(0);
      result$.subscribe((name) => {
        expect(name).toEqual('8%');
        done();
      });
    });

    it('should return percentages name for multiple tax rates', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      component.appliedTaxes$ = of([
        { taxRateId: 'tax-1', percentage: 6 },
        { taxRateId: 'tax-2', percentage: 5 },
      ]);

      const itemsArray = component.getItemsFormArray();
      const invoiceItem = stubInvoiceItem();
      invoiceItem.taxRates = [{ id: 'tax-1' }, { id: 'tax-2' }];
      const item = component.buildItemFormGroup(invoiceItem);
      itemsArray.push(item);

      const result$ = component.getTaxPercentageDisplay(0);
      result$.subscribe((name) => {
        expect(name).toEqual('6% + 5%');
        done();
      });
    });

    it('should return empty string if line item has no tax', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      component.appliedTaxes$ = of([]);

      const itemsArray = component.getItemsFormArray();
      const invoiceItem = stubInvoiceItem();
      invoiceItem.taxRates = [];
      const item = component.buildItemFormGroup(invoiceItem);
      itemsArray.push(item);

      const result$ = component.getTaxPercentageDisplay(0);
      result$.subscribe((name) => {
        expect(name).toEqual('');
        done();
      });
    });

    it('should return empty string if line tax does not exist', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      component.appliedTaxes$ = of([]);

      const itemsArray = component.getItemsFormArray();
      const item = component.buildItemFormGroup(stubInvoiceItem());
      itemsArray.push(item);

      const result$ = component.getTaxPercentageDisplay(0);
      result$.subscribe((name) => {
        expect(name).toEqual('');
        done();
      });
    });
  });

  describe('getTaxDisplayDescription', () => {
    it('should return a formatted description for an applied tax rate', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = false;
      component.taxRates$ = of([
        {
          id: 'tax-id',
          name: 'PST',
          description: 'description',
          percentage: 6,
        },
      ]);
      component.appliedTaxes$ = of([
        {
          taxRateId: 'tax-id',
          name: 'PST',
          description: 'a different description',
          percentage: 8,
        },
      ]);

      const itemsArray = component.getItemsFormArray();
      const item = component.buildItemFormGroup(stubInvoiceItem());
      itemsArray.push(item);

      const result$ = component.getTaxDisplayDescription('tax-id');
      result$.subscribe((name) => {
        expect(name).toEqual('PST - a different description (8%)');
        done();
      });
    });

    it('should return a shortened formatted string if tax does not have a description', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      component.appliedTaxes$ = of([
        {
          taxRateId: 'tax-id',
          name: 'PST',
          percentage: 6,
        },
      ]);
      const itemsArray = component.getItemsFormArray();
      const item = component.buildItemFormGroup(stubInvoiceItem());
      itemsArray.push(item);

      const result$ = component.getTaxDisplayDescription('tax-id');
      result$.subscribe((name) => {
        expect(name).toEqual('PST (6%)');
        done();
      });
    });

    it('should return an empty string if tax does not exist', (done) => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      component.appliedTaxes$ = of([
        {
          taxRateId: 'tax-id',
          name: 'GST',
          description: 'a different description',
          percentage: 5,
        },
      ]);
      const itemsArray = component.getItemsFormArray();
      const item = component.buildItemFormGroup(stubInvoiceItem());
      itemsArray.push(item);

      const result$ = component.getTaxDisplayDescription('a-different-tax-id');
      result$.subscribe((name) => {
        expect(name).toEqual('');
        done();
      });
    });
  });

  it('formatTaxAmount should return a string representation of the given number', () => {
    const result = component.formatTaxAmount(2325);
    expect(result).toEqual('23.25');
  });

  describe('buildBundleFormGroup', () => {
    test.each`
      hideBundleItems | expectedBundleItemsVisible
      ${undefined}    | ${true}
      ${null}         | ${true}
      ${false}        | ${true}
      ${true}         | ${false}
    `(
      `should set bundleItemsVisible to $expectedBundleItemsVisible if hideBundleItems is $hideBundleItems`,
      ({ hideBundleItems, expectedBundleItemsVisible }) => {
        const b = {
          bundleId: 'SOL-123',
          name: 'Bundle A',
          hideBundleItems: hideBundleItems,
        } as AppliedBundle;
        const fg = component.buildBundleFormGroup(b);
        expect(fg.controls['bundleId'].value).toEqual('SOL-123');
        expect(fg.controls['name'].value).toEqual('Bundle A');
        expect(fg.controls['bundleItemsVisible'].value).toEqual(expectedBundleItemsVisible);
      },
    );
  });

  describe('updateBundle', () => {
    beforeEach(() => {
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component
        .getBundlesFormArray()
        .push(component.buildBundleFormGroup({ bundleId: 'SOL-123', hideBundleItems: true } as AppliedBundle));
    });

    it('should not call updateBundle if form control is clean', () => {
      const fg = component.getBundlesFormArray().at(0) as FormGroup;
      fg.controls['bundleItemsVisible'].markAsPristine();
      component.updateBundle(0);
      expect(invoiceItemStoreServiceMock.updateBundle).not.toHaveBeenCalled();
    });

    it('should call updateBundle if form control is dirty', () => {
      const fg = component.getBundlesFormArray().at(0) as FormGroup;
      fg.controls['bundleId'].setValue('SOL-123');
      fg.controls['bundleInstanceId'].setValue('123');
      fg.controls['bundleItemsVisible'].setValue(true);
      fg.controls['bundleItemsVisible'].markAsDirty();
      component.updateBundle(0);
      expect(invoiceItemStoreServiceMock.updateBundle).toHaveBeenCalledWith(
        component.merchantId,
        component.invoiceId,
        'SOL-123',
        '123',
        false,
      );
    });
  });

  describe('bundleContainsItem', () => {
    it(`should return true when both the bundle and item have bundle instance IDs that match`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(true);
    });
    it(`should return true when both the bundle and item have bundle IDs that match`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(true);
    });
    it(`should return false when the bundle has a bundle instance ID but the item doesn't`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle doesn't have a bundle instance ID but the item does`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle and item bundle instance IDs don't match`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle and item bundle IDs don't match`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when there is no bundle`, () => {
      const item = {
        bundleId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem({} as AppliedBundle, item)).toBe(false);
    });
    it(`should return false when there is no item`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      expect(component.bundleContainsItem(bundle, {} as InvoiceItem)).toBe(false);
    });
  });

  describe('removeServicePeriod', () => {
    it(`should call through to service's update method with field mask for service_period_start and service_period_end`, () => {
      // set up component state
      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      // TODO a unit test for when canEdit is false?
      component.canEdit = true;
      const itemsArray = component.getItemsFormArray();
      const stubInvoice = stubInvoiceItem();
      stubInvoice.taxRates = []; // remove tax rates to simplify test
      const item = component.buildItemFormGroup(stubInvoice);
      itemsArray.push(item);

      // call method to remove the first / only invoice item
      component.removeServicePeriod(0);

      // assert expectations
      const expectedFieldMask = new FieldMask({
        paths: ['service_period_start', 'service_period_end'],
      });
      expect(invoiceItemStoreServiceMock.updateLineItem).toHaveBeenCalledWith(
        'partner-id',
        'invoice-id',
        'item-id',
        'sku',
        1200,
        1,
        'description',
        [],
        [],
        undefined,
        undefined,
        expectedFieldMask,
      );
    });
  });

  describe('editServicePeriod', () => {
    it(`should not call through to service's update method when dialog response is empty`, async () => {
      // set up component state
      jest.spyOn(matDialogService, 'open').mockReturnValue(emptyResponseDialogReferenceMock);

      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      const itemsArray = component.getItemsFormArray();
      const stubInvoice = stubInvoiceItem();
      stubInvoice.taxRates = []; // remove tax rates to simplify test
      const item = component.buildItemFormGroup(stubInvoice);
      itemsArray.push(item);

      // call method to edit service period
      await component.editServicePeriod(0);

      // assert expectations
      expect(invoiceItemStoreServiceMock.updateLineItem).not.toHaveBeenCalled();
    });

    it(`should call through to service's update method with field mask for service_period_start and service_period_end`, async () => {
      // set up component state
      jest.spyOn(matDialogService, 'open').mockReturnValue(servicePeriodDialogReferenceMock);

      component.merchantId = 'partner-id';
      component.invoiceId = 'invoice-id';
      component.canEdit = true;
      const itemsArray = component.getItemsFormArray();
      const stubInvoice = stubInvoiceItem();
      stubInvoice.taxRates = []; // remove tax rates to simplify test
      const item = component.buildItemFormGroup(stubInvoice);
      itemsArray.push(item);

      // call method to edit service period
      await component.editServicePeriod(0);

      // assert expectations
      const expectedFieldMask = new FieldMask({
        paths: ['service_period_start', 'service_period_end'],
      });
      expect(invoiceItemStoreServiceMock.updateLineItem).toHaveBeenCalledWith(
        'partner-id',
        'invoice-id',
        'item-id',
        'sku',
        1200,
        1,
        'description',
        [],
        [],
        servicePeriodStartDateMock,
        addDays(servicePeriodStartDateMock, 1),
        expectedFieldMask,
      );
    });
  });
});
