<form [formGroup]="formGroup" class="invoice-item-form">
  <div class="table-container">
    <div class="table-wrapper">
      <!-- Header -->
      <mat-grid-list [cols]="!config?.displayAsCreditNote ? 21 : 17" rowHeight="48px">
        <mat-grid-tile [colspan]="7" [rowspan]="1">
          <mat-grid-tile-header>
            {{ 'COMMON.PRICING.DESCRIPTION' | translate }}
          </mat-grid-tile-header>
        </mat-grid-tile>
        <mat-grid-tile [colspan]="2" [rowspan]="1">
          <mat-grid-tile-header class="flex-start">
            {{ 'COMMON.PRICING.QUANTITY' | translate }}
          </mat-grid-tile-header>
        </mat-grid-tile>
        <mat-grid-tile [colspan]="3" [rowspan]="1">
          <mat-grid-tile-header class="flex-start">
            @if (config?.displayAsCreditNote) {
              {{ 'BILLING.CREDIT_NOTE_PAGE.CREDIT_AMOUNT' | translate }}
            } @else {
              {{ 'COMMON.PRICING.UNIT_PRICE' | translate }}
            }
          </mat-grid-tile-header>
        </mat-grid-tile>
        <mat-grid-tile [colspan]="2" [rowspan]="1">
          <mat-grid-tile-header class="flex-center">
            {{ 'COMMON.PRICING.TAX' | translate }}
          </mat-grid-tile-header>
        </mat-grid-tile>
        @if (!config?.displayAsCreditNote) {
          <mat-grid-tile [colspan]="4" [rowspan]="1">
            <mat-grid-tile-header class="flex-start">
              {{ 'COMMON.PRICING.DISCOUNT' | translate }}
            </mat-grid-tile-header>
          </mat-grid-tile>
        }
        <mat-grid-tile [colspan]="2" [rowspan]="1">
          <mat-grid-tile-header class="flex-end">
            {{ 'COMMON.PRICING.AMOUNT' | translate }}
          </mat-grid-tile-header>
        </mat-grid-tile>
      </mat-grid-list>

      <ng-container *ngIf="getBundlesFormArray()?.controls as bundleControls">
        <ng-container *ngFor="let bundleControl of bundleControls; index as i">
          <!--
            DEPRECATION NOTICE: a bundle is now represented as single item on the invoice rather than it being comprised of all individual products within said bundle.
            New invoices will not be created with bundle items, however, we still need to support the legacy bundle view so that we do not
            break the historical representation of past invoices.
          -->
          <div class="bundle-grouping">
            <mat-grid-list cols="1" rowHeight="48px" class="bundle-header">
              <mat-grid-tile [colspan]="1" [rowspan]="1">
                <mat-grid-tile-header class="flex-space-between">
                  <div class="bundle-name">{{ bundleControl.value.name }}</div>
                  <div class="bundle-items-visibility" formArrayName="bundlesArray">
                    <div [formGroupName]="i">
                      <mat-slide-toggle
                        class="toggle"
                        labelPosition="before"
                        formControlName="bundleItemsVisible"
                        (change)="updateBundleItemsVisible(i)"
                        [disabled]="!canEdit"
                      >
                        <div
                          *ngIf="bundleControl.value.bundleItemsVisible === false"
                          [matTooltip]="'INVOICE_ITEM_FORM.PACKAGE_CONTENTS_HIDDEN_FROM_CUSTOMERS' | translate"
                        >
                          <mat-icon>visibility_off</mat-icon>
                          <span>
                            {{ 'INVOICE_ITEM_FORM.PACKAGE_CONTENTS_HIDDEN' | translate }}
                          </span>
                        </div>
                        <div
                          *ngIf="bundleControl.value.bundleItemsVisible === true"
                          [matTooltip]="'INVOICE_ITEM_FORM.PACKAGE_CONTENTS_VISIBLE_TO_CUSTOMERS' | translate"
                        >
                          <mat-icon>visibility</mat-icon>
                          <span>
                            {{ 'INVOICE_ITEM_FORM.PACKAGE_CONTENTS_VISIBLE' | translate }}
                          </span>
                        </div>
                      </mat-slide-toggle>
                    </div>
                  </div>
                </mat-grid-tile-header>
              </mat-grid-tile>
            </mat-grid-list>
            <ng-container *ngFor="let item of getItemsFormArray().controls; index as i">
              <!-- Load in the LineItemForm template if the item matches the bundle ID and bundle instance ID -->
              <ng-container
                *ngIf="bundleContainsItem(bundleControl.value, item?.value)"
                [ngTemplateOutlet]="LineItemForm"
                [ngTemplateOutletContext]="{ index: i, item: item }"
              ></ng-container>
            </ng-container>
          </div>

          <!-- Spacer between bundles-->
          <div
            [ngClass]="{
              'bundle-spacer': bundleControls.length > i + 1,
              'bundle-spacer-small': bundleControls.length <= i + 1,
            }"
          ></div>
        </ng-container>
      </ng-container>

      <!-- Items -->
      <ng-container *ngFor="let item of getItemsFormArray().controls; index as i">
        <!-- Only display the item if it does not have a bundle, or when viewing a credit note where bundle items are considered plain line items -->
        <ng-container *ngIf="config?.displayAsCreditNote || !item.value.bundleId; then Items"></ng-container>
        <!-- Items without a bundle -->
        <ng-template #Items>
          <!-- Load in the LineItemForm template if the item is not in a bundle-->
          <ng-container
            [ngTemplateOutlet]="LineItemForm"
            [ngTemplateOutletContext]="{ index: i, item: item }"
          ></ng-container>
        </ng-template>
      </ng-container>

      <!-- Line Item Display -->
      <ng-template #LineItemForm let-index="index" let-item="item">
        <mat-grid-list [cols]="!config?.displayAsCreditNote ? 21 : 17" formArrayName="itemsArray" rowHeight="48px">
          <ng-container [formGroupName]="index">
            <mat-grid-tile [colspan]="7" [rowspan]="1">
              <glxy-form-field size="small" bottomSpacing="none" [showLabel]="false">
                <input
                  matInput
                  #rowDescription
                  type="text"
                  formControlName="description"
                  [matAutocomplete]="autoProducts"
                  id="{{ index.toString() }}"
                  (blur)="updateDescription(index)"
                />
                <mat-autocomplete #autoProducts="matAutocomplete" [autoActiveFirstOption]="true" panelWidth="fit">
                  @if (!config?.hideAddNewProduct && item.value?.sku === '') {
                    <mat-option
                      *ngIf="rowDescription.value.length > 1"
                      (onSelectionChange)="openCreateAppWorkflowDialog(rowDescription.value, index)"
                    >
                      <mat-icon>add</mat-icon>
                      {{ 'INVOICE_ITEM_FORM.ADD_AS_NEW_PRODUCT' | translate: { productName: rowDescription.value } }}
                    </mat-option>
                    <mat-option
                      *ngIf="rowDescription.value.length > 1"
                      (onSelectionChange)="useCustomLineItem(item.value, index)"
                    >
                      <mat-icon>check</mat-icon>
                      {{ 'INVOICE_ITEM_FORM.USE_AS_LINE_ITEM' | translate: { productName: rowDescription.value } }}
                    </mat-option>
                  }
                </mat-autocomplete>
              </glxy-form-field>
            </mat-grid-tile>
            <mat-grid-tile [colspan]="2" [rowspan]="1">
              <glxy-form-field size="small" bottomSpacing="none" [showLabel]="false">
                <input matInput type="number" formControlName="quantity" (blur)="updateQuantity(index)" />
              </glxy-form-field>
            </mat-grid-tile>
            <mat-grid-tile [colspan]="3" [rowspan]="1">
              <glxy-form-field
                [size]="'small'"
                [bottomSpacing]="'none'"
                [showLabel]="false"
                [prefixText]="currencyPrefix$ | async"
              >
                <input matInput class="right-align" formControlName="amount" required (blur)="updateAmount(index)" />
              </glxy-form-field>
            </mat-grid-tile>
            <mat-grid-tile [colspan]="2" [rowspan]="1">
              {{ getTaxPercentageDisplay(index) | async }}
            </mat-grid-tile>
            @if (!config?.displayAsCreditNote) {
              <mat-grid-tile [colspan]="4" [rowspan]="1" class="text-align-left">
                <div
                  [glxyTooltip]="getFormattedDiscountsForDisplay(index) | async"
                  [tooltipPositions]="discountPopoverPositions"
                  class="discount-container"
                >
                  {{ getFormattedDiscountsForDisplay(index) | async }}
                </div>
              </mat-grid-tile>
            }
            <mat-grid-tile [colspan]="2" [rowspan]="1" class="tile-flex-end">
              <div class="row">
                @if (!config?.displayAsCreditNote && (getFormattedDiscountsForDisplay(index) | async)) {
                  <div class="strikethrough col-xs-12 ellipsis-overflow">
                    <billing-ui-simple-price-display
                      [price]="(item.value.subtotal || 0) * 100"
                      [currencyCode]="currencyCode$ | async"
                      [frequency]="BillingFrequency.ONE_TIME"
                      [alwaysShowNumber]="true"
                      [allowNegativePrice]="true"
                    ></billing-ui-simple-price-display>
                  </div>
                }
                <div class="col-xs-12 ellipsis-overflow">
                  <billing-ui-simple-price-display
                    [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
                    [price]="getSubtotalAfterDiscountForIndex(index)"
                    [currencyCode]="currencyCode$ | async"
                    [frequency]="BillingFrequency.ONE_TIME"
                    [alwaysShowNumber]="true"
                    [allowNegativePrice]="true"
                  ></billing-ui-simple-price-display>
                </div>
              </div>
            </mat-grid-tile>
            <mat-grid-tile [colspan]="1" [rowspan]="1">
              @if (canEdit) {
                <mat-menu #appMenu="matMenu">
                  @if (isInternalTaxProvider$ | async) {
                    <button mat-menu-item (click)="setItemTax(index)">
                      {{ 'INVOICE_ITEM_FORM.SET_ITEM_TAX' | translate }}
                    </button>
                  }
                  @if (!config?.displayAsCreditNote && getAppliedDiscountsAtIndex(index).length <= 0) {
                    <button mat-menu-item (click)="addDiscount(index)">
                      {{ 'INVOICE_ITEM_FORM.ADD_DISCOUNT' | translate }}
                    </button>
                  }
                  @if (!config?.displayAsCreditNote && getAppliedDiscountsAtIndex(index)?.length > 0) {
                    <button mat-menu-item (click)="editDiscount(index)">
                      {{ 'INVOICE_ITEM_FORM.EDIT_DISCOUNT' | translate }}
                    </button>
                  }
                  @if (hasServicePeriodAccess && !config?.displayAsCreditNote) {
                    <button mat-menu-item (click)="editServicePeriod(index)">
                      @if (item.value?.servicePeriodStart || item.value?.servicePeriodEnd) {
                        {{ 'INVOICE_ITEM_FORM.EDIT_SERVICE_PERIOD' | translate }}
                      } @else {
                        {{ 'INVOICE_ITEM_FORM.ADD_SERVICE_PERIOD' | translate }}
                      }
                    </button>
                  }
                  <mat-divider></mat-divider>
                  @if (!config?.displayAsCreditNote && getAppliedDiscountsAtIndex(index)?.length > 0) {
                    <button mat-menu-item (click)="removeDiscount(index)">
                      {{ 'INVOICE_ITEM_FORM.REMOVE_DISCOUNT' | translate }}
                    </button>
                  }
                  @if (
                    hasServicePeriodAccess &&
                    !config?.displayAsCreditNote &&
                    (item.value?.servicePeriodStart || item.value?.servicePeriodEnd)
                  ) {
                    <button mat-menu-item (click)="removeServicePeriod(index)">
                      {{ 'INVOICE_ITEM_FORM.REMOVE_SERVICE_PERIOD' | translate }}
                    </button>
                  }
                  <button mat-menu-item (click)="deleteItem(index)">
                    {{ 'INVOICE_ITEM_FORM.REMOVE_ITEM' | translate }}
                  </button>
                </mat-menu>
                <button mat-icon-button [matMenuTriggerFor]="appMenu">
                  <mat-icon>more_vert</mat-icon>
                </button>
              } @else {
                @if (hasServicePeriodAccess && !config?.displayAsCreditNote) {
                  <mat-menu #appMenu="matMenu">
                    <button mat-menu-item (click)="editServicePeriod(index)">
                      @if (item.value?.servicePeriodStart || item.value?.servicePeriodEnd) {
                        {{ 'INVOICE_ITEM_FORM.EDIT_SERVICE_PERIOD' | translate }}
                      } @else {
                        {{ 'INVOICE_ITEM_FORM.ADD_SERVICE_PERIOD' | translate }}
                      }
                    </button>
                    @if (
                      hasServicePeriodAccess &&
                      !config?.displayAsCreditNote &&
                      (item.value?.servicePeriodStart || item.value?.servicePeriodEnd)
                    ) {
                      <!-- NOTE: this divider should be moved up from this scope when there are more than just 1 'removal' menu options -->
                      <mat-divider></mat-divider>
                      <button mat-menu-item (click)="removeServicePeriod(index)">
                        {{ 'INVOICE_ITEM_FORM.REMOVE_SERVICE_PERIOD' | translate }}
                      </button>
                    }
                  </mat-menu>
                  <button mat-icon-button [matMenuTriggerFor]="appMenu">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                }
              }
            </mat-grid-tile>
          </ng-container>
        </mat-grid-list>
        @if (
          hasServicePeriodAccess &&
          !config?.displayAsCreditNote &&
          item.value?.servicePeriodStart &&
          item.value?.servicePeriodEnd
        ) {
          <div class="service-period-date-display">
            <mat-icon>date_range</mat-icon>
            <p>
              {{ item.value.servicePeriodStart | date: 'mediumDate' }} -
              {{ item.value.servicePeriodEnd | date: 'mediumDate' }}
            </p>
          </div>
        }
      </ng-template>
    </div>
  </div>
  <!-- Add Line Item -->
  @if (canEdit) {
    <div class="row row-gutters">
      <div class="col col-xs-12 col-sm-6">
        <button type="button" color="primary" mat-stroked-button class="action-button" (click)="addItem()">
          {{ 'INVOICE_ITEM_FORM.ADD_ITEM' | translate }}
        </button>
      </div>
      <div class="col col-xs-12 col-sm-6">
        <button type="button" color="primary" mat-stroked-button class="action-button" (click)="addCustomItem()">
          {{ 'INVOICE_ITEM_FORM.ADD_CUSTOM_ITEM' | translate }}
        </button>
      </div>
    </div>
  }

  <!-- Footer -->
  <div class="footer-divider-wrapper">
    <mat-divider></mat-divider>
  </div>
  <div class="row">
    <div class="col col-xs-12 col-sm-6"></div>
    <div class="col col-xs-12 col-sm-6">
      @if (config?.displayAsCreditNote) {
        <!-- Initial amount (for credit notes) -->
        <div class="total-row">
          <div>{{ 'BILLING.CREDIT_NOTE_PAGE.SUMMARY.INVOICE_TOTAL' | translate }}</div>
          <div class="total-row-value">
            @if (subtotal$ | async) {
              <billing-ui-simple-price-display
                [price]="config?.initialTotal || 0"
                [currencyCode]="currencyCode$ | async"
                [frequency]="BillingFrequency.ONE_TIME"
                [alwaysShowNumber]="true"
              ></billing-ui-simple-price-display>
            }
          </div>
        </div>
      }

      <!-- Subtotal -->
      <div class="total-row">
        <div>
          @if (config?.displayAsCreditNote) {
            {{ 'BILLING.CREDIT_NOTE_PAGE.SUMMARY.SUBTOTAL' | translate }}
          } @else {
            {{ 'COMMON.PRICING.SUBTOTAL' | translate }}
          }
        </div>
        <div class="total-row-value">
          @if (subtotal$ | async; as subtotal) {
            <billing-ui-simple-price-display
              [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
              [price]="(+subtotal || 0) * 100"
              [currencyCode]="currencyCode$ | async"
              [frequency]="BillingFrequency.ONE_TIME"
              [alwaysShowNumber]="true"
            ></billing-ui-simple-price-display>
          }
        </div>
      </div>

      <!-- Discounts -->
      @if (!config?.displayAsCreditNote) {
        @if (discountTotal$ | async; as dt) {
          <div class="total-row">
            <div>{{ 'COMMON.PRICING.DISCOUNTS' | translate }}</div>
            <div class="total-row-value">
              @if (subtotal$ | async; as subtotal) {
                <billing-ui-simple-price-display
                  [price]="(+dt || 0) * 100 * -1"
                  [currencyCode]="currencyCode$ | async"
                  [frequency]="BillingFrequency.ONE_TIME"
                  [alwaysShowNumber]="true"
                  [allowNegativePrice]="true"
                ></billing-ui-simple-price-display>
              }
            </div>
          </div>
        }
      }

      <!-- Taxes -->
      <div class="total-row" *ngFor="let tax of appliedTaxes$ | async">
        <div>
          <span class="left-align">
            {{ getTaxDisplayDescription(tax.taxRateId || '') | async }}
          </span>
        </div>
        <div class="total-row-value">
          @if (subtotal$ | async) {
            <billing-ui-simple-price-display
              [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
              [price]="tax.amount || 0"
              [currencyCode]="currencyCode$ | async"
              [frequency]="BillingFrequency.ONE_TIME"
              [alwaysShowNumber]="true"
            ></billing-ui-simple-price-display>
          }
        </div>
      </div>

      <!-- Credit notes - Pre Payment -->
      <div class="total-row" *ngFor="let creditNote of prePaymentAppliedCreditNotes$ | async">
        <div>
          <span class="left-align"
            >{{ 'INVOICE_ITEM_FORM.CREDITS_APPLIED' | translate }} (<a
              [routerLink]="'/billing/credit-note/' + creditNote.creditNoteId"
            >
              {{ creditNote.number }} </a
            >)
          </span>
        </div>
        <div class="total-row-value">
          @if (subtotal$ | async) {
            <billing-ui-simple-price-display
              [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
              [price]="(creditNote.amount || 0) * -1"
              [currencyCode]="currencyCode$ | async"
              [frequency]="BillingFrequency.ONE_TIME"
              [alwaysShowNumber]="true"
              [allowNegativePrice]="true"
            ></billing-ui-simple-price-display>
          }
        </div>
      </div>

      <!-- Default Taxes -->
      @if (canEdit && !config?.displayAsCreditNote && (isInternalTaxProvider$ | async)) {
        <div class="total-row">
          <div>
            <a (click)="updateDefaultTaxes()">
              {{ 'INVOICE_ITEM_FORM.UPDATE_DEFAULT_TAX' | translate }}
            </a>
          </div>
        </div>
      }

      <div class="footer-divider-wrapper">
        <mat-divider></mat-divider>
      </div>

      <!-- Total -->
      @if (config?.displayAsCreditNote) {
        <div class="total-row">
          <div>
            {{ 'BILLING.CREDIT_NOTE_PAGE.SUMMARY.TOTAL' | translate }}
          </div>
          <div class="total-row-value">
            @if (subtotal$ | async; as subtotal) {
              @if (total$ | async; as total) {
                <billing-ui-simple-price-display
                  [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
                  [price]="(+total || 0) * 100"
                  [currencyCode]="currencyCode$ | async"
                  [frequency]="BillingFrequency.ONE_TIME"
                  [alwaysShowNumber]="true"
                ></billing-ui-simple-price-display>
              }
            }
          </div>
        </div>
        @if (config.initialTotal < convertDollarsToCents(parseFloat(total$ | async)) && this.canEdit) {
          <div class="invalid">
            {{ 'BILLING.CREDIT_NOTE_PAGE.TOTAL_EXCEEDS_INVOICE_AMOUNT_INFO' | translate }}
          </div>
        }
        @if (convertDollarsToCents(parseFloat(total$ | async)) === 0 && this.canEdit) {
          <div class="invalid">
            {{ 'BILLING.CREDIT_NOTE_PAGE.CREDIT_TOTAL_MINIMUM_INFO' | translate }}
          </div>
        }
      } @else {
        <div class="total-row">
          <div>
            @if ((isInternalTaxProvider$ | async) || ((appliedTaxes$ | async)?.length ?? 0) > 0) {
              <strong>{{ 'INVOICE_ITEM_FORM.AMOUNT_DUE' | translate }}</strong>
            } @else {
              <strong>{{ 'INVOICE_ITEM_FORM.AMOUNT_DUE_BEFORE_TAX' | translate }}</strong>
            }
          </div>
          <div class="total-row-value">
            @if (subtotal$ | async; as subtotal) {
              @if (amountOwing$ | async; as amountOwing) {
                <billing-ui-simple-price-display
                  [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
                  [price]="(+amountOwing || 0) * 100"
                  [currencyCode]="currencyCode$ | async"
                  [frequency]="BillingFrequency.ONE_TIME"
                  [alwaysShowNumber]="true"
                ></billing-ui-simple-price-display>
              }
            }
          </div>
        </div>
      }

      <div class="footer-divider-wrapper">
        <mat-divider></mat-divider>
      </div>

      <!-- Credit notes - Post Payment -->
      <div class="total-row" *ngFor="let creditNote of postPaymentAppliedCreditNotes$ | async">
        <div>
          <span class="left-align">
            {{ 'BILLING.CREDIT_NOTE_PAGE.SUMMARY.REFUND' | translate }}
            (<a [routerLink]="'/billing/credit-note/' + creditNote.creditNoteId"> {{ creditNote.number }} </a>)
          </span>
        </div>
        <div class="total-row-value">
          @if (subtotal$ | async) {
            <billing-ui-simple-price-display
              [billingParenthesesWrapper]="!!config?.displayAsCreditNote"
              [price]="(creditNote.amount || 0) * -1"
              [currencyCode]="currencyCode$ | async"
              [frequency]="BillingFrequency.ONE_TIME"
              [alwaysShowNumber]="true"
              [allowNegativePrice]="true"
            ></billing-ui-simple-price-display>
          }
        </div>
      </div>
    </div>
  </div>
</form>
