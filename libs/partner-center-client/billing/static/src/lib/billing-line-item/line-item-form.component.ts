import { CommonModule, getCurrencySymbol } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  AppliedBundle,
  AppliedCreditNote as InvoiceAppliedCreditNote,
  AppliedDiscount,
  AppliedTaxRate,
  CreditNoteType,
  Currency,
  DiscountType,
  InvoiceItem,
  TaxRate,
  TaxRateService,
} from '@galaxy/billing';
import { TranslateService } from '@galaxy/lexicon';
import { ItemType } from '@galaxy/marketplace-apps';
import { TranslateModule } from '@ngx-translate/core';
import { Frequency, TaxProviderType } from '@vendasta/billing';
import {
  BillingUiModule,
  DiscountDialogInfo,
  DiscountForRetailDialog,
  DiscountFromRetailDialog,
  DiscountPipe,
  OnSaveResponse,
  RetailDiscountDialogComponent,
} from '@vendasta/billing-ui';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Frequency as BillingFrequency } from '@vendasta/galaxy/frequency';
import { Package, PackageService } from '@vendasta/marketplace-packages';
import { combineLatest, firstValueFrom, Observable, Subject, Subscription } from 'rxjs';
import { delay, map, shareReplay, startWith, switchMap, take, tap } from 'rxjs/operators';
import { AddTaxRateDialogComponent } from './add-tax-rate-dialog/add-tax-rate-dialog.component';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { MatInputModule } from '@angular/material/input';
import {
  CommonLineItemStoreService,
  formDataToInvoiceItem,
  getSubtotalAfterDiscountForItem,
} from './common-line-item-store.service';
import { ItemSelectorDialogComponent, SubscriptionItem } from './item-selector-dialog/item-selector-dialog.component';
import { ParenthesesWrapperDirective } from './parentheses-wrapper.directive';
import { RouterLink } from '@angular/router';
import { convertDollarsToCents } from './../subscriptions/utils';
import {
  EditServicePeriodData,
  EditServicePeriodDialogComponent,
  ServicePeriod,
} from './edit-service-period-dialog/edit-service-period-dialog.component';
import { FieldMask } from '@galaxy/marketplace-apps/v1';

export interface LineItem {
  id?: string;
  sku: string;
  amount: number;
  quantity: number;
  description: string;
  subtotal?: number;
  total?: number;
  appliedTaxes?: AppliedTaxRate[];
  appliedDiscounts?: AppliedDiscount[];
  taxRates?: TaxRate[];
  bundleId?: string;
  bundleInstanceId?: string;
  servicePeriodStart?: Date;
  servicePeriodEnd?: Date;
}

export interface LineItemSummaryContainer {
  id?: string;
  defaultTaxRates: TaxRate[];
  items: LineItem[];
  amountOwing: number;
  total: number;
  subtotal: number;
  discountTotal?: number;
  appliedTaxes: AppliedTaxRate[];
  appliedCreditNotes?: InvoiceAppliedCreditNote[];
  currency: Currency;
  appliedBundles?: AppliedBundle[];
}

export interface LineItemFormData {
  description: string;
  quantity: number;
  amount: number;
  subtotal: number;
  total: number;
  sku: string;
  id: string;
  taxRates: TaxRate[];
  appliedDiscounts: AppliedDiscount[];
  appId: string;
  editionId: string;
  servicePeriodStart?: Date;
  servicePeriodEnd?: Date;
}

export interface LineItemConfig {
  hideAddNewProduct: boolean;
  displayAsCreditNote: boolean;
  initialTotal: number;
}

@Component({
  selector: 'billing-line-item-form',
  templateUrl: './line-item-form.component.html',
  styleUrls: ['./line-item-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatButtonModule,
    MatGridListModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    MatSlideToggleModule,
    MatTooltipModule,
    BillingUiModule,
    GalaxyFormFieldModule,
    TranslateModule,
    GalaxyTooltipModule,
    MatInputModule,
    ParenthesesWrapperDirective,
    RouterLink,
  ],
})
export class LineItemFormComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;
  total$!: Observable<string>;
  amountOwing$!: Observable<string>;
  subtotal$!: Observable<string>;
  discountTotal$!: Observable<string>;
  appliedTaxes$!: Observable<AppliedTaxRate[]>;
  prePaymentAppliedCreditNotes$!: Observable<InvoiceAppliedCreditNote[]>;
  postPaymentAppliedCreditNotes$!: Observable<InvoiceAppliedCreditNote[]>;
  taxRates$!: Observable<TaxRate[]>;
  subscriptions: Subscription[] = [];
  currencyCode$!: Observable<string>;
  currencyPrefix$!: Observable<string>;
  bundles$!: Observable<AppliedBundle[]>;
  focusNewestItem = false;
  isInternalTaxProvider$!: Observable<boolean>;

  discountPopoverPositions = [{ ...PopoverPositions['Top'] }, { ...PopoverPositions['Bottom'] }];

  delayedUpdate$$ = new Subject<LineItem[]>();

  @Input() merchantId!: string;
  @Input() marketId!: string;
  @Input() customerId!: string;
  @Input() invoiceId!: string;
  @Input() canEdit!: boolean;
  @Input() isTemplate = false;
  @Input() hasPackageRetailPriceAccess = false;
  @Input() hasServicePeriodAccess = false;
  @Input() includeSetupFees = true;
  @Input() config: LineItemConfig = {
    hideAddNewProduct: false,
    displayAsCreditNote: false,
    initialTotal: 0,
  };
  @Input() creditNoteId?: string;

  @Output() itemsChange: EventEmitter<LineItem[]> = new EventEmitter<LineItem[]>();

  constructor(
    private formBuilder: UntypedFormBuilder,
    private lineItemStoreService: CommonLineItemStoreService,
    private readonly packageService: PackageService,
    private readonly taxRateService: TaxRateService,
    public dialog: MatDialog,
    private readonly translateService: TranslateService,
  ) {
    this.formGroup = this.formBuilder.group({
      itemsArray: this.formBuilder.array([]),
      bundlesArray: this.formBuilder.array([]),
    });
  }

  private static patchItemFormGroup(existingFormGroup: UntypedFormGroup, item: LineItem): void {
    existingFormGroup.controls['id'].setValue(item.id);
    existingFormGroup.controls['sku'].setValue(item.sku);
    existingFormGroup.controls['description'].setValue(item.description);
    existingFormGroup.controls['quantity'].setValue(item.quantity);
    existingFormGroup.controls['amount'].setValue(((item.amount || 0) / 100).toFixed(2));
    existingFormGroup.controls['subtotal'].setValue(((item.subtotal || 0) / 100).toFixed(2));
    existingFormGroup.controls['total'].setValue(((item.total || 0) / 100).toFixed(2));
    existingFormGroup.controls['taxRates'].setValue(item.taxRates);
    existingFormGroup.controls['appliedDiscounts'].setValue(item.appliedDiscounts);
    existingFormGroup.controls['servicePeriodStart'].setValue(item.servicePeriodStart);
    existingFormGroup.controls['servicePeriodEnd'].setValue(item.servicePeriodEnd);
  }

  private static patchBundleFormGroup(existingFormGroup: UntypedFormGroup, bundle: AppliedBundle): void {
    existingFormGroup.controls['bundleId'].setValue(bundle.bundleId);
    existingFormGroup.controls['bundleInstanceId'].setValue(bundle.bundleInstanceId);
    existingFormGroup.controls['name'].setValue(bundle.name);
    existingFormGroup.controls['bundleItemsVisible'].setValue(!bundle.hideBundleItems);
  }

  ngOnInit(): void {
    //initialize the stores
    this.lineItemStoreService.init(this.merchantId, this.customerId, this.invoiceId, this.creditNoteId ?? '');

    this.subscriptions.push(
      this.lineItemStoreService.items$.subscribe((items) => {
        const itemsArray = this.getItemsFormArray();

        // clear out form control as its out of sync
        if (items.length < itemsArray.length) {
          itemsArray.clear();
        }

        // add new items or update another
        for (let i = 0; i < items?.length; i++) {
          const existingFormGroup = itemsArray?.at(i) as UntypedFormGroup;
          if (existingFormGroup === undefined) {
            itemsArray.push(this.buildItemFormGroup(items[i]));
          } else {
            LineItemFormComponent.patchItemFormGroup(existingFormGroup, items[i]);
          }
        }
        this.delayedUpdate$$.next(items);
        this.itemsChange.emit(items);
      }),
    );

    this.subscriptions.push(
      this.lineItemStoreService.bundles$.subscribe((bundles) => {
        const bundlesArray = this.getBundlesFormArray();

        // clear out form control as its out of sync
        if (bundles.length < bundlesArray.length) {
          bundlesArray.clear();
        }

        // add new items or update another
        for (let i = 0; i < bundles?.length; i++) {
          const existingFormGroup = bundlesArray?.at(i) as UntypedFormGroup;
          if (existingFormGroup === undefined) {
            bundlesArray.push(this.buildBundleFormGroup(bundles[i]));
          } else {
            LineItemFormComponent.patchBundleFormGroup(existingFormGroup, bundles[i]);
          }
        }
      }),
    );

    this.subscriptions.push(
      this.delayedUpdate$$
        .pipe(
          delay(200),
          tap((items: LineItem[]) => {
            // if the user just added a new item, focus it by it's element id.
            if (this.focusNewestItem) {
              const id: string = (items?.length - 1).toString();
              document?.getElementById(id)?.focus();
              this.focusNewestItem = false;
            }
          }),
        )
        .subscribe(),
    );

    this.isInternalTaxProvider$ = this.taxRateService.getTaxProvider(this.merchantId).pipe(
      map((p) => p?.taxProviderType === TaxProviderType.TAX_PROVIDER_TYPE_INTERNAL),
      startWith(false),
      shareReplay(1),
    );

    this.currencyCode$ = this.lineItemStoreService.currency$.pipe(map((c) => String(c)));

    this.currencyPrefix$ = this.lineItemStoreService.currency$.pipe(
      take(1),
      map((currency) => getCurrencySymbol(currency, 'wide')),
      shareReplay(1),
    );

    this.total$ = this.lineItemStoreService.total$.pipe(map((total) => total.toFixed(2)));
    this.amountOwing$ = this.lineItemStoreService.amountOwing$.pipe(map((amountOwing) => amountOwing.toFixed(2)));
    this.discountTotal$ = this.lineItemStoreService.discountTotal$.pipe(
      map((discountTotal) => {
        if (discountTotal <= 0) {
          return '';
        }
        return discountTotal.toFixed(2);
      }),
    );

    this.subtotal$ = this.lineItemStoreService.subtotal$.pipe(map((subtotal) => subtotal.toFixed(2)));

    this.appliedTaxes$ = this.lineItemStoreService.appliedTaxes$;

    this.prePaymentAppliedCreditNotes$ = this.lineItemStoreService.appliedCreditNotes$.pipe(
      map((creditNotes) => creditNotes?.filter(({ creditNoteType }) => creditNoteType === CreditNoteType.PRE_PAYMENT)),
    );

    this.postPaymentAppliedCreditNotes$ = this.lineItemStoreService.appliedCreditNotes$.pipe(
      map((creditNotes) => creditNotes?.filter(({ creditNoteType }) => creditNoteType === CreditNoteType.POST_PAYMENT)),
    );

    this.taxRates$ = this.lineItemStoreService.taxRates$;

    this.bundles$ = this.lineItemStoreService.bundles$;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  public getItemsFormArray(): UntypedFormArray {
    return this.formGroup.get('itemsArray') as UntypedFormArray;
  }

  public getBundlesFormArray(): UntypedFormArray {
    return this.formGroup.get('bundlesArray') as UntypedFormArray;
  }

  public addItem(): void {
    this.subscriptions.push(
      this.dialog
        .open(ItemSelectorDialogComponent, {
          width: '60%',
          minHeight: '572px',
          data: {
            partnerId: this.merchantId,
            marketId: this.marketId,
            customerId: this.customerId,
            title: this.translateService.instant('INVOICE_ITEM_FORM.SELECT_ITEMS'),
          },
        })
        .afterClosed()
        .pipe(take(1))
        .subscribe((subscriptionItems) => {
          if (subscriptionItems?.length === 0) {
            return;
          }
          subscriptionItems.map((item: SubscriptionItem) => {
            if (item.itemType === ItemType.ITEM_TYPE_PACKAGE && !this.hasPackageRetailPriceAccess) {
              this.addBundle(item.sku);
              return;
            }
            this.lineItemStoreService.addLineItem(
              this.merchantId,
              this.invoiceId,
              item.sku,
              item.amount,
              item.quantity,
              item.name,
              [],
            );

            if (item.setupFee > 0 && this.includeSetupFees) {
              // include setup fee as a separate line item
              const feeDescription = `${item.name} ${this.translateService.instant(
                'INVOICE_ITEM_FORM.SETUP_FEE_SUFFIX',
              )}`;
              this.lineItemStoreService.addLineItem(
                this.merchantId,
                this.invoiceId,
                item.sku,
                item.setupFee,
                item.quantity,
                feeDescription,
                [],
              );
            }
          });
          this.focusNewestItem = true;
        }),
    );
  }

  public addCustomItem(): void {
    this.focusNewestItem = true;
    this.lineItemStoreService.addLineItem(this.merchantId, this.invoiceId, '', 0, 1, '', []);
  }

  public deleteItem(index: number): void {
    this.lineItemStoreService.deleteLineItem(this.merchantId, this.invoiceId, index);
  }

  updateAmount(index: number): void {
    const formGroup: UntypedFormGroup = this.getItemsFormArray()?.at(index) as UntypedFormGroup;
    if (formGroup.controls['amount'].dirty) {
      this.updateItem(index);
    }
  }

  updateQuantity(index: number): void {
    const formGroup: UntypedFormGroup = this.getItemsFormArray()?.at(index) as UntypedFormGroup;
    if (formGroup.controls['quantity'].dirty) {
      if (this.isTemplate && formGroup.controls['quantity'].invalid) {
        const initialValues = formGroup.value;
        initialValues.quantity = 1;
        formGroup.reset(initialValues);
        this.updateItem(index);
      } else {
        this.updateItem(index);
      }
    }
  }

  public updateItem(index: number): void {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    this.lineItemStoreService.updateLineItem(
      this.merchantId,
      this.invoiceId,
      item.id || '',
      item.sku,
      item.amount,
      item.quantity,
      item.description,
      item.taxRates || [],
      item.appliedDiscounts || [],
      item.servicePeriodStart,
      item.servicePeriodEnd,
    );
  }

  public updateBundle(index: number): void {
    const formGroup: UntypedFormGroup = this.getBundlesFormArray()?.at(index) as UntypedFormGroup;
    if (formGroup.controls['bundleItemsVisible'].dirty) {
      const bundleId = formGroup.get('bundleId')?.value;
      const bundleInstanceId = formGroup.get('bundleInstanceId')?.value;
      const bundleItemsVisible = formGroup.get('bundleItemsVisible')?.value as boolean;
      this.lineItemStoreService.updateBundle(
        this.merchantId,
        this.invoiceId,
        bundleId,
        bundleInstanceId,
        !bundleItemsVisible,
      );
    }
  }

  public updateDescription(index: number): void {
    const formGroup: UntypedFormGroup = this.getItemsFormArray()?.at(index) as UntypedFormGroup;
    if (formGroup.controls['description'].dirty) {
      this.updateItem(index);
    }
  }

  public useCustomLineItem(value: LineItemFormData, index: number): void {
    const formGroup: UntypedFormGroup = this.getItemsFormArray()?.at(index) as UntypedFormGroup;
    formGroup.controls['sku'].setValue('');
    formGroup.controls['amount'].setValue(0);
    this.updateItem(index);
  }

  public setItemTax(index: number): void {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    this.subscriptions.push(
      this.dialog
        .open(AddTaxRateDialogComponent, {
          height: '300px',
          width: '600px',
          data: {
            merchantId: this.merchantId,
            taxRates: item.taxRates,
            action: 'Set item tax',
          },
        })
        .afterClosed()
        .subscribe((taxRates) => {
          if (taxRates) {
            this.lineItemStoreService.updateLineItem(
              this.merchantId,
              this.invoiceId,
              item.id || '',
              item.sku,
              item.amount,
              item.quantity,
              item.description,
              taxRates,
              item.appliedDiscounts || [],
              item.servicePeriodStart,
              item.servicePeriodEnd,
            );
          }
        }),
    );
  }

  public async editServicePeriod(index: number): Promise<void> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    const servicePeriod: ServicePeriod = await firstValueFrom(
      this.dialog
        .open(EditServicePeriodDialogComponent, {
          width: '600px',
          data: {
            itemName: item.description,
            servicePeriod: {
              start: item.servicePeriodStart || null,
              end: item.servicePeriodEnd || null,
            },
          } as EditServicePeriodData,
        })
        .afterClosed(),
    );

    if (!servicePeriod) {
      return;
    }

    const fieldMask = new FieldMask({
      paths: ['service_period_start', 'service_period_end'],
    });
    this.lineItemStoreService.updateLineItem(
      this.merchantId,
      this.invoiceId,
      item.id || '',
      item.sku,
      item.amount,
      item.quantity,
      item.description,
      item.taxRates || [],
      item.appliedDiscounts || [],
      servicePeriod.start,
      servicePeriod.end,
      fieldMask,
    );
  }

  public removeServicePeriod(index: number): void {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    const fieldMask = new FieldMask({
      paths: ['service_period_start', 'service_period_end'],
    });
    this.lineItemStoreService.updateLineItem(
      this.merchantId,
      this.invoiceId,
      item.id || '',
      item.sku,
      item.amount,
      item.quantity,
      item.description,
      item.taxRates || [],
      item.appliedDiscounts || [],
      undefined,
      undefined,
      fieldMask,
    );
  }

  public updateDefaultTaxes(): void {
    this.subscriptions.push(
      this.lineItemStoreService.defaultTaxRates$
        .pipe(
          take(1),
          switchMap((taxRates) => {
            return this.dialog
              .open(AddTaxRateDialogComponent, {
                height: '300px',
                width: '600px',
                data: {
                  merchantId: this.merchantId,
                  taxRates: taxRates,
                  action: 'Update default tax',
                },
              })
              .afterClosed();
          }),
        )
        .subscribe((taxRates) => {
          if (taxRates) {
            this.lineItemStoreService.updateDefaultTaxes(this.merchantId, this.invoiceId, taxRates);
          }
        }),
    );
  }

  public updateBundleItemsVisible(index: number): void {
    this.updateBundle(index);
  }

  getFormattedDiscountsForDisplay(index: number): Observable<string> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    return combineLatest([this.lineItemStoreService.currency$, this.lineItemStoreService.items$]).pipe(
      map(([currency, items]) => {
        const lineItem = this.isTemplate ? items[index] : items.find((i) => i.id === item.id);
        const discounts = lineItem?.appliedDiscounts || [];
        return discounts
          .filter((d) => d.totalAmount !== 0)
          .map((d) => {
            let display = d.description;
            if (!display) {
              display = new DiscountPipe().transform(d.value, DiscountType[d.type], currency);
              display = `- ${display}`;
            }
            return display;
          })
          .join(', ');
      }),
    );
  }

  getSubtotalAfterDiscountForIndex(index: number): number {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    return getSubtotalAfterDiscountForItem(item);
  }

  private doesTaxExistOnItem(item: LineItem, taxId: string): boolean {
    return !!(item && item.taxRates && item.taxRates.some((itemRate) => taxId === itemRate.id));
  }

  private taxesToPercentageDisplay(taxRates: TaxRate[] | AppliedTaxRate[]): string {
    if (!taxRates) {
      return '';
    }
    return taxRates.map((tr) => `${tr.percentage}%`).join(' + ');
  }

  getTaxPercentageDisplay(index: number): Observable<string> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    return this.appliedTaxes$.pipe(
      map((taxRates) =>
        this.taxesToPercentageDisplay(
          taxRates.filter((taxRate) => this.doesTaxExistOnItem(item, taxRate.taxRateId || '')),
        ),
      ),
    );
  }

  private taxToDescriptionDisplay(tax: TaxRate | AppliedTaxRate | undefined) {
    if (!tax) {
      return '';
    }
    return tax.description
      ? `${tax.name} - ${tax.description} (${tax.percentage}%)`
      : `${tax.name} (${tax.percentage}%)`;
  }

  getTaxDisplayDescription(taxRateId: string): Observable<string> {
    return this.appliedTaxes$.pipe(
      map((taxRates) => {
        return this.taxToDescriptionDisplay(taxRates.find((a) => a.taxRateId === taxRateId));
      }),
    );
  }

  formatTaxAmount(amount: number): string {
    return (amount / 100).toFixed(2);
  }

  getAppliedDiscountsAtIndex(index: number): AppliedDiscount[] {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    return item.appliedDiscounts || [];
  }

  async addDiscount(index: number): Promise<unknown> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    const discount: DiscountForRetailDialog = {
      id: '',
      merchantId: this.merchantId,
      SKU: item.sku,
      customerId: this.customerId,
    };
    return this.openDiscountDialog(index, discount, 'create');
  }

  async editDiscount(index: number): Promise<unknown> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    const existingDiscount = item.appliedDiscounts?.at(0);
    if (!existingDiscount) {
      return this.addDiscount(index);
    }
    const discount: DiscountForRetailDialog = {
      id: existingDiscount.referenceId,
      merchantId: this.merchantId,
      SKU: item.sku,
      customerId: this.customerId,
      discountType: existingDiscount.type,
      amount: existingDiscount.value,
      description: existingDiscount.description,
    };
    return this.openDiscountDialog(index, discount, 'edit');
  }

  async removeDiscount(index: number): Promise<unknown> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    return this.lineItemStoreService.updateLineItem(
      this.merchantId,
      this.invoiceId,
      item.id || '',
      item.sku,
      item.amount,
      item.quantity,
      item.description,
      item.taxRates || [],
      [],
      item.servicePeriodStart,
      item.servicePeriodEnd,
    );
  }

  private async openDiscountDialog(
    index: number,
    discount: DiscountForRetailDialog,
    mode: 'create' | 'edit',
  ): Promise<unknown> {
    const item = formDataToInvoiceItem(this.getItemsFormArray()?.at(index).value);
    const currencyCode = await firstValueFrom(this.currencyCode$);
    const dialogData: DiscountDialogInfo = {
      currencyCode,
      discount,
      mode,
      hideDates: true,
      onSave: (discount: DiscountFromRetailDialog, _): OnSaveResponse => {
        const appliedDiscount: AppliedDiscount = {
          description: discount.description || '',
          totalAmount: 0, // set on server
          type: discount.discountType,
          value: discount.amount,
          referenceId: discount.id,
        };
        this.lineItemStoreService.updateLineItem(
          this.merchantId,
          this.invoiceId,
          item.id || '',
          item.sku,
          item.amount,
          item.quantity,
          item.description,
          item.taxRates || [],
          [appliedDiscount],
          item.servicePeriodStart,
          item.servicePeriodEnd,
        );
        return Promise.resolve({ success: true, discount: discount });
      },
    };
    return firstValueFrom(
      this.dialog
        .open(RetailDiscountDialogComponent, {
          width: '450px',
          data: dialogData,
        })
        .afterClosed(),
    );
  }

  public buildItemFormGroup(data: LineItem): UntypedFormGroup {
    const itemFormGroup = this.formBuilder.group({
      id: [data.id || ''],
      sku: [data.sku || ''],
      description: [{ value: data.description || '', disabled: !this.canEdit }],
      quantity: [
        {
          value: data.quantity || 1,
          disabled: !this.canEdit,
        },
        [Validators.required, this.greaterThanZeroValidator()],
      ],
      amount: [
        {
          value: ((data.amount || 0) / 100).toFixed(2),
          disabled: !this.canEdit,
        },
      ],
      subtotal: [((data.subtotal || 0) / 100).toFixed(2)],
      total: [((data.total || 0) / 100).toFixed(2)],
      taxRates: [data.taxRates || []],
      appliedDiscounts: [data.appliedDiscounts || []],
      bundleId: [data.bundleId || ''],
      bundleInstanceId: [data.bundleInstanceId || ''],
      servicePeriodStart: [data.servicePeriodStart || null],
      servicePeriodEnd: [data.servicePeriodEnd || null],
    });
    return itemFormGroup;
  }

  public buildBundleFormGroup(data: AppliedBundle): UntypedFormGroup {
    return this.formBuilder.group({
      bundleId: [data.bundleId || ''],
      bundleInstanceId: [data.bundleInstanceId || ''],
      name: [data.name || ''],
      bundleItemsVisible: [!data.hideBundleItems],
    });
  }

  private greaterThanZeroValidator = () => {
    return (control: UntypedFormControl) => {
      if (control.value <= 0) {
        return { isZeroOrLess: true };
      }
      return null;
    };
  };

  public addBundle(bundleId: string): void {
    if (bundleId) {
      this.subscriptions.push(
        this.packageService
          .get(bundleId)
          .pipe(take(1))
          .subscribe((pkg: Package) => {
            if (pkg.usesBillingPricing) {
              this.lineItemStoreService.addBundle(this.merchantId, this.invoiceId, bundleId);
            } else {
              // there is no bundle in billing, add "legacy package" as single line item
              this.lineItemStoreService.addLineItem(
                this.merchantId,
                this.invoiceId,
                pkg.packageId,
                pkg.pricing?.prices[0]?.price > 0 ? pkg.pricing?.prices[0]?.price : 0,
                1,
                pkg.name,
                [],
              );
            }
          }),
      );
    }
  }

  public openCreateAppWorkflowDialog(appName: string, index: number): void {
    this.subscriptions.push(
      this.lineItemStoreService.createNewApp(appName).subscribe((app) => {
        if (app) {
          // update the line item from the added app and refresh the marketplace data
          const formGroup: UntypedFormGroup = this.getItemsFormArray()?.at(index) as UntypedFormGroup;
          formGroup.controls['description'].setValue(app.name);
          formGroup.controls['sku'].setValue(app.billingId);
          formGroup.controls['amount'].setValue(((app.suggestedRetailPrice || 0) / 100).toFixed(2));
          this.updateItem(index);
          this.lineItemStoreService.refresh();
        }
      }),
    );
  }

  public bundleContainsItem(bundle: AppliedBundle, item: InvoiceItem): boolean {
    if (!bundle || !item) {
      return false;
    }

    if (!!bundle.bundleInstanceId && !!item.bundleInstanceId) {
      return bundle.bundleInstanceId === item.bundleInstanceId;
    }
    if (!bundle.bundleInstanceId && !item.bundleInstanceId) {
      return bundle.bundleId === item.bundleId;
    }
    return false;
  }

  protected readonly Frequency = Frequency;
  protected readonly BillingFrequency = BillingFrequency;
  protected readonly parseFloat = parseFloat;
  protected readonly convertDollarsToCents = convertDollarsToCents;
}
