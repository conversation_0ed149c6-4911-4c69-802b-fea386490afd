import { Component, computed, input, signal, TemplateRef, viewChild, inject, Inject, ViewChild } from '@angular/core';
import {
  CrmDrawerComponent,
  ObjectType,
  ObjectTypes,
  SNACKBAR_DURATION,
  TranslateForCrmObjectPipe,
  VerticalNavCardComponent,
  VerticalNavTabComponent,
  CrmInjectionToken,
  CrmDependencies,
} from '@galaxy/crm/static';
import { AsyncPipe, NgComponentOutlet } from '@angular/common';
import { MatAnchor, MatButton } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { Row } from '@vendasta/galaxy/table';
import {
  DeleteData,
  ListCustomFieldManagementTableComponent,
} from '../custom-field-management-table/custom-field-management-table.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { FormBuilder, FormControl, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';

import {
  ArchiveFieldSchemaRequestInterface,
  CRMCustomObjectTypeApiService,
  FieldType,
  ListCustomObjectTypesRequestInterface,
  CustomObjectTypeInterface,
} from '@vendasta/crm';
import { firstValueFrom, map, of, switchMap, take, Observable } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import {
  CustomFieldsManagementTableChangesService,
  FIELD_OPERATION_STATUS,
} from '../custom-fields-management-table-changes.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ExternalIdComponent } from '../external-id/external-id.component';
import { DasherizePipe } from '../dasherize.pipe';
import { isSystemField, sanitizeName } from '../utils';
import { RestrictedValuesComponent } from '../restricted-values/restricted-values.component';
import { toSignal } from '@angular/core/rxjs-interop';
import { ListCustomObjectTypesResponse } from '@vendasta/crm/lib/_internal/objects';
import { shareReplay } from 'rxjs/operators';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';

enum DeleteStatus {
  Skipped,
  Success,
  Failure,
}

interface ExtraNavTab {
  label: string;
  component: any;
  inputs: any;
}

interface fieldTypeOption {
  label: string;
  example?: string;
  fieldType: FieldType;
}

@Component({
  selector: 'crm-custom-fields-page',
  templateUrl: './custom-field-management-page.component.html',
  styleUrls: ['./custom-field-management-page.component.scss'],
  imports: [
    GalaxyPageModule,
    CrmDrawerComponent,
    TranslateModule,
    VerticalNavTabComponent,
    VerticalNavCardComponent,
    MatTabGroup,
    MatTab,
    ListCustomFieldManagementTableComponent,
    DasherizePipe,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    MatOption,
    ReactiveFormsModule,
    MatSelect,
    ExternalIdComponent,
    NgComponentOutlet,
    MatInput,
    MatAnchor,
    MatButton,
    MatCheckboxModule,
    RestrictedValuesComponent,
    AsyncPipe,
    TranslateForCrmObjectPipe,
    GalaxyStickyFooterModule,
  ],
  providers: [CustomFieldsManagementTableChangesService],
})
export class ListCustomFieldManagementPageComponent {
  drawer = viewChild.required<CrmDrawerComponent>('drawer');
  upsertTemplateRef = viewChild.required<TemplateRef<any>>('upsert');
  @ViewChild(VerticalNavTabComponent) verticalNavTab!: VerticalNavTabComponent;
  extraNavs = input<ExtraNavTab[]>([]);
  supportedCrmObjectTypes = input.required<ObjectType[]>();

  private readonly crmDependencies = inject(CrmInjectionToken);

  protected readonly customObjectTypeList = signal<CustomObjectTypeInterface[]>([]);

  // Convert the feature flag observable to a signal
  private readonly hasRestrictedFieldValuesFeatureFlag = toSignal(
    this.crmDependencies.hasRestrictedFieldValuesAdminFeatureFlag$ ?? of(false),
    { initialValue: false },
  );

  protected readonly hasCustomObjectFeatureFlag = toSignal(
    this.crmDependencies.hasCustomObjectFeatureFlag$ ?? of(false),
    { initialValue: false },
  );

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly snackService: SnackbarService,
    private readonly translateService: TranslateService,
    private readonly tableChangesService: CustomFieldsManagementTableChangesService,
    private readonly modalService: OpenConfirmationModalService,
    private readonly customObjectTypeService: CRMCustomObjectTypeApiService,
  ) {
    // Listen to field type changes and update the signal
    this.formGroup.get('fieldType')?.valueChanges.subscribe((fieldType) => {
      this.fieldType.set(fieldType);
    });

    this.loadCustomObjects();
  }

  private getCustomObjectTypes(): Observable<ListCustomObjectTypesResponse> {
    return this.config.namespace$.pipe(
      take(1),
      switchMap((namespace) => {
        return this.customObjectTypeService.listCustomObjectTypes({
          namespace: namespace,
        } as ListCustomObjectTypesRequestInterface);
      }),
      shareReplay(1),
    );
  }
  private loadCustomObjects() {
    this.getCustomObjectTypes()
      .pipe(take(1))
      .subscribe({
        next: (response) => {
          const customObjects: CustomObjectTypeInterface[] = response.customObjectTypes || [];

          this.customObjectTypeList.set(customObjects);
        },
        error: () => {
          this.customObjectTypeList.set([]);
        },
      });
  }

  protected readonly action = signal('');
  protected readonly editObjectId = signal('');
  protected readonly isSystemField = signal(false);
  private readonly externalIdPristine = signal(true);
  protected readonly fieldType = signal<FieldType>(FieldType.FIELD_TYPE_INVALID);

  protected readonly isEditingDisabled = computed(() => {
    return !!this.editObjectId() || this.isSystemField();
  });

  protected readonly formGroup = new FormBuilder().nonNullable.group({
    fieldName: ['', Validators.required],
    fieldType: [FieldType.FIELD_TYPE_INVALID, Validators.required],
    fieldDescription: '',
    objectType: ['Company', Validators.required],
    objectSubtype: [''],
    externalId: '',
    restrictedValues: [[] as string[]],
  });

  // Computed property to show/hide restricted values section
  protected readonly showRestrictedValues = computed(() => {
    const fieldType = this.fieldType();
    const isValidFieldType =
      fieldType === FieldType.FIELD_TYPE_STRING || fieldType === FieldType.FIELD_TYPE_STRING_LIST;
    const hasFeatureFlag = this.hasRestrictedFieldValuesFeatureFlag();

    // Only show for string and string list fields AND if the feature flag is enabled
    return isValidFieldType && hasFeatureFlag;
  });

  // Computed property to determine if system field can have restricted values edited
  protected readonly canEditSystemFieldRestrictedValues = computed(() => {
    return this.isSystemField() && this.showRestrictedValues();
  });

  // Computed property to determine if save button should be shown for system fields
  protected readonly showSaveButtonForSystemField = computed(() => {
    return this.isSystemField() && this.canEditSystemFieldRestrictedValues();
  });

  // Computed property to get objectType value
  protected readonly objectType = computed(() => {
    return this.formGroup.get('objectType')?.value as ObjectType;
  });

  // Computed property for field type validation
  protected readonly showFieldTypeError = computed(() => {
    const fieldTypeControl = this.formGroup.get('fieldType');
    return fieldTypeControl?.touched && fieldTypeControl?.hasError('required');
  });

  protected readonly fieldTypes: fieldTypeOption[] = [
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.STRING',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.STRING',
      fieldType: FieldType.FIELD_TYPE_STRING,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.STRING_LIST',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.STRING_LIST',
      fieldType: FieldType.FIELD_TYPE_STRING_LIST,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.INTEGER',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.INTEGER',
      fieldType: FieldType.FIELD_TYPE_INTEGER,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.FLOAT',
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.FLOAT',
      fieldType: FieldType.FIELD_TYPE_FLOAT,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.EMAIL',
      fieldType: FieldType.FIELD_TYPE_EMAIL,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.PHONE_NUMBER',
      fieldType: FieldType.FIELD_TYPE_PHONE,
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.DATE',
      fieldType: FieldType.FIELD_TYPE_DATE,
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.DATE',
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.DATE_TIME',
      fieldType: FieldType.FIELD_TYPE_DATETIME,
      example: 'CUSTOM_FIELDS.FIELD_EXAMPLES.DATE_TIME',
    },
    {
      label: 'CUSTOM_FIELDS.FIELD_TYPES.BOOLEAN',
      fieldType: FieldType.FIELD_TYPE_BOOLEAN,
    },
  ];

  // Computed property to filter field types based on create vs edit action
  protected readonly availableFieldTypes = computed(() => {
    const currentAction = this.action();

    // When creating new fields, exclude STRING_LIST
    if (currentAction === 'create') {
      return this.fieldTypes.filter((fieldType) => fieldType.fieldType !== FieldType.FIELD_TYPE_STRING_LIST);
    }

    // When editing, show all field types (including STRING_LIST for existing fields)
    return this.fieldTypes;
  });

  createDrawer() {
    //reset all fields except for Object type
    this.formGroup.controls.fieldName.reset();
    this.formGroup.controls.fieldDescription.reset();
    this.formGroup.controls.fieldType.reset();
    this.formGroup.controls.externalId.reset();
    this.formGroup.controls.restrictedValues.reset();

    const currentTabId = this.currentTabClicked();
    const isCustomObjectTab = this.customObjectTypeList().some((obj) => obj.customObjectTypeId === currentTabId);

    if (isCustomObjectTab) {
      this.formGroup.controls.objectType.setValue(currentTabId);
      this.formGroup.controls.objectSubtype.setValue(currentTabId);
    } else {
      this.formGroup.controls.objectType.setValue(currentTabId || 'Company');
      this.formGroup.controls.objectSubtype.setValue('');
    }

    // Reset signals
    this.fieldType.set(FieldType.FIELD_TYPE_INVALID);

    // Enable all controls
    this.formGroup.controls.fieldName.enable();
    this.formGroup.controls.fieldDescription.enable();
    this.formGroup.controls.fieldType.enable();
    this.formGroup.controls.externalId.enable();
    this.formGroup.controls.restrictedValues.enable();
    this.formGroup.controls.objectType.enable();

    this.action.set('create');
    this.editObjectId.set('');
    this.isSystemField.set(false);
    this.externalIdPristine.set(true);
    this.drawer().open(this.upsertTemplateRef(), this.translateService.instant('CUSTOM_FIELDS.CREATE'));
  }

  editDrawer(row: Row) {
    this.editObjectId.set(row.id);
    this.action.set('edit');

    const fieldType = this.readableTextToCustomFieldType(row.data['field_type']?.value);
    this.formGroup.controls.fieldType.setValue(fieldType);
    this.fieldType.set(fieldType);
    this.formGroup.controls.fieldDescription.setValue(row.data['description']?.value);
    this.formGroup.controls.fieldName.setValue(row.data['name']?.value);
    this.formGroup.controls.externalId.setValue(row.data['external_id']?.value);

    const currentTabId = this.currentTabClicked();
    const isCustomObjectTab = this.customObjectTypeList().some((obj) => obj.customObjectTypeId === currentTabId);

    if (isCustomObjectTab) {
      this.formGroup.controls.objectType.setValue(currentTabId);
      this.formGroup.controls.objectSubtype.setValue(currentTabId);
    } else {
      this.formGroup.controls.objectType.setValue(currentTabId || 'Company');
      this.formGroup.controls.objectSubtype.setValue('');
    }

    // Reset restricted values initially
    this.formGroup.controls.restrictedValues.setValue([]);

    // Load existing field customizations if this is a string or string list field
    if (fieldType === FieldType.FIELD_TYPE_STRING || fieldType === FieldType.FIELD_TYPE_STRING_LIST) {
      const actualObjectType = isCustomObjectTab
        ? ('CustomObject' as ObjectType)
        : ((currentTabId || 'Company') as ObjectType);
      this.tableChangesService
        .getFieldCustomization(actualObjectType, row.id)
        .pipe(take(1))
        .subscribe((customization) => {
          if (customization?.restrictedFieldValues?.length) {
            const restrictedValues = customization.restrictedFieldValues
              .map((value) => value.stringValue || '')
              .filter(Boolean);
            this.formGroup.controls.restrictedValues.setValue(restrictedValues);
          }
        });
    }

    this.formGroup.controls.objectType.disable();
    this.formGroup.controls.fieldType.disable();

    if (isSystemField(row.data['namespace']?.value)) {
      this.formGroup.controls.fieldName.disable();
      this.formGroup.controls.fieldDescription.disable();
      this.isSystemField.set(true);
    } else {
      this.isSystemField.set(false);
      // Enable 'fieldName' and 'fieldDescription' if namespace is not 'standard', 'system', or 'extension'
      this.formGroup.controls.fieldName.enable();
      this.formGroup.controls.fieldDescription.enable();
    }
    this.drawer().open(this.upsertTemplateRef(), this.translateService.instant('CUSTOM_FIELDS.EDIT'));
  }

  async confirmDelete(data: DeleteData) {
    const dialogResult$ = this.modalService
      .openModal({
        type: 'warn',
        title: 'CUSTOM_FIELDS.DELETE_MODAL.TITLE',
        message: 'CUSTOM_FIELDS.DELETE_MODAL.DESCRIPTION',
        confirmButtonText: 'ACTIONS.DELETE',
        hideCancel: false,
        cancelButtonText: 'ACTIONS.CANCEL',
        actionOnEnterKey: true,
        cancelOnEscapeKeyOrBackgroundClick: true,
        width: 560,
      })
      .pipe(
        switchMap((result) => {
          if (!result) {
            return of(DeleteStatus.Skipped);
          }
          return this.tableChangesService
            .deleteField({
              namespace: data.namespace,
              crmObjectType: data.objectType,
              fieldId: data.fieldId,
            } as ArchiveFieldSchemaRequestInterface)
            .pipe(
              map((deleteResult) => {
                return deleteResult === FIELD_OPERATION_STATUS.SUCCESS ? DeleteStatus.Success : DeleteStatus.Failure;
              }),
            );
        }),
      );

    const wasDeleted = await firstValueFrom(dialogResult$);
    switch (wasDeleted) {
      case DeleteStatus.Success:
        this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.DELETED', {
          duration: SNACKBAR_DURATION,
        });
        return;
      case DeleteStatus.Failure:
        this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_DELETE', {
          duration: SNACKBAR_DURATION,
        });
        return;
    }
    return;
  }

  createField() {
    this.formGroup.markAllAsTouched();
    if (this.formGroup.invalid) {
      this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.INVALID_FORM', { duration: SNACKBAR_DURATION });
      return;
    }
    const fieldType = this.formGroup.value.fieldType;
    const externalId = this.externalIdPristine()
      ? sanitizeName(this.formGroup.value.fieldName ?? '')
      : this.formGroup.value.externalId;

    const selectedObjectType = this.formGroup.value.objectType;
    const isCustomObject = this.customObjectTypeList().some((obj) => obj.customObjectTypeId === selectedObjectType);

    const objectType = isCustomObject ? ('CustomObject' as ObjectType) : (selectedObjectType as ObjectType);
    const objectSubtype = isCustomObject ? selectedObjectType : undefined;

    // Prepare field customization if restricted values exist
    const fieldCustomization = this.formGroup.value.restrictedValues?.length
      ? {
          namespace: '', // This will be set by the service
          fieldId: '', // This will be set by the service
          restrictedFieldValues: this.formGroup.value.restrictedValues.map((value) => ({
            stringValue: value,
          })),
        }
      : undefined;

    const fieldRequest = {
      fieldName: this.formGroup.value.fieldName,
      fieldType: fieldType,
      fieldDescription: this.formGroup.value.fieldDescription,
      externalId: externalId,
    };

    this.tableChangesService
      .createField(objectType, fieldRequest, fieldCustomization, objectSubtype)
      .pipe(take(1))
      .subscribe((result) => {
        switch (result) {
          case FIELD_OPERATION_STATUS.SUCCESS:
            this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.CREATED', {
              duration: SNACKBAR_DURATION,
            });
            break;
          case FIELD_OPERATION_STATUS.FIELD_CREATED_CUSTOMIZATION_FAILED:
            this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.CREATED', {
              duration: SNACKBAR_DURATION,
            });
            // Also show warning about restricted values
            setTimeout(() => {
              this.snackService.openErrorSnack('CUSTOM_FIELDS.WARNING.RESTRICTED_VALUES_SAVE_FAILED');
            }, 1000);
            break;
          case FIELD_OPERATION_STATUS.FIELD_CREATION_FAILED:
          default:
            this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_CREATE', {
              duration: SNACKBAR_DURATION,
            });
            break;
        }
        this.drawer().close();
      });
  }

  editField() {
    this.formGroup.controls.objectType.enable();
    this.formGroup.controls.fieldType.enable();
    if (this.formGroup.invalid) {
      this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.INVALID_FORM', { duration: SNACKBAR_DURATION });
      return;
    }

    const fieldType = this.formGroup.value.fieldType;

    const selectedObjectType = this.formGroup.value.objectType;
    const isCustomObject = this.customObjectTypeList().some((obj) => obj.customObjectTypeId === selectedObjectType);

    const objectType = isCustomObject ? ('CustomObject' as ObjectType) : (selectedObjectType as ObjectType);
    const objectSubtype = isCustomObject ? selectedObjectType : undefined;

    // Prepare field customization if restricted values are enabled
    const fieldCustomization = this.formGroup.value.restrictedValues?.length
      ? {
          fieldId: this.editObjectId(),
          namespace: '', // This will be set by the service
          restrictedFieldValues: this.formGroup.value.restrictedValues.map((value) => ({
            fieldId: this.editObjectId(),
            stringValue: value,
          })),
        }
      : undefined;

    const fieldRequest = {
      fieldId: this.editObjectId(),
      fieldName: this.formGroup.value.fieldName,
      fieldType: fieldType,
      fieldDescription: this.formGroup.value.fieldDescription,
      externalId: this.formGroup.value.externalId,
    };

    // For system fields, use the system field update method
    const updateObservable = this.isSystemField()
      ? this.tableChangesService.updateFieldCustomizationOnly(objectType, this.editObjectId(), fieldCustomization)
      : this.tableChangesService.updateField(
          objectType,
          fieldRequest,
          fieldCustomization,
          this.isSystemField(),
          objectSubtype,
        );

    updateObservable.pipe(take(1)).subscribe((result) => {
      switch (result) {
        case FIELD_OPERATION_STATUS.SUCCESS:
          this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.EDITED', {
            duration: SNACKBAR_DURATION,
          });
          break;
        case FIELD_OPERATION_STATUS.FIELD_UPDATED_CUSTOMIZATION_FAILED:
          this.snackService.openSuccessSnack('CUSTOM_FIELDS.SUCCESS.EDITED', {
            duration: SNACKBAR_DURATION,
          });
          // Also show warning about restricted values
          setTimeout(() => {
            this.snackService.openErrorSnack('CUSTOM_FIELDS.WARNING.RESTRICTED_VALUES_UPDATE_FAILED');
          }, 1000);
          break;
        case FIELD_OPERATION_STATUS.FIELD_UPDATE_FAILED:
        default:
          this.snackService.openErrorSnack('CUSTOM_FIELDS.ERROR.FAILED_TO_EDIT', {
            duration: SNACKBAR_DURATION,
          });
          break;
      }
      this.drawer().close();
    });
  }

  protected readonly currentTabClicked = signal('');
  protected readonly selectedTabId = signal<string | undefined>(undefined);

  private readonly selectedTabIndexes = new Map<string | undefined, number>();
  getSelectedTabIndex(customObjectId: string): number {
    return this.selectedTabIndexes.get(customObjectId) ?? 0;
  }

  onTabChange(customObjectId: string, selectedIndex: number): void {
    this.selectedTabIndexes.set(customObjectId, selectedIndex);
  }

  tabClicked(event: string) {
    if (ObjectTypes.includes(event as ObjectType)) {
      this.formGroup.controls['objectType'].setValue(event);
    }
    this.currentTabClicked.set(event);
    this.selectedTabId.set(event);
  }

  externalIdChanged(newExternalIdValue: string) {
    this.externalIdPristine.set(false);
    this.formGroup.controls.externalId.setValue(newExternalIdValue);
  }

  externalIdErrorsChanged(errors: ValidationErrors | null) {
    this.formGroup.controls.externalId.setErrors(errors);
  }

  readableTextToCustomFieldType(text: string): FieldType {
    switch (text) {
      case 'Date':
        return FieldType.FIELD_TYPE_DATE;
      case 'Integer':
        return FieldType.FIELD_TYPE_INTEGER;
      case 'String':
        return FieldType.FIELD_TYPE_STRING;
      case 'String List':
        return FieldType.FIELD_TYPE_STRING_LIST;
      case 'Geopoint':
        return FieldType.FIELD_TYPE_GEOPOINT;
      case 'Float':
        return FieldType.FIELD_TYPE_FLOAT;
      case 'Boolean':
        return FieldType.FIELD_TYPE_BOOLEAN;
      case 'Tag':
        return FieldType.FIELD_TYPE_TAG;
      case 'Phone number':
        return FieldType.FIELD_TYPE_PHONE;
      case 'Email':
        return FieldType.FIELD_TYPE_EMAIL;
      case 'Date time':
        return FieldType.FIELD_TYPE_DATETIME;
      default:
        return FieldType.FIELD_TYPE_INVALID;
    }
  }

  singularNameControls: { [key: string]: FormControl<string> } = {};
  pluralNameControls: { [key: string]: FormControl<string> } = {};

  getSingularNameControl(customObjectType: CustomObjectTypeInterface): FormControl<string> {
    if (!this.singularNameControls[customObjectType.customObjectTypeId!]) {
      this.singularNameControls[customObjectType.customObjectTypeId!] = new FormControl(
        customObjectType.singularObjectName!,
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      );
    }
    return this.singularNameControls[customObjectType.customObjectTypeId!];
  }

  getPluralNameControl(customObjectType: CustomObjectTypeInterface): FormControl<string> {
    if (!this.pluralNameControls[customObjectType.customObjectTypeId!]) {
      this.pluralNameControls[customObjectType.customObjectTypeId!] = new FormControl(
        customObjectType.pluralObjectName!,
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      );
    }
    return this.pluralNameControls[customObjectType.customObjectTypeId!];
  }

  createNewNavCard() {
    this.config.namespace$
      .pipe(
        take(1),
        switchMap((namespace) => {
          return this.customObjectTypeService.createCustomObjectType({
            namespace: namespace,
            customObjectType: {
              namespace: namespace,
              singularObjectName: 'NewObject',
              pluralObjectName: 'NewObjects',
            } as CustomObjectTypeInterface,
          });
        }),
      )
      .subscribe({
        next: (response) => {
          const newObject: CustomObjectTypeInterface = {
            customObjectTypeId: response.customObjectTypeId || '',
            singularObjectName: 'NewObject',
            pluralObjectName: 'NewObjects',
          };

          const currentObjects = this.customObjectTypeList();
          this.customObjectTypeList.set([...currentObjects, newObject]);

          this.selectedTabId.set(newObject.customObjectTypeId);
          this.selectedTabIndexes.set(newObject.customObjectTypeId, 1);
        },
        error: () => {
          this.snackService.openErrorSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.ERROR.FAILED_TO_CREATE');
        },
      });
  }

  saveCustomObjectType(customObjectType: CustomObjectTypeInterface): void {
    const singularControl = this.getSingularNameControl(customObjectType);
    const pluralControl = this.getPluralNameControl(customObjectType);

    if (singularControl.invalid || pluralControl.invalid) {
      this.snackService.openErrorSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.ERROR.INVALID_FORM');
      return;
    }

    const singularName = singularControl.value;
    const pluralName = pluralControl.value;

    this.config.namespace$
      .pipe(
        take(1),
        switchMap((namespace) => {
          return this.customObjectTypeService.updateCustomObjectType({
            namespace: namespace,
            customObjectType: {
              namespace: namespace,
              customObjectTypeId: customObjectType.customObjectTypeId,
              singularObjectName: singularName,
              pluralObjectName: pluralName,
            } as CustomObjectTypeInterface,
          });
        }),
      )
      .subscribe({
        next: () => {
          const currentObjects = this.customObjectTypeList();
          const updatedObjects = currentObjects.map((obj) => {
            if (obj.customObjectTypeId === customObjectType.customObjectTypeId) {
              return {
                ...obj,
                singularObjectName: singularName,
                pluralObjectName: pluralName,
              };
            }
            return obj;
          });

          this.customObjectTypeList.set(updatedObjects);
          this.snackService.openSuccessSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.SUCCESS.EDITED');

          this.selectedTabId.set(customObjectType.customObjectTypeId);
        },
        error: () => {
          this.snackService.openErrorSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.ERROR.FAILED_TO_EDIT');
        },
      });
  }

  deleteCustomObjectType(customObjectType: CustomObjectTypeInterface): void {
    const dialogResult$ = this.modalService
      .openModal({
        type: 'warn',
        title: this.translateService.instant('CUSTOM_FIELDS.CUSTOM_OBJECT.DELETE_MODAL.TITLE', {
          objectName: customObjectType.pluralObjectName,
        }),
        message: this.translateService.instant('CUSTOM_FIELDS.CUSTOM_OBJECT.DELETE_MODAL.MESSAGE', {
          objectName: customObjectType.singularObjectName,
        }),
        confirmButtonText: this.translateService.instant('CUSTOM_FIELDS.CUSTOM_OBJECT.DELETE_MODAL.CONFIRM', {
          objectName: customObjectType.pluralObjectName,
        }),
        hideCancel: false,
        cancelButtonText: 'ACTIONS.CANCEL',
        actionOnEnterKey: true,
        cancelOnEscapeKeyOrBackgroundClick: true,
        width: 560,
      })
      .pipe(
        switchMap((result) => {
          if (!result) {
            return of(false);
          }
          return this.config.namespace$.pipe(
            take(1),
            switchMap((namespace) => {
              return this.customObjectTypeService
                .deleteCustomObjectType({
                  namespace: namespace,
                  customObjectTypeId: customObjectType.customObjectTypeId,
                })
                .pipe(map(() => true));
            }),
          );
        }),
      );

    dialogResult$.subscribe({
      next: (wasDeleted) => {
        if (wasDeleted) {
          const currentObjects = this.customObjectTypeList();
          const deletedIndex = currentObjects.findIndex(
            (obj) => obj.customObjectTypeId === customObjectType.customObjectTypeId,
          );
          const filteredObjects = currentObjects.filter(
            (obj) => obj.customObjectTypeId !== customObjectType.customObjectTypeId,
          );

          this.selectedTabIndexes.delete(customObjectType.customObjectTypeId);
          let nextSelectedTabId: string | undefined;
          if (filteredObjects.length > 0) {
            if (deletedIndex > 0) {
              nextSelectedTabId = filteredObjects[deletedIndex - 1].customObjectTypeId;
            } else {
              nextSelectedTabId = filteredObjects[0].customObjectTypeId;
            }
          } else {
            nextSelectedTabId = 'Company';
          }

          this.customObjectTypeList.set(filteredObjects);
          this.selectedTabId.set(nextSelectedTabId);

          this.snackService.openSuccessSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.SUCCESS.DELETED');
        }
      },
      error: () => {
        this.snackService.openErrorSnack('CUSTOM_FIELDS.CUSTOM_OBJECT.ERROR.FAILED_TO_DELETE');
      },
    });
  }
}
