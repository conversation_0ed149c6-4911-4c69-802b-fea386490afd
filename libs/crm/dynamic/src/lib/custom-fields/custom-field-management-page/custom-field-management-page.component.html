<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        [attr.data-action]="'clicked-back-from-crm-field-management'"
        [useHistory]="true"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>{{ 'CUSTOM_FIELDS.CUSTOM_OBJECT.TITLE' | translate }}</glxy-page-title>
  </glxy-page-toolbar>
  <crm-drawer #drawer>
    <crm-vertical-nav-tab
      [useQueryParams]="false"
      [selectedTabId]="selectedTabId()"
      class="side-nav"
      (tabClicked)="tabClicked($event)"
    >
      @for (objectType of supportedCrmObjectTypes(); track objectType) {
        <crm-vertical-nav-card [label]="'TITLE' | translateForCrmObject: objectType | async" [tabId]="objectType">
          <div class="actions-container">
            <a class="action" mat-raised-button color="primary" (click)="createDrawer()">
              {{ 'CUSTOM_FIELDS.CREATE' | translate }}
            </a>
          </div>
          <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" animationDuration="0ms">
            <mat-tab label="{{ 'CUSTOM_FIELDS.LABELS.FIELDS' | translate }}">
              <crm-list-custom-fields
                [objType]="objectType"
                (editField)="editDrawer($event)"
                (deleteField)="confirmDelete($event)"
              ></crm-list-custom-fields>
            </mat-tab>
          </mat-tab-group>
        </crm-vertical-nav-card>
      }
      @for (nav of extraNavs(); track nav) {
        <crm-vertical-nav-card [label]="nav.label">
          @if ((nav.label | dasherize) === currentTabClicked()) {
            <ng-container *ngComponentOutlet="nav.component; inputs: nav.inputs" />
          }
        </crm-vertical-nav-card>
      }
      @if (hasCustomObjectFeatureFlag()) {
        @for (customObjectType of customObjectTypeList(); track customObjectType) {
          <crm-vertical-nav-card
            [label]="customObjectType.pluralObjectName"
            [tabId]="customObjectType.customObjectTypeId"
          >
            <div class="actions-container">
              <a class="action" mat-raised-button color="primary" (click)="createDrawer()">
                {{ 'CUSTOM_FIELDS.CREATE' | translate }}</a
              >
            </div>
            <mat-tab-group
              mat-stretch-tabs="false"
              mat-align-tabs="start"
              animationDuration="0ms"
              preserveContent="true"
              [selectedIndex]="getSelectedTabIndex(customObjectType.customObjectTypeId || '')"
              (selectedIndexChange)="onTabChange(customObjectType.customObjectTypeId || '', $event)"
            >
              <mat-tab label="{{ 'CUSTOM_FIELDS.LABELS.FIELDS' | translate }}">
                <crm-list-custom-fields
                  [objType]="'CustomObject'"
                  [objSubtype]="customObjectType.customObjectTypeId"
                  (editField)="editDrawer($event)"
                  (deleteField)="confirmDelete($event)"
                ></crm-list-custom-fields>
              </mat-tab>
              <mat-tab label="{{ 'CUSTOM_FIELDS.LABELS.CONFIGURATION' | translate }}">
                <div class="configuration-form">
                  <glxy-form-field bottomSpacing="small" [showLabel]="true">
                    <glxy-label>{{ 'CUSTOM_FIELDS.CUSTOM_OBJECT.SINGULAR_NAME' | translate }}</glxy-label>
                    <input matInput [formControl]="getSingularNameControl(customObjectType)" />
                  </glxy-form-field>
                  <glxy-form-field bottomSpacing="small" [showLabel]="true">
                    <glxy-label>{{ 'CUSTOM_FIELDS.CUSTOM_OBJECT.PLURAL_NAME' | translate }}</glxy-label>
                    <input matInput [formControl]="getPluralNameControl(customObjectType)" />
                  </glxy-form-field>
                </div>
                <div class="action-bar">
                  <glxy-sticky-footer>
                    <button mat-flat-button color="primary" (click)="saveCustomObjectType(customObjectType)">
                      {{ 'ACTIONS.SAVE' | translate }}
                    </button>
                    <button mat-stroked-button color="warn" (click)="deleteCustomObjectType(customObjectType)">
                      {{ 'CUSTOM_FIELDS.CUSTOM_OBJECT.DELETE_OBJECT' | translate }}
                    </button>
                  </glxy-sticky-footer>
                </div>
              </mat-tab>
            </mat-tab-group>
          </crm-vertical-nav-card>
        }
      }
      @if (hasCustomObjectFeatureFlag()) {
        <button type="button" class="add-nav-card-button" mat-flat-button (click)="createNewNavCard()">
          {{ 'CUSTOM_FIELDS.CUSTOM_OBJECT.ADD_CUSTOM_OBJECT' | translate }}
        </button>
      }
    </crm-vertical-nav-tab>
  </crm-drawer>
</glxy-page>

<ng-template #upsert>
  <div class="drawer-container">
    @if (isSystemField()) {
      <div class="alert">
        <glxy-alert type="tip">
          @if (canEditSystemFieldRestrictedValues()) {
            {{ 'CUSTOM_FIELDS.SYSTEM_FIELDS_RESTRICTED_VALUES_MESSAGE' | translate }}
          } @else {
            {{ 'CUSTOM_FIELDS.SYSTEM_FIELDS_MESSAGE' | translate }}
          }
        </glxy-alert>
      </div>
    }
    <form [formGroup]="formGroup" novalidate #form="ngForm" class="drawer">
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.OBJECT' | translate }}</glxy-label>
          <mat-select #defaultInput formControlName="objectType" [placeholder]="'CUSTOM_FIELDS.OBJECT' | translate">
            @for (objectType of supportedCrmObjectTypes(); track objectType) {
              <mat-option value="{{ objectType }}">{{
                'OBJECT_TYPE' | translateForCrmObject: objectType | async
              }}</mat-option>
            }
            @if (hasCustomObjectFeatureFlag()) {
              @if (customObjectTypeList().length > 0) {
                @for (customObjectType of customObjectTypeList(); track customObjectType.customObjectTypeId) {
                  <mat-option value="{{ customObjectType.customObjectTypeId }}">{{
                    customObjectType.singularObjectName
                  }}</mat-option>
                }
              }
            }
          </mat-select>
        </glxy-form-field>
      </glxy-form-row>
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_NAME' | translate }}</glxy-label>
          <input formControlName="fieldName" matInput placeholder="{{ 'CUSTOM_FIELDS.FIELD_NAME' | translate }}" />
          @if (formGroup.get('fieldName')?.touched && formGroup.get('fieldName')?.hasError('required')) {
            <glxy-error>
              {{ 'CUSTOM_FIELDS.ERROR.FIELD_NAME_REQUIRED' | translate }}
            </glxy-error>
          }
        </glxy-form-field>
      </glxy-form-row>
      <crm-external-id
        [fieldId]="editObjectId()"
        [externalId]="formGroup.value?.externalId ?? ''"
        [name]="formGroup.value?.fieldName ?? ''"
        [objectType]="objectType()"
        [createNewField]="action() === 'create'"
        [disabled]="isEditingDisabled()"
        (externalIdChange)="externalIdChanged($event)"
        (externalIdErrors)="externalIdErrorsChanged($event)"
      ></crm-external-id>
      <glxy-form-row>
        <glxy-form-field bottomSpacing="small" [showLabel]="true">
          <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_DESCRIPTION' | translate }}</glxy-label>
          <textarea
            formControlName="fieldDescription"
            matInput
            placeholder="{{ 'CUSTOM_FIELDS.FIELD_DESCRIPTION' | translate }}"
          ></textarea>
        </glxy-form-field>
      </glxy-form-row>

      <glxy-form-field bottomSpacing="small" [showLabel]="true" [required]="true">
        <glxy-label>{{ 'CUSTOM_FIELDS.FIELD_TYPE' | translate }}</glxy-label>
        <mat-select formControlName="fieldType" placeholder="{{ 'CUSTOM_FIELDS.FIELD_TYPE' | translate }}">
          @for (fieldType of availableFieldTypes(); track fieldType.fieldType) {
            @if (fieldType.label && fieldType.fieldType) {
              <mat-option [value]="fieldType.fieldType">
                {{ fieldType.label | translate }}
                @if (fieldType.example) {
                  ({{ fieldType.example | translate }})
                }
              </mat-option>
            }
          }
        </mat-select>
        @if (showFieldTypeError()) {
          <glxy-error>
            {{ 'CUSTOM_FIELDS.ERROR.FIELD_TYPE_REQUIRED' | translate }}
          </glxy-error>
        }
      </glxy-form-field>

      <!-- Restricted Values Section -->
      @if (showRestrictedValues()) {
        <glxy-form-row>
          <glxy-form-field bottomSpacing="small" [showLabel]="true">
            <glxy-label>{{ 'CUSTOM_FIELDS.RESTRICTED_VALUES.LABEL' | translate }}</glxy-label>
            <div class="restrict-description">
              {{ 'CUSTOM_FIELDS.RESTRICT_TO_SPECIFIC_VALUES_DESCRIPTION' | translate }}
            </div>
            <crm-restricted-values formControlName="restrictedValues" />
          </glxy-form-field>
        </glxy-form-row>
      }
    </form>
    @if (action() === 'create') {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="createField()">
          {{ 'ACTIONS.CREATE' | translate }}
        </button>
      </div>
    }
    @if (action() === 'edit' && !isSystemField()) {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="editField()">
          {{ 'ACTIONS.SAVE' | translate }}
        </button>
      </div>
    }
    @if (action() === 'edit' && showSaveButtonForSystemField()) {
      <div class="drawer-actions">
        <button mat-flat-button color="primary" type="button" (click)="editField()">
          {{ 'ACTIONS.SAVE' | translate }}
        </button>
      </div>
    }
  </div>
</ng-template>
