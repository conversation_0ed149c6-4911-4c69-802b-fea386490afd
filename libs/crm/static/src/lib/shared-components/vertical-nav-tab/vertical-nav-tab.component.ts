import { CommonModule, Location } from '@angular/common';
import { HttpParams } from '@angular/common/http';
import {
  AfterContentInit,
  Component,
  ContentChildren,
  EventEmitter,
  HostBinding,
  inject,
  Input,
  OnInit,
  Output,
  QueryList,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MatSelectModule } from '@angular/material/select';
import { ObserversModule } from '@angular/cdk/observers';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';

import { VerticalNavCardComponent } from './vertical-nav-card.component';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

@Component({
  selector: 'crm-vertical-nav-tab',
  templateUrl: './vertical-nav-tab.component.html',
  styleUrls: ['./vertical-nav-tab.component.scss'],
  imports: [CommonModule, ObserversModule, MatSelectModule, GalaxyFormFieldModule],
})
export class VerticalNavTabComponent implements OnInit, AfterContentInit, OnChanges {
  @HostBinding('class') class = 'vertical-nav-tab';

  @Input() useQueryParams = true;
  @Input() selectedTabId?: string;

  @ContentChildren(VerticalNavCardComponent) tabs?: QueryList<VerticalNavCardComponent>;

  @Output() tabClicked: EventEmitter<string> = new EventEmitter();

  @Input() tabActivated = false;

  private readonly location = inject(Location);
  private activatedRoute = inject(ActivatedRoute);
  isMobile = false;
  mobileSelectValue = '';

  constructor(private responsive: BreakpointObserver) {}

  ngOnInit() {
    this.responsive.observe([Breakpoints.Handset]).subscribe((result) => {
      this.isMobile = result.matches;
    });
  }

  ngAfterContentInit(): void {
    this.setActiveTab();
  }

  setActiveTab(): void {
    if (!this.tabs) return;

    let activeTabs: VerticalNavCardComponent[] = [];
    let queryParamHasMappedToATab = false;

    if (this.selectedTabId) {
      const targetTab = this.tabs.find((tab) => tab.id === this.selectedTabId);
      if (targetTab) {
        activeTabs = [targetTab];
        this.selectTab(targetTab);
        this.tabActivated = true;
        return;
      }
    }

    if (!this.tabActivated) {
      const activeTabParam = this.activatedRoute.snapshot.queryParams['tab'];
      activeTabs = this.tabs.filter((tab) => tab.active);

      if (activeTabParam && this.useQueryParams) {
        // check if tab id in query param, and set as first activeTabs if so
        this.tabs?.some((tab) => {
          if (tab.id === activeTabParam) {
            activeTabs = [];
            activeTabs.push(tab);
            queryParamHasMappedToATab = true;
            return true;
          }
          return false;
        });
      }

      if ((activeTabs && activeTabs.length === 0) || !activeTabs[0]) {
        // if no tabs marked active, either through manually setting
        // as active or via query param, make first tab active
        activeTabs = [];
        activeTabs.push(this.tabs.first);
      }

      if (!queryParamHasMappedToATab) {
        // If there is no active tab query param that is an id of an actual tab,
        // then set the url to include the correct tab in the query param
        if (activeTabs && activeTabs[0] && activeTabs[0].id) {
          this.addQueryString(activeTabs[0].id);
        }
      }

      // Select the active tab
      if (activeTabs && activeTabs[0]) {
        this.selectTab(activeTabs[0]);
        this.tabActivated = true;
        this.mobileSelectValue = activeTabs[0]?.id || '';
      }
    }
  }

  addQueryString(tabTitle: string): void {
    if (this.useQueryParams) {
      const params = new HttpParams().set('tab', tabTitle);
      this.location.replaceState(this.location.path().split('?')[0], params.toString(), false);
    }
  }

  selectTab(tab: VerticalNavCardComponent, scrolltarget?: HTMLElement): void {
    // deactivate all tabs
    this.tabs?.forEach((thisTab) => {
      thisTab.active = false;
    });

    // only scroll back to the top of the tab content if top of the content is not visible
    if (scrolltarget && scrolltarget?.getClientRects()[0]?.top < 0) {
      window.setTimeout(() => {
        scrolltarget.scrollIntoView({ behavior: 'instant' });
      }, 0);
    }

    // activate the tab the user has clicked on.
    tab.active = true;
    this.mobileSelectValue = tab?.id || '';

    this.tabClicked.emit(tab.id);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedTabId']) {
      this.setActiveTab();
    }
  }
}
