<div #scrollTarget class="scroll-target"></div>

<ul *ngIf="!isMobile" class="tabs-container">
  <ng-container *ngFor="let tab of tabs">
    <li
      #tabElement
      class="tab"
      (click)="selectTab(tab, scrollTarget); addQueryString(tab.id)"
      [class.active]="tab.active"
      (keydown.enter)="tabElement.click(); $event.preventDefault()"
      (keydown.space)="tabElement.click(); $event.preventDefault()"
      tabindex="0"
    >
      <span>{{ tab.label }}</span>
    </li>
  </ng-container>
  <li>
    <ng-content select=".add-nav-card-button"></ng-content>
  </li>
</ul>
<glxy-form-field class="tabs-mobile">
  <mat-select *ngIf="isMobile" [value]="mobileSelectValue">
    @for (tab of tabs; track tab) {
      <mat-option [value]="tab.id" (click)="selectTab(tab, scrollTarget); addQueryString(tab.id)">
        {{ tab.label }}
      </mat-option>
    }
  </mat-select>
</glxy-form-field>
<div class="tab-cards" (cdkObserveContent)="setActiveTab()">
  <ng-content></ng-content>
  <div *ngIf="isMobile">
    <ng-content select=".add-nav-card-button"></ng-content>
  </div>
</div>
