import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { FormControl, Validators } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { FieldValue } from '@vendasta/crm';
import { StandardIds } from '../../shared-services';
import { CrmFieldOptionsService } from '../../shared-services';
import { ObjectType } from '../../tokens-and-interfaces';

export interface CloseLostOpportunityData {
  reason: FieldValue;
  reasonDescription?: FieldValue;
}

@Component({
  selector: 'crm-opportunity-close-lost',
  templateUrl: './opportunity-close-lost.component.html',
  styles: [],
  imports: [
    CommonModule,
    MatButtonModule,
    TranslateModule,
    GalaxyFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatSelectModule,
  ],
  standalone: true,
})
export class CrmOpportunityCloseLostComponent {
  protected closedLostReason = new FormControl<string>('', { nonNullable: true, validators: Validators.required });
  protected closedLostReasonDescription = new FormControl<string>('');

  private readonly dialogRef = inject(MatDialogRef<CrmOpportunityCloseLostComponent>);
  private readonly fieldOptionsService = inject(CrmFieldOptionsService);

  protected readonly closedLostReasonOptions$ = this.fieldOptionsService.getFieldOptions(
    StandardIds.OpportunityClosedLostReason,
    '',
    'Opportunity' as ObjectType,
  );

  protected closeDialog() {
    const closedLostOpportunityData: CloseLostOpportunityData = {
      reason: new FieldValue({
        stringValue: this.closedLostReason.value,
        fieldId: StandardIds.OpportunityClosedLostReason,
      }),
    };
    if (this.closedLostReasonDescription.value) {
      closedLostOpportunityData.reasonDescription = new FieldValue({
        stringValue: this.closedLostReasonDescription.value,
        fieldId: StandardIds.OpportunityClosedLostReasonDescription,
      });
    }
    this.dialogRef.close(closedLostOpportunityData);
  }
}
