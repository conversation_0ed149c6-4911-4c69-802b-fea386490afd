import { Inject, Injectable, OnDestroy, Optional, inject } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { TranslateService } from '@ngx-translate/core';
import {
  ActivityInterface,
  CrmObjectInterface,
  FieldSchemaInterface,
  FieldType,
  FieldValueInterface,
  FilterOperator,
  ListCrmObjectsRequestInterface,
  ListCrmObjectsResponseInterface,
  ObjectProjectionFilterInterface,
  PagedResponseMetadataInterface,
  PhoneFieldsInterface,
  FieldValue,
} from '@vendasta/crm';
import { GalaxyFilterInterface, GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  CellData,
  CustomCell,
  DateData,
  GalaxyDataSource,
  PagedListRequestInterface,
  PagedResponseInterface,
  PhoneData,
  Row,
  TextData,
} from '@vendasta/galaxy/table';
import { GalaxyColumnDef } from '@vendasta/galaxy/table/src/table.interface';
import {
  animationFrameScheduler,
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  firstValueFrom,
  map,
  Observable,
  observeOn,
  of,
  ReplaySubject,
  shareReplay,
  switchMap,
  take,
  tap,
  withLatestFrom,
  startWith,
} from 'rxjs';
import { CrmFormService } from '../../shared-services/crm-services/crm-form.service';
import { CrmObjectService } from '../../shared-services/crm-services/crm-object-service/crm-object.service';
import { CrmTableStateService } from '../../shared-services/crm-services/crm-object-service/crm-table-state.service';
import {
  ActivityType,
  CrmDependencies,
  CrmInjectionToken,
  CrmObjectDependencies,
  CrmObjectInjectionToken,
  ObjectType,
  TaskTableCustomizationServiceToken,
} from '../../tokens-and-interfaces';
import { CRMTrackingService } from '../../shared-services/page-analytics.service';
import { CrmObjectManagementTableChangesService } from '../../shared-services/crm-object-management-table-changes.service';
import {
  ActivityTableCustomizationService,
  ColumnDataInterface,
  CompanyTableCustomizationService,
  ContactTableCustomizationService,
  OpportunityTableCustomizationService,
  StandardIds,
  SystemFieldIds,
  TableCustomizationServiceInterface,
  Pipeline,
} from '../../shared-services';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { format, ParsedNumber } from 'libphonenumber-js';
import { CrmBoardColumnInterface, CrmBoardDatasource, CrmBoardDatasourceInterface } from '@galaxy/crm/components/board';
import {
  CrmOpportunityCloseLostComponent,
  CloseLostOpportunityData,
} from '../opportunity/opportunity-close-lost.component';
import { MatDialog } from '@angular/material/dialog';
import { OpportunityStatus } from '../../constants';

interface ColumnDef extends GalaxyColumnDef {
  external_id?: string;
}

@Injectable()
export class ListObjectsTableService implements OnDestroy {
  // use ReplaySubject, this should only emit when explicitly set / actually have values to be used by the component
  private objectType$$ = new ReplaySubject<ObjectType>();
  private objectSubtype$ = this.crmObjectDependencies?.objectSubtype$ ?? of('');
  private fieldSchemas$$ = new ReplaySubject<FieldSchemaInterface[]>();
  private columns$$ = new ReplaySubject<GalaxyColumnDef[]>();
  private groupConfigs$$ = new ReplaySubject<GalaxyColumnDef[]>();
  private activityType$$ = new BehaviorSubject<ActivityType | ''>('');
  private objectChanges$ = toObservable(this.crmObjectManagementTableChangesService.getCrmObjectChanges());
  private refreshedRows$$ = new BehaviorSubject<Map<string, Row>>(new Map());
  private initialLoadingCompleted$$ = new BehaviorSubject<boolean>(true);
  private receivedObject$$ = new BehaviorSubject(false);
  private baseColumnIds$$ = new BehaviorSubject<string[]>([]);
  private showActions$$ = new BehaviorSubject<boolean>(true);
  private data$$ = new BehaviorSubject<CrmObjectInterface[]>([]);

  dataSource: GalaxyDataSource<Row, GalaxyFilterInterface, MatSort>;
  boardSource: CrmBoardDatasourceInterface<Row, GalaxyFilterInterface, MatSort>;
  initialLoadingCompleted$: Observable<boolean> = this.initialLoadingCompleted$$.asObservable();
  columns$: Observable<GalaxyColumnDef[]> = this.columns$$.asObservable();
  groupConfigs$: Observable<GalaxyColumnDef[]> = this.groupConfigs$$.asObservable();

  showEmptyState$: Observable<boolean> = combineLatest([this.initialLoadingCompleted$$, this.receivedObject$$]).pipe(
    map(([loading, hasObjects]) => !loading && !hasObjects),
  );
  showObjects$: Observable<boolean> = combineLatest([this.initialLoadingCompleted$, this.showEmptyState$]).pipe(
    map(([loading, showEmptyState]) => !loading && !showEmptyState),
  );

  private latestRequestPayload$$ = new ReplaySubject<PagedListRequestInterface<GalaxyFilterInterface, MatSort>>();
  // this reference has to be shared or else we get blinking behaviour on the table
  // when new observables are returned each time
  private latestResponse$: Observable<ListCrmObjectsResponseInterface> = this.latestRequestPayload$$.pipe(
    observeOn(animationFrameScheduler),
    withLatestFrom(this.objectType$$, this.activityType$$, this.objectSubtype$),
    tap(([, objectType, activityType, objectSubtype]) => {
      this.refreshedRows$$.next(new Map());
      this.crmObjectManagementTableChangesService.clearAllChanges(objectType, activityType || objectSubtype);
    }),
    switchMap(([req]) => this.fetchObjects(req)),
    withLatestFrom(this.latestRequestPayload$$),
    tap(([resp]) => {
      const receivedObject = this.receivedObject$$.value;
      if (!receivedObject && (resp?.crmObjects || []).length > 0) {
        this.receivedObject$$.next(true);
      }
    }),
    tap(async ([, req]) => {
      if (req.searchOptions?.text) {
        const objectType = await firstValueFrom(this.objectType$$);
        this.trackingService.trackEvent(objectType, 'searched-crm-object', {
          searchFields: req.searchOptions.fields,
        });
      }
    }),
    tap(([resp]) => this.data$$.next(resp.crmObjects || [])),
    map(([resp]) => resp),
  );

  private readonly dialog = inject(MatDialog);

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly objectsService: CrmObjectService,
    private readonly fieldsService: CrmFormService,
    private readonly translate: TranslateService,
    private readonly trackingService: CRMTrackingService,
    private readonly contactCustomization: ContactTableCustomizationService,
    private readonly companyCustomization: CompanyTableCustomizationService,
    private readonly opportunityCustomization: OpportunityTableCustomizationService,
    private readonly tableStateService: CrmTableStateService,
    private readonly snackService: SnackbarService,
    private readonly crmFormService: CrmFormService,
    private readonly crmObjectManagementTableChangesService: CrmObjectManagementTableChangesService,
    @Optional()
    @Inject(TaskTableCustomizationServiceToken)
    private readonly taskCustomization?: ActivityTableCustomizationService,
    @Optional()
    @Inject(CrmObjectInjectionToken)
    private readonly crmObjectDependencies?: CrmObjectDependencies,
  ) {
    const schemas$: Observable<FieldSchemaInterface[]> = combineLatest([
      this.config.namespace$,
      this.objectType$$,
      this.objectSubtype$,
    ]).pipe(
      observeOn(animationFrameScheduler),
      switchMap(([, objectType, objectSubtype]) => this.fieldsService.listAllObjectFields$(objectType, objectSubtype)),
      distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
      shareReplay(1),
    );
    schemas$.pipe(takeUntilDestroyed()).subscribe((schemas) => {
      this.fieldSchemas$$.next(schemas);
    });
    const columnGroups$ = this.fieldSchemas$$.pipe(
      map((schemas: FieldSchemaInterface[]) => schemas.map((schema) => this.fieldSchemaToTableColumn(schema))),
      switchMap((columns: GalaxyColumnDef[]) =>
        combineLatest([
          this.applyColumnLayout$(columns, this.objectSubtype$),
          this.objectType$$,
          this.activityType$$.pipe(startWith('' as ActivityType)),
        ]),
      ),
      map(([columns, objectType, activityType]) => this.customizedColumns(activityType || objectType, columns, false)),
      map((columns) => this.addStaticColumns(columns)),
      switchMap((columns) => {
        return this.showBaseColumns(columns);
      }),
      switchMap((columns) => {
        return this.tableStateService.initializeLocalStorageColumns(columns);
      }),
      switchMap((columns) => this.addColumnGroups(columns, this.objectSubtype$)),
      distinctUntilChanged((a, b) => JSON.stringify(a.columns) === JSON.stringify(b.columns)),
      shareReplay(1),
    );
    columnGroups$.pipe(takeUntilDestroyed()).subscribe(({ columns, groupConfigs }) => {
      this.columns$$.next(columns);
      this.groupConfigs$$.next(groupConfigs);
    });
    this.config.namespace$
      .pipe(
        takeUntilDestroyed(),
        distinctUntilChanged(),
        tap(() => this.initialLoadingCompleted$$.next(true)),
      )
      .subscribe();

    const paginatedAPI = {
      get: (
        req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
      ): Observable<PagedResponseInterface<Row>> => {
        const results = this.listObjects(req).pipe(
          catchError(() => {
            this.snackService.openErrorSnack('ERRORS.GENERIC_MESSAGE');
            return of({
              crmObjects: [],
              pagingMetadata: {
                nextCursor: '',
                hasMore: false,
              },
            }) as Observable<ListCrmObjectsResponseInterface>;
          }),
        );

        return combineLatest([results, this.objectChanges$, this.refreshedRows$$]).pipe(
          withLatestFrom(this.fieldSchemas$$, this.objectType$$, this.activityType$$, this.objectSubtype$),
          map(([[results, changes, refreshedRows], schemas, objectType, activityType, objectSubtype]) => {
            const deleted = changes.deleted;
            const updated = changes.updated;
            const created = changes.created;

            const type = activityType || objectSubtype || objectType;
            const objects = [...(created.get(type) || [])];
            objects.push(...(results?.crmObjects || []));

            const filteredObjects = objects.filter((object) => !deleted.get(type)?.includes(object.crmObjectId ?? ''));
            updated.get(type)?.forEach((updatedObject) => {
              const index = filteredObjects.findIndex((object) => object.crmObjectId === updatedObject.crmObjectId);
              if (index > -1) {
                filteredObjects[index] = updatedObject;
              }
            });

            const rows = this.transformToPagedResponse(
              filteredObjects || [],
              schemas,
              objectType,
              activityType,
              results?.pagingMetadata,
            );
            rows.data.map((r) => refreshedRows.get(r.id) || r);
            return rows;
          }),
          tap(() => {
            this.initialLoadingCompleted$$.next(false);
          }),
        );
      },
    };
    this.dataSource = new GalaxyDataSource<Row, GalaxyFilterInterface, MatSort>(paginatedAPI);

    const boardAPI = {
      get: (
        req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
      ): Observable<PagedResponseInterface<Row>> => {
        return this.fetchObjects(req).pipe(
          catchError(() => {
            this.snackService.openErrorSnack('ERRORS.GENERIC_MESSAGE');
            return of({} as ListCrmObjectsResponseInterface);
          }),
          withLatestFrom(this.fieldSchemas$$, this.objectType$$, this.activityType$$),
          map(([objectListResp, schemas, objectType, activityType]) =>
            this.transformToPagedResponse(
              objectListResp?.crmObjects || [],
              schemas,
              objectType,
              activityType,
              objectListResp?.pagingMetadata,
            ),
          ),
        );
      },
    };
    this.boardSource = new CrmBoardDatasource<Row, GalaxyFilterInterface, MatSort>(boardAPI);
  }

  applyColumnLayout$(columns: ColumnDef[], objectSubtype$?: Observable<string>): Observable<ColumnDef[]> {
    return combineLatest([this.objectType$$, objectSubtype$ ?? of('')]).pipe(
      switchMap(([objectType, objectSubtype]) =>
        this.fieldsService.listAllObjectFieldGroups$(objectType, objectSubtype),
      ),
      map((fieldGroups) => {
        const fieldIds: string[] = fieldGroups.reduce(
          (r: string[], fieldGroup) => r.concat(fieldGroup.fieldIds ?? []),
          [],
        );

        const sortedColumns: ColumnDef[] = JSON.parse(JSON.stringify(columns));
        sortedColumns.sort((a, b) => {
          let indexA = fieldIds.indexOf(a.id);
          if (indexA === -1 && a.external_id) {
            indexA = fieldIds.indexOf(a.external_id);
          }
          let indexB = fieldIds.indexOf(b.id);
          if (indexB === -1 && b.external_id) {
            indexB = fieldIds.indexOf(b.external_id);
          }
          if (indexA === -1) {
            return 1;
          }
          if (indexB === -1) {
            return -1;
          }
          return indexA - indexB;
        });

        const firstFieldGroupFieldIds = fieldGroups[0]?.fieldIds ?? [];
        for (const column of sortedColumns) {
          if (firstFieldGroupFieldIds.includes(column.id)) {
            continue;
          }
          column.hidden = true;
        }

        return sortedColumns;
      }),
    );
  }

  addStaticColumns(columns: GalaxyColumnDef[]): GalaxyColumnDef[] {
    if (!this.showActions$$.value) {
      return columns;
    }
    const actionsColumn: GalaxyColumnDef = {
      id: 'actions',
      title: this.translate.instant('LIST_OBJECTS_TABLE.ACTIONS_COLUMN.TITLE'),
      pinned: true,
      stickyEnd: true,
      sortable: false,
    };
    return [...columns, actionsColumn];
  }

  addColumnGroups(
    columns: GalaxyColumnDef[],
    objectSubtype$?: Observable<string>,
  ): Observable<{ columns: GalaxyColumnDef[]; groupConfigs: GalaxyColumnDef[] }> {
    const subtype$ = objectSubtype$ ?? of('');
    return combineLatest([this.objectType$$, subtype$]).pipe(
      switchMap(([objectType, objectSubtype]) => {
        return this.crmFormService.listAllObjectFieldGroups$(objectType, objectSubtype);
      }),
      map((groups) => {
        return groups
          .map((group) => {
            return {
              id: this.translate.instant(group.fieldGroupId ?? 'Custom'),
              title: this.translate.instant(group.description ?? 'Custom fields'),
              columns: (group.fieldIds || []).map((fieldId) => {
                return columns.find((column) => {
                  return column.id === fieldId;
                });
              }),
            } as GalaxyColumnDef;
          })
          .filter((g) => g !== undefined);
      }),
      map((groups) => {
        return {
          columns: columns,
          groupConfigs: groups,
        };
      }),
    );
  }

  private moveItemsToBeginning(columns: GalaxyColumnDef[], identifiersToMove: string[]): GalaxyColumnDef[] {
    const movedToFront: GalaxyColumnDef[] = [];

    for (const id of identifiersToMove) {
      const col = columns.find((c) => c.id === id);
      if (col) {
        movedToFront.push(col);
      }
    }
    const movedToBack = columns.filter((c) => !movedToFront.find((m) => m.id === c.id));
    return [...movedToFront, ...movedToBack];
  }

  showBaseColumns(columns: GalaxyColumnDef[]): Observable<GalaxyColumnDef[]> {
    return this.baseColumnIds$$.pipe(
      map((baseColumnIds) => {
        for (const col of baseColumnIds) {
          const index = columns.findIndex((c) => c.id === col);
          if (index > -1) {
            columns[index].hidden = false;
          }
        }
        return this.moveItemsToBeginning(columns, baseColumnIds);
      }),
    );
  }

  deleteObject(namespace: string, objectId: string): Observable<unknown> {
    return combineLatest([this.objectType$$, this.activityType$$, this.objectSubtype$]).pipe(
      switchMap(([objectType, activityType, objectSubtype]) =>
        this.objectsService
          .deleteCrmObject(namespace, objectType, objectId)
          .pipe(map(() => [objectType, activityType, objectSubtype] as [ObjectType, ActivityType | '', string])),
      ),
      tap(([objectType, activityType, objectSubtype]) => {
        this.crmObjectManagementTableChangesService.addDeletedCrmObjectId(
          objectType,
          objectId,
          activityType || objectSubtype,
        );
      }),
      take(1),
    );
  }

  async refreshRows(rows: string[]): Promise<void> {
    try {
      const objectType = await firstValueFrom(this.objectType$$);
      const objects = (await firstValueFrom(this.objectsService.getMultiObject(objectType, rows)))?.crmObjects;
      if (!objects?.length) {
        return;
      }

      const newRefreshedRows = await firstValueFrom(
        combineLatest([this.fieldSchemas$$, this.objectType$$, this.activityType$$]).pipe(
          map(([schemas, objectType, activityType]) =>
            this.crmObjectsToTableRows(objects, schemas, objectType, activityType),
          ),
        ),
      );
      const currentRefreshedRows = await firstValueFrom(this.refreshedRows$$);
      for (const row of newRefreshedRows) {
        currentRefreshedRows.set(row.id, row);
      }
      this.refreshedRows$$.next(currentRefreshedRows);
    } catch (e) {
      console.error('Error refreshing rows', e);
    }
  }

  setObjectType(objectType: ObjectType): void {
    this.objectType$$.next(objectType);
  }

  setActivityType(activityType: ActivityType): void {
    this.activityType$$.next(activityType);
  }

  setBaseColumns(baseColumnIds: string[]): void {
    this.baseColumnIds$$.next(baseColumnIds);
  }

  setShowActions(showActions: boolean): void {
    this.showActions$$.next(showActions);
  }

  private listObjects(
    req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
  ): Observable<ListCrmObjectsResponseInterface> {
    this.latestRequestPayload$$.next(req);
    return this.latestResponse$;
  }

  private getTableCustomizationService(
    objectType: ObjectType | ActivityType,
  ): TableCustomizationServiceInterface | undefined {
    if (this.crmObjectDependencies) {
      return this.crmObjectDependencies.services?.tableCustomizationService ?? undefined;
    }
    switch (objectType) {
      case 'Contact':
        return this.contactCustomization;
      case 'Company':
        return this.companyCustomization;
      case 'Task':
        return this.taskCustomization;
      case 'Opportunity':
        return this.opportunityCustomization;
    }
    return undefined;
  }

  private getActivityTypeFilter(activityType: ActivityType) {
    return {
      fieldId: SystemFieldIds.ActivityType,
      operator: FilterOperator.FILTER_OPERATOR_IS,
      values: [
        {
          string: activityType,
        },
      ],
    };
  }

  private fieldSchemaToTableColumn(schema: FieldSchemaInterface): ColumnDef {
    return {
      id: schema.fieldId || '',
      external_id: schema.externalId || '',
      title: schema.fieldName,
      sortable: true,
    };
  }

  private crmObjectsToTableRows(
    objects: CrmObjectInterface[],
    schemas: FieldSchemaInterface[],
    objectType: ObjectType,
    activityType: ActivityType | '',
  ): Row[] {
    const schemaMap: { [schemaId: string]: FieldSchemaInterface } = {};
    schemas.forEach((schema) => {
      if (!schema?.fieldId) {
        return;
      }
      schemaMap[schema.fieldId] = schema;
    });

    const objType = activityType || objectType;
    return (
      objects?.map((obj) => {
        const columnData: ColumnDataInterface = {};
        obj?.fields?.forEach((field) => {
          if (!field?.fieldId) {
            return;
          }
          columnData[field.fieldId] = this.getValueByType(field, schemaMap[field.fieldId]);
        });
        this.customizeRowData(objType, obj, schemaMap, columnData);

        return {
          id: obj?.crmObjectId || '',
          data: columnData,
        };
      }) || []
    );
  }

  private getValueByType(field: FieldValueInterface, schema: FieldSchemaInterface): CellData {
    switch (schema?.fieldType) {
      case FieldType.FIELD_TYPE_CURRENCY:
        return {
          cellType: 'currency',
          value: field.currencyValue?.value || 0,
          code: field.currencyValue?.currencyCode,
        };
      case FieldType.FIELD_TYPE_INTEGER:
        return {
          cellType: 'integer',
          value: field.integerValue,
        };
      case FieldType.FIELD_TYPE_FLOAT:
        return {
          cellType: 'float',
          value: field.floatValue,
        };
      case FieldType.FIELD_TYPE_DATETIME:
      case FieldType.FIELD_TYPE_DATE:
        return this.dateData(field.dateValue);
      case FieldType.FIELD_TYPE_PHONE:
        if (field.phoneFieldValues) {
          return this.phoneData(field.phoneFieldValues);
        }
        return this.phoneData(field.stringValue);
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_EMAIL:
        return this.textData(field.stringValue);
      case FieldType.FIELD_TYPE_DROPDOWN:
        return this.dropDownOptionData(schema, field.stringValue);
      case FieldType.FIELD_TYPE_BOOLEAN:
        return {
          cellType: 'boolean',
          value: field.booleanValue,
        };
      case FieldType.FIELD_TYPE_TAG:
      case FieldType.FIELD_TYPE_STRING_LIST:
        return {
          cellType: 'tags',
          value: field?.stringValues?.values || [],
        };
      default:
        return undefined;
    }
  }

  private dropDownOptionData(schema: FieldSchemaInterface, value?: string): TextData {
    const label = schema?.dropdownOptions?.find((o) => o?.value === value)?.label;
    return this.textData(label || value);
  }

  private textData(value?: string, link?: string): TextData {
    return {
      cellType: 'text',
      value: value || '',
      link: link || '',
    };
  }

  private phoneData(value?: string | PhoneFieldsInterface): PhoneData {
    if (typeof value === 'string') {
      return {
        cellType: 'phone',
        value: value,
      };
    }
    //format phone number
    if (value?.nationalNumber === '') {
      return {
        cellType: 'phone',
        value: '',
      };
    }
    if (!value?.e164Compliant) {
      return {
        cellType: 'phone',
        value: value?.nationalNumber,
      };
    }
    const parsed = {
      country: value?.isoCountryCode,
      phone: value?.nationalNumber,
      ext: value?.extension,
    } as ParsedNumber;

    let internationalNumber = value?.nationalNumber;
    try {
      internationalNumber = format(parsed, 'INTERNATIONAL');
    } catch (e) {
      console.warn('Failed formatting phone number', parsed, e);
    }

    return {
      cellType: 'phone',
      value: internationalNumber,
    };
  }

  private dateData(value?: Date): DateData {
    return {
      cellType: 'date',
      value: value,
    };
  }

  private customizedColumns(
    objectType: ObjectType | ActivityType,
    columns: ColumnDef[],
    isMobile: boolean,
  ): GalaxyColumnDef[] {
    return this.getTableCustomizationService(objectType)?.customizeColumns(columns, isMobile) || [];
  }

  private async addNamespaceMetadata(crmObject: ActivityInterface, columnData: ColumnDataInterface): Promise<void> {
    const namespaceFromConfig = await firstValueFrom(this.config.namespace$);
    columnData['namespace'] = {
      cellType: 'custom',
      // this field is just used as metadata, nothing loads for a cell component
      value: crmObject?.namespace ?? namespaceFromConfig,
    } as CustomCell;
  }

  private async customizeRowData(
    objectType: ObjectType | ActivityType,
    obj: CrmObjectInterface,
    schemaMap: { [schemaId: string]: FieldSchemaInterface },
    columnData: ColumnDataInterface,
  ) {
    await this.addNamespaceMetadata(obj, columnData);
    this.getTableCustomizationService(objectType)?.customizeRowData(obj, schemaMap, columnData);
  }

  lookupRowDisplayName(id: string): Observable<string> {
    return this.data$$.pipe(
      map((rows) => {
        const row = rows.find((r) => r.crmObjectId == id);
        if (!row) {
          return id;
        }
        const firstName = row?.fields?.find((f) => f.externalId == 'standard__first_name')?.stringValue;
        const lastName = row?.fields?.find((f) => f.externalId == 'standard__last_name')?.stringValue;
        if (firstName || lastName) {
          return `${firstName} ${lastName}`.trim();
        }
        const email = row?.fields?.find((f) => f.externalId == 'standard__email')?.stringValue;
        if (email) {
          return email;
        }
        const phone = row?.fields?.find((f) => f.externalId == 'standard__phone_number')?.stringValue;
        if (phone) {
          return phone;
        }
        return id;
      }),
    );
  }

  ngOnDestroy(): void {
    this.crmObjectManagementTableChangesService.clearAllChanges();
  }

  private buildRequest(
    req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
  ): Observable<ListCrmObjectsRequestInterface> {
    return combineLatest([this.objectType$$, this.activityType$$, this.objectSubtype$]).pipe(
      map(([objectType, activityType, objectSubtype]) => {
        const filterGroup = this.objectsService.convertFilters([...(req.filters || [])]);
        if (filterGroup && objectType === 'Activity') {
          if (activityType !== '') {
            filterGroup.filters?.push(this.getActivityTypeFilter(activityType));
          }
        }

        let sortBy = this.objectsService.convertSortBy(req.sorting);
        if (!sortBy?.length) {
          const objType = activityType || objectType;
          sortBy = this.getTableCustomizationService(objType)?.defaultSortingOptions;
        }
        const listCrmObjectReq = {
          search: {
            searchTerm: req?.searchOptions?.text || '',
          },
          filtersV2: filterGroup,
          pagingOptions: {
            cursor: req?.pagingOptions?.cursor,
            pageSize: req?.pagingOptions?.pageSize,
          },
          sortBy: sortBy ?? [],
          projectionFilters: [
            { filterType: 1, fromFieldType: FieldType.FIELD_TYPE_PHONE, toFieldType: FieldType.FIELD_TYPE_PHONE },
          ] as ObjectProjectionFilterInterface[],
          crmObjectSubtype: objectSubtype,
        } as ListCrmObjectsRequestInterface;

        return listCrmObjectReq;
      }),
    );
  }

  private fetchObjects(
    req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
  ): Observable<ListCrmObjectsResponseInterface> {
    const crmRequest = this.buildRequest(req);
    return combineLatest([this.objectType$$, crmRequest]).pipe(
      switchMap(([objectType, request]) => this.objectsService.listObjects(objectType, request)),
    );
  }

  private transformToPagedResponse(
    crmObjects: CrmObjectInterface[],
    schemas: FieldSchemaInterface[],
    objectType: ObjectType,
    activityType: ActivityType | '',
    metadata?: PagedResponseMetadataInterface,
  ): PagedResponseInterface<Row> {
    const rows = this.crmObjectsToTableRows(crmObjects, schemas, objectType, activityType);
    return {
      data: rows,
      pagingMetadata: {
        nextCursor: metadata?.nextCursor || '',
        hasMore: metadata?.hasMore || false,
        totalResults: metadata?.totalResults || 0,
      },
    } as PagedResponseInterface<Row>;
  }

  defaultBoardColumns(
    objectType: ObjectType,
    activityType?: ActivityType,
    pipeline?: Pipeline,
  ): CrmBoardColumnInterface<GalaxyFilterInterface>[] {
    const lifecycleStages: string[] = [
      'Visitor',
      'Lead',
      'Marketing qualified lead',
      'Sales accepted lead',
      'Sales qualified lead',
      'Prospect',
      'Customer',
      'Former customer',
    ];

    const taskStages: string[] = ['Open', 'Completed'];

    if (objectType === 'Contact') {
      return lifecycleStages.map(
        (stage, index) =>
          ({
            id: index.toString(),
            name: stage,
            filters: [
              {
                fieldId: StandardIds.ContactLifecycleStage,
                operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
                values: [{ string: stage }],
              },
            ],
            mutation: (object: Row) =>
              firstValueFrom(
                this.objectsService.updateCrmObject(objectType, {
                  crmObjectId: object.id,
                  fields: [{ fieldId: StandardIds.ContactLifecycleStage, stringValue: stage }],
                }),
              ),
          }) as CrmBoardColumnInterface<GalaxyFilterInterface>,
      );
    }
    if (objectType === 'Company') {
      return lifecycleStages.map(
        (stage, index) =>
          ({
            id: index.toString(),
            name: stage,
            filters: [
              {
                fieldId: StandardIds.CompanyLifecycleStage,
                operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
                values: [{ string: stage }],
              },
            ],
            mutation: (object: Row) =>
              firstValueFrom(
                this.objectsService.updateCrmObject(objectType, {
                  crmObjectId: object.id,
                  fields: [{ fieldId: StandardIds.CompanyLifecycleStage, stringValue: stage }],
                }),
              ),
          }) as CrmBoardColumnInterface<GalaxyFilterInterface>,
      );
    }
    if (objectType === 'Opportunity') {
      return (
        pipeline?.stages?.map(
          (stage) =>
            ({
              id: stage.id,
              name: stage.name,
              probability: stage.mutations.find((mutation) => mutation.fieldId === StandardIds.OpportunityProbability)
                ?.floatValue,
              filters: stage.filters,
              mutation: async (object: Row) => {
                let mutations = stage.mutations.map((m) => new FieldValue(m));
                let closeLostData: CloseLostOpportunityData | undefined;

                if (this.isOnStage(mutations, OpportunityStatus.ClosedLost)) {
                  closeLostData = await firstValueFrom(
                    this.dialog
                      .open(CrmOpportunityCloseLostComponent, {
                        minWidth: '360px',
                        width: '600px',
                      })
                      .afterClosed(),
                  );
                  if (!closeLostData) {
                    return Promise.reject(new Error('Did not disclose a reason for closing the opportunity'));
                  }
                }
                mutations = this.mutateCloseLostReasons(mutations, closeLostData);
                return firstValueFrom(
                  this.objectsService.updateCrmObject(objectType, {
                    crmObjectId: object.id,
                    fields: mutations,
                  }),
                );
              },
            }) as CrmBoardColumnInterface<GalaxyFilterInterface>,
        ) ?? []
      );
    }

    if (objectType === 'Activity' && activityType === 'Task') {
      return taskStages.map(
        (stage, index) =>
          ({
            id: index.toString(),
            name: stage,
            filters: [
              {
                fieldId: StandardIds.ActivityTaskStatus,
                operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
                values: [{ string: stage }],
              },
            ],
            mutation: (object: Row) =>
              firstValueFrom(
                this.objectsService.updateCrmObject(objectType, {
                  crmObjectId: object.id,
                  fields: [{ fieldId: StandardIds.ActivityTaskStatus, stringValue: stage }],
                }),
              ),
          }) as CrmBoardColumnInterface<GalaxyFilterInterface>,
      );
    }
    return [];
  }

  private mutateCloseLostReasons(mutations: FieldValue[], closeLostData?: CloseLostOpportunityData): FieldValue[] {
    const closeLostReason =
      closeLostData?.reason || new FieldValue({ fieldId: StandardIds.OpportunityClosedLostReason, stringValue: '' });
    const closeLostReasonDescription =
      closeLostData?.reasonDescription ||
      new FieldValue({ fieldId: StandardIds.OpportunityClosedLostReasonDescription, stringValue: '' });

    return [...mutations, closeLostReason, closeLostReasonDescription];
  }

  private isOnStage(fields: FieldValue[], status: OpportunityStatus): boolean {
    return fields.find((m) => m.fieldId === StandardIds.OpportunityStatus && m.stringValue === status) !== undefined;
  }
}
