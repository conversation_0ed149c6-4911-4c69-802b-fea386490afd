<mat-card appearance="outlined" class="mat-card--no-content-padding">
  <mat-card-header class="sales-order-card-header">
    <mat-card-title>
      <span>{{ 'FRONTEND.SALES_UI.ORDERS' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-button *ngIf="showCreateButton" color="primary" (click)="create()">
      {{ 'FRONTEND.SALES_UI.CREATE_ORDER' | translate }}
    </button>
  </mat-card-header>
  <ng-container *ngIf="loading">
    <mat-card-content>
      <div class="loading">
        <mat-spinner [diameter]="25" [strokeWidth]="3"></mat-spinner>
      </div>
    </mat-card-content>
  </ng-container>
  <ng-container *ngIf="!error; else errorTemplate">
    <ng-container *ngIf="response$ | async as response">
      <ng-container *ngIf="response.salesOrders.length > 0; else noneFound">
        <mat-card-content>
          <mat-list *ngIf="actionRequiredForFO$ | async as actionRequired">
            <mat-list-item
              *ngFor="let uiOrder of response.salesOrders"
              [ngClass]="{ 'extended-item': actionRequired.get(uiOrder.salesOrder.orderId) }"
            >
              <div matListItemLine class="order-container">
                <div class="order-item">
                  <div class="order-info">
                    <a
                      [routerLink]="uiOrder.orderDetailsUrl$ | async"
                      [attr.data-action]="
                        actionRequired.get(uiOrder.salesOrder.orderId) ? 'sales-order-id-title-clicked' : ''
                      "
                    >
                      {{ uiOrder.salesOrder.orderId }}
                    </a>
                    <p>
                      {{
                        'FRONTEND.SALES_UI.CONTRACT_START_DATE' | translate: { startDate: uiOrder.requestedActivation }
                      }}
                    </p>
                  </div>
                  <glxy-badge class="status" [color]="uiOrder.badge.color">
                    {{ uiOrder.badge.text | translate }}
                  </glxy-badge>
                </div>
                <glxy-alert
                  *ngIf="actionRequired.get(uiOrder.salesOrder.orderId)"
                  [type]="'warning'"
                  [showAction]="true"
                  [actionRouterLink]="uiOrder.fulfillmentFormUrl$ | async"
                  actionTitle="{{ 'FRONTEND.SALES_UI.FULFILLMENT_BANNER_BUTTON' | translate }}"
                  class="fulfillment-order-alert"
                >
                  <span class="warning-span">{{ 'FRONTEND.SALES_UI.FULFILLMENT_BANNER_TEXT' | translate }}</span>
                </glxy-alert>
              </div>
            </mat-list-item>
          </mat-list>
        </mat-card-content>
        <mat-card-actions align="end">
          <a
            mat-button
            color="primary"
            [routerLink]="ordersPageForBusinessUrl"
            [queryParams]="ordersPageForBusinessQuery"
          >
            {{ 'FRONTEND.SALES_UI.VIEW_ALL' | translate }}
          </a>
        </mat-card-actions>
      </ng-container>
    </ng-container>
  </ng-container>
</mat-card>

<ng-template #noneFound>
  <mat-card-content>
    <glxy-empty-state [size]="'small'" style="padding: 16px">
      <glxy-empty-state-hero>
        <mat-icon>assignment</mat-icon>
      </glxy-empty-state-hero>
      <p>{{ 'FRONTEND.SALES_UI.NO_ORDERS' | translate }}</p>
    </glxy-empty-state>
  </mat-card-content>
</ng-template>

<ng-template #errorTemplate>
  <mat-card-content>
    <glxy-empty-state [size]="'small'">
      <glxy-empty-state-hero>
        <mat-icon>error</mat-icon>
      </glxy-empty-state-hero>
      <p>{{ 'FRONTEND.SALES_UI.ERROR_RETRIEVING_ORDERS' | translate }}</p>
    </glxy-empty-state>
  </mat-card-content>
</ng-template>
