<div class="content" [formGroup]="form">
  <glxy-form-field>
    <glxy-label>
      {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.TITLE' | translate }}
    </glxy-label>
    <mat-select formControlName="sendChannel">
      <mat-option value="both">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.BOTH' | translate }}
      </mat-option>
      <mat-option value="email">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.EMAIL' | translate }}
      </mat-option>
      <mat-option value="sms">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.SMS' | translate }}
      </mat-option>
      <mat-option value="email-fallback-sms">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.EMAIL_FALLBACK_SMS' | translate }}
      </mat-option>
      <mat-option value="sms-fallback-email">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.SMS_FALLBACK_EMAIL' | translate }}
      </mat-option>
    </mat-select>
    <glxy-label-hint>
      {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_METHOD.HINT' | translate }}
    </glxy-label-hint>
  </glxy-form-field>
  <mat-divider></mat-divider>
  <glxy-form-field class="top-padding" bottomSpacing="none">
    <glxy-label>{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SCHEDULE_TITLE' | translate }}</glxy-label>
    <mat-radio-group formControlName="sendType">
      <mat-radio-button value="use-default">
        {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_TYPE.USE_DEFAULT' | translate }}
        <mat-icon
          [glxyTooltip]="'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.USE_DEFAULT_DESCRIPTION' | translate"
          [highContrast]="false"
          class="info-icon"
        >
          info_outline
        </mat-icon>
      </mat-radio-button>
      <mat-radio-button value="send-now"
        >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_TYPE.SEND_IMMEDIATELY' | translate }}
      </mat-radio-button>
      <mat-radio-button value="send-custom"
        >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_TYPE.SEND_CUSTOM' | translate }}
      </mat-radio-button>
    </mat-radio-group>
  </glxy-form-field>
  @if (form.controls.sendType.value === 'send-custom') {
    <div formGroupName="sendCustomForm" class="subform bottom-padding">
      <glxy-form-field bottomSpacing="none" class="small-field start" [showLabel]="false">
        <input formControlName="number" type="number" min="1" />
      </glxy-form-field>
      <glxy-form-field bottomSpacing="none" class="medium-field" [showLabel]="false">
        <mat-select formControlName="unit">
          <mat-option value="hours"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.HOURS' | translate }}
          </mat-option>
          <mat-option value="days"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.DAYS' | translate }}
          </mat-option>
          <mat-option value="weeks"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.WEEKS' | translate }}
          </mat-option>
          <mat-option value="months"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.MONTHS' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.AND_SEND_BETWEEN' | translate }}</span>
      <automata-time-range-slider
        [form]="form.controls.sendCustomForm.controls.timeRange"
        [startHour]="timeRangeStartHour"
        [endHour]="timeRangeEndHour"
        bottomSpacing="none"
      ></automata-time-range-slider>
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.ON' | translate }}</span>
      <automata-weekday-selector
        [form]="form.controls.sendCustomForm"
        [sleepWeekdays]="weekdays"
        bottomSpacing="none"
        [showLabel]="false"
        class="weekday-selector"
      ></automata-weekday-selector>
    </div>
  }
  <mat-divider></mat-divider>
  <glxy-form-field class="top-padding" bottomSpacing="none">
    <mat-checkbox formControlName="sendAgain">
      <span class="section-title">{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_AGAIN' | translate }}</span>
      <extended>{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.SEND_AGAIN_DESCRIPTION' | translate }}</extended>
    </mat-checkbox>
  </glxy-form-field>
  @if (form.controls.sendAgain.value) {
    <div formGroupName="sendAgainForm" class="subform bottom-padding">
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.EVERY' | translate }}</span>
      <glxy-form-field bottomSpacing="none" class="small-field" [showLabel]="false">
        <input formControlName="number" type="number" min="1" />
      </glxy-form-field>
      <glxy-form-field bottomSpacing="none" class="medium-field" [showLabel]="false">
        <mat-select formControlName="unit">
          <mat-option value="hours"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.HOURS' | translate }}
          </mat-option>
          <mat-option value="days"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.DAYS' | translate }}
          </mat-option>
          <mat-option value="weeks"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.WEEKS' | translate }}
          </mat-option>
          <mat-option value="months"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIME_UNIT.MONTHS' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.AFTER_INITIAL_REQUEST' | translate }}</span>
      <glxy-form-field bottomSpacing="none" class="medium-field-dropdown" [showLabel]="false">
        <mat-select formControlName="repeat">
          <mat-option [value]="1">1</mat-option>
          <mat-option [value]="2">2</mat-option>
          <mat-option [value]="3">3</mat-option>
        </mat-select>
      </glxy-form-field>
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TIMES' | translate }}</span>
    </div>
  }
  <mat-divider></mat-divider>
  <div class="top-padding">
    <div class="section-title bottom-space">
      {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.REPEAT_CUSTOMERS' | translate }}
    </div>
    <div class="subform">
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.WAIT_AT_LEAST' | translate }}</span>
      <glxy-form-field bottomSpacing="none" class="medium-field" [showLabel]="false">
        <mat-select formControlName="repeatWait">
          <mat-option value="30days"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.REPEAT_WAIT.30_DAYS' | translate }}
          </mat-option>
          <mat-option value="60days"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.REPEAT_WAIT.60_DAYS' | translate }}
          </mat-option>
          <mat-option value="90days"
            >{{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.REPEAT_WAIT.90_DAYS' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
      <span> {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.BEFORE_ASKING' | translate }}</span>
    </div>
  </div>
</div>
